import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import UnoCSS from 'unocss/vite'
import { presetIcons } from '@unocss/preset-icons'
import presetUno from '@unocss/preset-uno'
import { presetAttributify } from '@unocss/preset-attributify'
import fs from 'node:fs'
import path from 'node:path'

// 直接加载json文件而不是使用import
const loadIconsJson = (collection) => {
  const jsonPath = path.resolve('./node_modules/@iconify-json/', collection, 'icons.json')
  return JSON.parse(fs.readFileSync(jsonPath, 'utf-8'))
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  console.log(env, 'env') // ✅ 可打印出环境变量，如 VITE_APP_BUILD_PATH

  return {
    base: env.VITE_APP_BUILD_PATH || '/',
    esbuild: mode === 'production' ? {
      drop: ['console', 'debugger'],
    } : {},
    plugins: [
      vue(),
      UnoCSS({
        presets: [
          presetUno(),
          presetAttributify(),
          presetIcons({
            scale: 1.2,
            warn: true,
            prefix: 'i-',
            collections: {},
            extraProperties: {
              'display': 'inline-block',
              'vertical-align': 'middle'
            }
          })
        ]
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      port: 5173,
      open: true,
      cors: true,
      proxy: {
        '/api': {
          target: 'http://************:8888',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
          secure: false,
        },
        '/upload': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
        }
      }
    }
  }
})
