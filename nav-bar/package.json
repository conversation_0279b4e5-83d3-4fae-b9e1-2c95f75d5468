{"name": "nav-bar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@hatamiarash7/vue-flip-clock": "^1.0.13", "@iconify-json/fa": "^1.2.1", "@iconify-json/material-symbols": "^1.2.18", "@iconify-json/mdi": "^1.2.3", "@iconify/json": "^2.2.325", "@tsparticles/engine": "^3.8.1", "@tsparticles/vue3": "^3.0.1", "@vueuse/core": "^13.1.0", "animate-css-grid": "^1.5.1", "ant-design-vue": "^4.2.6", "axios": "^1.8.4", "dayjs": "^1.11.13", "gsap": "^3.12.7", "lunisolar": "^2.5.2", "mitt": "^3.0.1", "pinia": "^3.0.1", "sass": "^1.86.3", "sortablejs": "^1.15.0", "tsparticles": "^2.12.0", "tsparticles-engine": "^2.12.0", "vue": "^3.5.13", "vue-baidu-calendar": "^1.0.8", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@iconify-json/carbon": "^1.2.8", "@unocss/preset-attributify": "^66.1.0-beta.10", "@unocss/preset-icons": "^66.1.0-beta.10", "@vitejs/plugin-vue": "^5.2.1", "unocss": "^66.1.0-beta.10", "vite": "^6.2.0"}}