<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/office/sideBar.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="LinkFun是一个现代化的应用导航系统，用于组织和快速访问您喜爱的应用和网站。提供优雅的界面设计和丰富的自定义选项。" />
    <title>LinkFun妙趣导航 | 摸鱼导航 | 你的个人专属网址收藏夹</title>
    
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
  <script>
  var _hmt = _hmt || [];
  (function() {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?5120b4f215d44d5c6865c06bf5ce33ca";
    var s = document.getElementsByTagName("script")[0]; 
    s.parentNode.insertBefore(hm, s);
  })();

  // 初始页面标题设置（页面加载时的后备方案）
  // 注意：动态标题更新已在 App.vue 中通过 watch 监听 navigationStore.currentDataSource 实现
  // 此处仅作为 Vue 应用启动前的初始设置
  const currentTitle = localStorage.getItem('currentDataSource');
  const title = document.querySelector('title');

  if(currentTitle === 'entertainment') {
    title.textContent = 'LinkFun妙趣导航 | 摸鱼导航 | 你的个人专属网址收藏夹';
  } else if(currentTitle === 'office' || currentTitle === 'pure') {
    title.textContent = 'LinkFun妙趣导航 | 办公导航 | AI导航 | 你的个人专属网址收藏夹';
  }
  
  </script>
</html>
