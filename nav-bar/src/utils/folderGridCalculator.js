/**
 * 文件夹网格布局计算器
 * 根据容器的实际高宽动态计算最多能显示几个项目
 */

export class FolderGridCalculator {
  constructor() {
    this.config = {
      // 默认配置
      minItemSize: 24,      // 最小项目尺寸 (px)
      maxItemSize: 36,      // 最大项目尺寸 (px)
      gap: 6,               // 网格间隙 (px)
      padding: 0,           // 容器内边距 (px)
      aspectRatio: 1,       // 项目宽高比 (1:1)
      columns: 3,           // 固定3列
      allowedRows: [1, 3],  // 允许1行或3行
    }
  }

  /**
   * 计算最优网格布局
   * @param {number} containerWidth - 容器宽度
   * @param {number} containerHeight - 容器高度
   * @param {number} itemCount - 项目数量
   * @param {object} folderSize - 文件夹尺寸 {w: number, h: number}
   * @param {object} options - 可选配置
   * @returns {object} 布局计算结果
   */
  calculateOptimalLayout(containerWidth, containerHeight, itemCount, folderSize = { w: 2, h: 2 }, options = {}) {
    // 合并配置
    const config = { ...this.config, ...options }

    // 可用空间（减去内边距）
    const availableWidth = containerWidth - (config.padding * 2)
    const availableHeight = containerHeight - (config.padding * 2)

    // 根据文件夹尺寸决定列数和行数
    let cols, rows


    // 特殊处理3x1布局：显示4个应用并居中
    if (folderSize.w === 3 && folderSize.h === 1) {
      cols = 4  // 3x1文件夹使用4列显示4个应用
      rows = 1
    } else {
      // 其他布局保持原有逻辑
      cols = config.columns
      // 根据文件夹高度决定行数：h=1时用1行，h=2时用3行
      if (folderSize.h === 1) {
        rows = 1  // 2x1文件夹使用1行
      } else {
        rows = 3  // 2x2文件夹使用3行
      }
    }

    // 如果没有项目，返回对应尺寸的默认布局
    if (itemCount === 0) {
      return this.createLayoutResult(cols, rows, config.minItemSize, config, true, folderSize)
    }

    // 计算在此网格配置下的项目尺寸
    const layout = this.calculateItemSize(availableWidth, availableHeight, cols, rows, config, folderSize)

    // 如果计算的布局无效，使用默认配置
    if (!layout.isValid) {
      return this.createLayoutResult(cols, rows, config.minItemSize, config, true, folderSize)
    }

    return layout
  }

  /**
   * 计算指定网格配置下的项目尺寸
   */
  calculateItemSize(availableWidth, availableHeight, cols, rows, config, folderSize = { w: 2, h: 2 }) {
    // 计算基于宽度的项目尺寸
    const totalGapWidth = (cols - 1) * config.gap
    const itemWidthByWidth = (availableWidth - totalGapWidth) / cols

    // 计算基于高度的项目尺寸
    const totalGapHeight = (rows - 1) * config.gap
    const itemHeightByHeight = (availableHeight - totalGapHeight) / rows

    // 取较小值以确保项目能完全显示
    const itemSize = Math.min(itemWidthByWidth, itemHeightByHeight)

    // 检查尺寸是否在合理范围内
    const isValid = itemSize >= config.minItemSize && itemSize <= config.maxItemSize

    return this.createLayoutResult(cols, rows, itemSize, config, isValid, folderSize)
  }

  /**
   * 创建布局结果对象
   */
  createLayoutResult(cols, rows, itemSize, config, isValid = true, folderSize = { w: 2, h: 2 }) {
    const clampedItemSize = Math.max(config.minItemSize, Math.min(config.maxItemSize, itemSize))

    // 根据文件夹尺寸动态设置width和height比例
    let widthPercent, heightPercent

    if (folderSize.w === 2 && folderSize.h === 1) {
      // 2x1和3x1文件夹使用适中比例
      widthPercent = '85%'
      heightPercent = '66%'
    } else if(folderSize.w === 3 && folderSize.h === 1) {
      widthPercent = '73%'
      heightPercent = '66%'
    } else {
      // 2x2文件夹使用较大比例
      widthPercent = '85%'
      heightPercent = '85%'
    }

    const maxItems = cols * rows


    return {
      columns: cols,
      rows: rows,
      itemSize: Math.round(clampedItemSize),
      gap: config.gap,
      maxItems: maxItems,
      gridWidth: cols * clampedItemSize + (cols - 1) * config.gap,
      gridHeight: rows * clampedItemSize + (rows - 1) * config.gap,
      isValid,
      // CSS 样式
      gridStyle: {
        'grid-template-columns': `repeat(${cols}, ${Math.round(clampedItemSize)}px)`,
        'grid-template-rows': `repeat(${rows}, ${Math.round(clampedItemSize)}px)`,
        'gap': `${config.gap}px`,
        'width': widthPercent,
        'height': heightPercent,
      }
    }
  }

  /**
   * 计算布局评分（简化版，因为现在只有两种固定布局）
   */
  calculateLayoutScore(layout, itemCount, maxItems, config) {
    let score = 0

    // 项目尺寸评分 (尺寸越大越好，但不超过最大值)
    const sizeRatio = layout.itemSize / config.maxItemSize
    score += sizeRatio * 50

    // 空间利用率评分 (实际项目数 / 最大可容纳项目数)
    const utilizationRatio = itemCount / maxItems
    score += utilizationRatio * 50

    return score
  }

  /**
   * 获取显示的项目列表（根据最大显示数量截取）
   */
  getDisplayItems(items, maxItems) {
    if (!Array.isArray(items)) return []
    return items.slice(0, maxItems)
  }
}

// 创建单例实例
export const folderGridCalculator = new FolderGridCalculator()

// 默认导出
export default folderGridCalculator