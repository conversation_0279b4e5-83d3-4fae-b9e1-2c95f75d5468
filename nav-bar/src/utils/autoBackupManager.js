import { backup, getBackupList } from '@/api/navbar'
import { message } from 'ant-design-vue'

/**
 * 自动备份管理器
 * 负责管理用户登录后的自动备份和还原功能
 */
class AutoBackupManager {
  constructor() {
    this.backupTimer = null
    this.BACKUP_DELAY = 10000 //  10秒延迟
    this.isBackingUp = false
    this.isRestoring = false
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean} 用户登录状态
   */
  isUserLoggedIn() {
    const token = localStorage.getItem('token')
    return !!token
  }

  /**
   * 收集需要备份的数据
   * @returns {Object} 备份数据对象
   */
  collectBackupData() {
    const backupKeys = [
      'categoryApps_entertainment', 
      'homeDockApps', 
      'categoryApps_office', 
      'officeDockApps',
    ]
    
    const layoutData = {}
    
    backupKeys.forEach(key => {
      const data = localStorage.getItem(key)
      if (data) {
        try {
          // 验证是否为有效JSON并存储
          layoutData[key] = JSON.parse(data)
        } catch (error) {
          console.warn(`自动备份: ${key} 不是有效的JSON格式:`, error)
          // 如果不是JSON格式，直接存储原始字符串
          layoutData[key] = data
        }
      }
    })
    
    return layoutData
  }

  /**
   * 执行实际的备份操作
   * @returns {Promise<boolean>} 备份是否成功
   */
  async performBackup() {
    if (!this.isUserLoggedIn()) {
      console.log('自动备份: 用户未登录，跳过备份')
      return false
    }

    if (this.isBackingUp) {
      console.log('自动备份: 正在备份中，跳过本次备份')
      return false
    }

    try {
      this.isBackingUp = true
      
      const layoutData = this.collectBackupData()
      const allLayoutInfo = JSON.stringify(layoutData)
      
      console.log('自动备份: 开始备份数据', layoutData)
      
      const response = await backup(allLayoutInfo)
      
      if (response.status === 200) {
        console.log('自动备份: 备份成功')
        return true
      } else {
        console.warn('自动备份: 备份失败，服务器响应:', response)
        return false
      }
    } catch (error) {
      console.error('自动备份: 备份过程中发生错误:', error)
      return false
    } finally {
      this.isBackingUp = false
    }
  }

  /**
   * 触发自动备份（带防抖）
   * 如果10秒内再次调用，会重置计时器
   */
  triggerAutoBackup() {
    if (!this.isUserLoggedIn()) {
      return
    }

    // 清除之前的定时器
    if (this.backupTimer) {
      clearTimeout(this.backupTimer)
    }

    // 设置新的定时器
    this.backupTimer = setTimeout(async () => {
      const success = await this.performBackup()
      
      if (success) {
        // 静默成功，不显示提示以免干扰用户
        console.log('自动备份: 备份完成')
      } else {
        // 失败也静默处理，避免频繁弹窗
        console.warn('自动备份: 备份失败')
      }
      
      this.backupTimer = null
    }, this.BACKUP_DELAY)

    console.log('自动备份: 已设置10秒延迟备份')
  }

  /**
   * 自动还原备份
   * 在用户登录后调用，使用getBackupList获取备份列表并还原第一个备份
   * @returns {Promise<boolean>} 还原是否成功
   */
  async autoRestore() {
    if (!this.isUserLoggedIn()) {
      console.log('自动还原: 用户未登录，跳过还原')
      return false
    }

    if (this.isRestoring) {
      console.log('自动还原: 正在还原中，跳过本次还原')
      return false
    }

    try {
      this.isRestoring = true
      
      console.log('自动还原: 开始获取备份列表并还原第一个备份')

      const response = await getBackupList()

      if (response.status === 200) {
        
        if(response.data && response.data.length === 0) {
          this.performBackup()
          return
        }
        // 使用第一个备份数据进行还原
        const firstBackup = response.data[0]
        console.log(`自动还原: 找到${response.data.length}个备份，使用第一个备份进行还原`)

        // 解析备份数据
        const backupData = typeof firstBackup.data === 'string'
          ? JSON.parse(firstBackup.data)
          : firstBackup.data

        console.log('自动还原: 恢复数据:', backupData)

        // 恢复指定的四个localStorage项
        const restoreKeys = [
            'categoryApps_entertainment', 
            'homeDockApps', 
            'categoryApps_office', 
            'officeDockApps',
        ]

        let restoredCount = 0
        restoreKeys.forEach(key => {
          console.log(backupData[key],'backupData[key]')
          if (backupData[key] !== undefined) {
            try {
              // 如果备份数据是对象，转换为JSON字符串存储
              const dataToStore = typeof backupData[key] === 'object'
                ? JSON.stringify(backupData[key])
                : backupData[key]

              localStorage.setItem(key, dataToStore)
              console.log(`自动还原: 已恢复 ${key}`)
              restoredCount++
            } catch (error) {
              console.error(`自动还原: 恢复 ${key} 时发生错误:`, error)
            }
          } else {
            console.warn(`自动还原: 备份数据中未找到 ${key}`)
          }
        })

        if (restoredCount > 0) {
          message.success('已自动还原您的布局设置')
          
          // 触发页面刷新以应用还原的数据
          setTimeout(() => {
            window.location.reload()
          }, 1000)
          
          return true
        } else {
          console.warn('自动还原: 没有可还原的数据')
          return false
        }
      } else {
        console.warn('自动还原: 服务器返回数据异常:', response)
        return false
      }
    } catch (error) {
      console.error('自动还原: 还原过程中发生错误:', error)
      return false
    } finally {
      this.isRestoring = false
    }
  }

  /**
   * 清理定时器
   * 在组件卸载时调用
   */
  cleanup() {
    if (this.backupTimer) {
      clearTimeout(this.backupTimer)
      this.backupTimer = null
      console.log('自动备份: 已清理备份定时器')
    }
  }

  /**
   * 手动取消当前的备份计时
   */
  cancelPendingBackup() {
    if (this.backupTimer) {
      clearTimeout(this.backupTimer)
      this.backupTimer = null
      console.log('自动备份: 已取消待执行的备份')
    }
  }
}

// 创建单例实例
const autoBackupManager = new AutoBackupManager()

export default autoBackupManager
