/**
 * axios请求封装
 * @description 统一处理请求和响应，配置拦截器、超时时间、错误处理等
 */

import axios from 'axios'
import emitter from '@/utils/mitt'

// 创建axios实例
const service = axios.create({
  // 基础URL，会和请求的URL拼接
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  // 请求超时时间
  timeout: 15000,
  // 请求头
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    
    // 获取token并添加到header
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['token'] = `${token}`
    }
    
    // 可根据环境添加特定参数
    if (import.meta.env.DEV) {
      // 开发环境特殊处理
      config.params = { 
        ...config.params,
        _t: Date.now() // 防止GET请求缓存
      }
    }
    
    return config
  },
  error => {
    // 处理请求错误
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data
    // 检测token失效消息
    if (res.message && res.message.indexOf('token失效') !== -1) {
      // 发出token失效事件，让HeaderBar组件处理
      emitter.emit('token-expired', { message: res.message })
      // 返回错误信息
      return Promise.reject(new Error(res.message))
    }

    // 假设服务端返回的数据结构为 { status, data, message }
    // 可根据实际情况调整判断逻辑
    if (res.status !== undefined && res.status !== 200) {
      // 处理业务错误
      console.error('请求失败:', res.message || '未知错误')

      // 处理特定状态码
      if (res.code === 401) {
        // 登录状态失效，可以在这里处理登出或重新登录逻辑
        // 例如：router.push('/login')
        localStorage.removeItem('token')
      }

      // 返回错误信息
      return Promise.reject(new Error(res.message || '未知错误'))
    }

    // 返回成功响应数据
    return res
  },
  error => {
    
    // 处理HTTP错误
    let message = '网络请求失败，请稍后重试'
    
    if (error.response) {
      const message = error.response.data.message
      // 检测token失效消息
      if (message && message.indexOf('token失效') !== -1) {
        // 发出token失效事件，让HeaderBar组件处理
        emitter.emit('token-expired', { message: message })
        // 返回错误信息
        return Promise.reject(new Error(message))
      }
      // 服务器返回了错误状态码
      const status = error.response.status
      
      switch (status) {
        case 400:
          message = '请求错误(400)'
          break
        case 401:
          message = '未授权，请重新登录(401)'
          // 登录状态失效处理
          localStorage.removeItem('token')
          // router.push('/login')
          break
        case 403:
          message = '拒绝访问(403)'
          break
        case 404:
          message = '请求的资源不存在(404)'
          break
        case 500:
          message = '服务器错误(500)'
          break
        default:
          message = `请求失败(${status})`
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      message = '服务器无响应，请检查网络'
    }
    
    // 显示错误提示
    console.error('响应拦截器错误:', message)
    
    return Promise.reject(error)
  }
)

/**
 * 封装GET请求
 * @param {string} url - 请求URL
 * @param {object} params - 请求参数
 * @param {object} config - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export function get(url, params = {}, config = {}) {
  return service.get(url, {
    params,
    ...config
  })
}

/**
 * 封装POST请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export function post(url, data = {}, config = {}) {
  return service.post(url, data, config)
}

/**
 * 封装PUT请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export function put(url, data = {}, config = {}) {
  return service.put(url, data, config)
}

/**
 * 封装DELETE请求
 * @param {string} url - 请求URL
 * @param {object} params - 请求参数
 * @param {object} config - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export function del(url, params = {}, config = {}) {
  return service.delete(url, {
    params,
    ...config
  })
}

/**
 * 下载文件请求
 * @param {string} url - 请求URL
 * @param {object} params - 请求参数
 * @param {string} filename - 下载文件名
 * @returns {Promise} - 返回Promise对象
 */
export function download(url, params = {}, filename) {
  return service.get(url, {
    params,
    responseType: 'blob'
  }).then(response => {
    // 创建blob链接
    const blob = new Blob([response])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename || 'file'
    link.click()
    URL.revokeObjectURL(link.href)
  })
}

/**
 * 上传文件请求
 * @param {string} url - 请求URL
 * @param {FormData} formData - 表单数据
 * @param {object} config - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export function upload(url, formData, config = {}) {
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 导出默认请求配置和方法
export default {
  service,
  get,
  post,
  put,
  del,
  download,
  upload
} 