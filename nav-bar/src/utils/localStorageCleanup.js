/**
 * localStorage 清理工具
 * 用于清理不再需要的旧配置数据
 */

/**
 * 清理不再需要的 localStorage 数据
 */
export function cleanupObsoleteLocalStorage() {
  
  
  const itemsToRemove = [
    'maxGridColumns',  // 不再需要，现在使用智能网格计算器
  ]
  
  let removedCount = 0
  
  itemsToRemove.forEach(key => {
    if (localStorage.getItem(key) !== null) {
      const value = localStorage.getItem(key)
      localStorage.removeItem(key)
      
      removedCount++
    }
  })
  
  if (removedCount > 0) {
    
  } else {
    
  }
}

/**
 * 检查是否存在过时的 localStorage 数据
 * @returns {boolean} 是否存在过时数据
 */
export function hasObsoleteLocalStorage() {
  const obsoleteKeys = ['maxGridColumns']
  return obsoleteKeys.some(key => localStorage.getItem(key) !== null)
}

/**
 * 获取过时的 localStorage 数据列表
 * @returns {Array} 过时数据列表
 */
export function getObsoleteLocalStorageItems() {
  const obsoleteKeys = ['maxGridColumns']
  const obsoleteItems = []
  
  obsoleteKeys.forEach(key => {
    const value = localStorage.getItem(key)
    if (value !== null) {
      obsoleteItems.push({ key, value })
    }
  })
  
  return obsoleteItems
}

/**
 * 显示清理提示信息
 */
export function showCleanupInfo() {
  if (hasObsoleteLocalStorage()) {
    const obsoleteItems = getObsoleteLocalStorageItems()
    console.warn('发现过时的 localStorage 数据:', obsoleteItems)
    console.info('这些数据已不再使用，建议清理。运行 cleanupObsoleteLocalStorage() 进行清理。')
  }
}

/**
 * 自动清理（在应用启动时调用）
 */
export function autoCleanup() {
  // 检查是否是首次使用新版本
  const lastCleanupVersion = localStorage.getItem('lastCleanupVersion')
  const currentVersion = '2.0.0' // 智能网格版本
  
  if (lastCleanupVersion !== currentVersion) {
    cleanupObsoleteLocalStorage()
    localStorage.setItem('lastCleanupVersion', currentVersion)
    
  }
}

export default {
  cleanupObsoleteLocalStorage,
  hasObsoleteLocalStorage,
  getObsoleteLocalStorageItems,
  showCleanupInfo,
  autoCleanup
}
