<script setup>
import { useThemeStore } from './stores/theme'
import { useWallpaperStore } from './stores/wallpaper'
import { useNavigationStore } from './stores/navigation'
import { onMounted, ref, provide, watch, nextTick, onBeforeUnmount, computed } from 'vue'
import { useRoute } from 'vue-router'
import Layout from './components/layout/Layout.vue'

const themeStore = useThemeStore()
const wallpaperStore = useWallpaperStore()
const navigationStore = useNavigationStore()
const route = useRoute()

// 页面标题映射配置
const titleMap = {
  entertainment: 'LinkFun妙趣导航 | 摸鱼导航 | 你的个人专属网址收藏夹',
  office: 'LinkFun妙趣导航 | 办公导航 | AI导航 | 你的个人专属网址收藏夹',
  pure: 'LinkFun妙趣导航 | 办公导航 | AI导航 | 你的个人专属网址收藏夹'
}

// 更新页面标题函数
const updatePageTitle = (dataSource) => {
  const newTitle = titleMap[dataSource] || titleMap.entertainment
  document.title = newTitle
  console.log(`[标题更新] 数据源: ${dataSource}, 新标题: ${newTitle}`)
}

// 检查是否为后台管理路由或办公模式路由
const isAdminRoute = computed(() => {
  
  return route.path === '/office'
})

// 背景图片加载状态
const isBgLoading = ref(false)

// 访问统计
const totalVisits = ref(0)
const isVisitCountVisible = ref(true)
let visitTimer = null

// 记录页面访问
const logPageVisit = async () => {
  try {
    // 检查是否已经记录过访问
    const hasVisited = sessionStorage.getItem('hasVisitedSite')
    
    if (!hasVisited) {
      // 标记此会话已访问
      sessionStorage.setItem('hasVisitedSite', 'true')
    }
  } catch (error) {
    console.error('记录访问失败:', error)
  }
}

// 监听路由变化，但不重复记录访问
watch(() => route.path, (newPath) => {
  // 仅获取最新总访问数，不记录新访问

}, { immediate: false })

// 定时刷新访问数据
const startVisitCountTimer = () => {
  visitTimer = setInterval(() => {

  }, 60000) // 每分钟更新一次
}

// 暂时隐藏访问计数
const hideVisitCount = () => {
  isVisitCountVisible.value = false
  
  // 5秒后再显示
  setTimeout(() => {
    isVisitCountVisible.value = true
  }, 5000)
}

// 监听wallpaperStore中的bgTransparent变化
watch(() => wallpaperStore.bgTransparent, (newValue) => {
  if (newValue) {
    document.documentElement.classList.add('transparent-bg')
  } else {
    document.documentElement.classList.remove('transparent-bg')
  }
})

// 监听wallpaperStore中的currentBgUrl变化
watch(() => wallpaperStore.currentBgUrl, (newUrl, oldUrl) => {
  // 只有当URL确实发生变化时才重新加载背景
  if (newUrl && newUrl !== oldUrl) {
    // 防止直接在这里加载，我们应该让wallpaperStore来处理
    // 这里只处理显示加载状态
    isBgLoading.value = !!newUrl
  }
})

// 监听wallpaperStore中的blurAmount变化
watch(() => wallpaperStore.blurAmount, (newValue) => {
  document.documentElement.style.setProperty('--wallpaper-blur', `${newValue}px`)
})

// 监听数据源变化，动态更新页面标题
watch(() => navigationStore.currentDataSource, (newDataSource) => {
  if (newDataSource) {
    updatePageTitle(newDataSource)
  }
}, { immediate: false })

onMounted(() => {
  // 初始化壁纸设置
  wallpaperStore.initWallpaper()

  // 设置初始主题
  const savedTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', savedTheme);

  // 初始化主题
  document.documentElement.classList.add('theme-transition')

  // 设置初始页面标题
  const currentDataSource = navigationStore.currentDataSource || localStorage.getItem('currentDataSource') || 'entertainment'
  updatePageTitle(currentDataSource)

  // 记录页面访问
  logPageVisit()
  // 开始定时更新
  startVisitCountTimer()
})

onBeforeUnmount(() => {
  // 清除定时器
  if (visitTimer) {
    clearInterval(visitTimer)
  }
})
</script>

<template>
  <div 
    class="app-container"
    :class="{ 'loading-bg': isBgLoading }" 
    :style="!isAdminRoute ? { backgroundImage: `url(${wallpaperStore.currentBgUrl})` } : {}"
  >
    <div 
      v-if="!isAdminRoute"
      class="app-overlay" 
      :style="{
        backdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
        WebkitBackdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
        backgroundColor: wallpaperStore.bgTransparent ? 'rgba(0, 0, 0, 0)' : `rgba(0, 0, 0, ${0.3 + (wallpaperStore.blurAmount / 60)})`
      }"
    ></div>
    

    
    <!-- 后台页面直接渲染路由视图 -->
    <router-view></router-view>

    <!-- 前台页面使用Layout组件 -->
    <!-- <Layout /> -->
  </div>
</template>

<style lang="scss">
@use './assets/scss/main.scss' as *;

/* 重置CSS样式 */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
  display: block;
}

/* 基本行高设置 */
body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

:root {
  /* 基本颜色变量 */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --accent-color: #0d6efd;
  --accent-color-rgb: 13, 110, 253;
  --accent-color-light: #e6f0ff;
  --border-color: #dee2e6;
  --hover-bg: #f3f4f6;
  --header-bg: rgba(255, 255, 255, 0.8);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  /* z-index层级管理 */
  --z-index-base: 1;
  --z-index-layout: 10;
  --z-index-header: 100;
  --z-index-sidebar: 100;
  --z-index-dropdown: 200;
  --z-index-modal: 500;
  --z-index-modal-fullscreen: 9999;
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --text-primary: #f3f4f6;
  --text-secondary: #9ca3af;
  --accent-color: #3b82f6;
  --accent-color-rgb: 59, 130, 246;
  --accent-color-light: rgba(59, 130, 246, 0.1);
  --border-color: #374151;
  --hover-bg: #2c3544;
  --header-bg: rgba(31, 41, 55, 0.8);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

/* 蓝色主题 */
[data-theme="blue"] {
  --bg-primary: #eef2ff;
  --bg-secondary: #e0e7ff;
  --text-primary: #312e81;
  --text-secondary: #4338ca;
  --accent-color: #4f46e5;
  --accent-color-rgb: 79, 70, 229;
  --accent-color-light: rgba(79, 70, 229, 0.1);
  --border-color: #a5b4fc;
  --hover-bg: #c7d2fe;
  --header-bg: rgba(224, 231, 255, 0.8);
  --shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
  
  /* 侧边栏特殊颜色 */
  --sidebar-bg: #312e81;
  --sidebar-text-primary: #eef2ff;
  --sidebar-text-secondary: #a5b4fc;
}

/* 全局样式 */
html, body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: #fff;
  margin: 0;
  padding: 0;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root, .transparent-bg {
  --text-color: #fff;
  --text-secondary-color: rgba(255, 255, 255, 0.8);
}

.app-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow: hidden;
  color: #fff;
  transition: background-image 0.3s ease;
}

.app-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 0;
  transition: all 0.5s ease;
}

.layout-container {
  position: relative;
  z-index: 1;
}

/* 透明背景模式 */
.transparent-bg {
  --bg-opacity: 0;
  --bg-blur: 0px;
}

.transparent-bg
.transparent-bg[data-theme="dark"] ,
.transparent-bg[data-theme="blue"] ,
.transparent-bg .header-bar,
.transparent-bg[data-theme="dark"] .header-bar,
.transparent-bg[data-theme="blue"] .header-bar,
.transparent-bg .main-content,
.transparent-bg[data-theme="dark"] .main-content,
.transparent-bg[data-theme="blue"] .main-content {
  background-color: transparent !important;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border: none;
}

.transparent-bg .main-content.expanded {
  margin: 0;
  width: 100%;
  max-width: none;
}

.transparent-bg .menu-tooltip {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  box-shadow: none;
  border: none;
}

.transparent-bg .tooltip-arrow {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
}

.transparent-bg[data-theme="dark"] .menu-tooltip,
.transparent-bg[data-theme="dark"] .tooltip-arrow {
  background-color: rgba(0, 0, 0, 0.5);
}

.transparent-bg[data-theme="blue"] .menu-tooltip,
.transparent-bg[data-theme="blue"] .tooltip-arrow {
  background-color: rgba(0, 0, 0, 0.5);
}

.transparent-bg .app-overlay {
  background-color: rgba(0, 0, 0, 0);
}

/* 壁纸加载状态样式 */
.loading-bg {
  animation: bgPulse 2s infinite;
}

@keyframes bgPulse {
  0% { 
    background-color: rgba(0, 0, 0, 0.2);
  }
  50% { 
    background-color: rgba(0, 0, 0, 0.4);
  }
  100% { 
    background-color: rgba(0, 0, 0, 0.2);
  }
}

/* 访问计数器 */
.visit-counter {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(5px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.visit-counter:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: translateX(-50%) translateY(-3px);
}

.visit-icon {
  display: flex;
  align-items: center;
}

/* 访问计数器动画 */
.visit-counter-enter-active,
.visit-counter-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.visit-counter-enter-from,
.visit-counter-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(10px);
}

/* 全屏模式弹窗相关样式 */
body.modal-fullscreen-mode {
  overflow: hidden !important;
}

/* 全屏模式下降低布局元素的z-index */
body.modal-fullscreen-mode .side-navigation,
body.modal-fullscreen-mode .header-bar,
body.modal-fullscreen-mode .fixed-logo-container {
  z-index: 0 !important;
}

/* 确保弹窗在全屏模式下的z-index足够高 */
body.modal-fullscreen-mode .app-modal-overlay,
body.modal-fullscreen-mode .app-modal-fullscreen {
  z-index: var(--z-index-modal-fullscreen) !important;
}

.app-icon-div {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 15px;
  .app-icon-text{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 20px; /* 默认字体大小 */
    font-weight: 600;
    color: #333;
    // background: #ffffff;
    white-space: nowrap; /* 防止文字换行 */
    // overflow: hidden; /* 超出部分隐藏 */
    // text-overflow: ellipsis; /* 超出部分显示省略号 */
    padding: 0 4px; /* 添加少量内边距 */
    box-sizing: border-box; /* 确保内边距不会增加元素尺寸 */
    transform-origin: center; /* 确保缩放从中心开始 */
    /* 删除固定缩放比例，将由JS动态设置 */
    /* 删除各种length类的样式，不再使用CSS固定缩放 */
  }
}


</style>
