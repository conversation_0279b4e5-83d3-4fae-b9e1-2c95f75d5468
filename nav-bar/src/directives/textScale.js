/**
 * 文本缩放自定义指令
 * 根据文本内容动态计算最佳缩放比例
 */

/**
 * 检测字符是否为全角字符（如中文、日文等）
 * @param {string} char 单个字符
 * @return {boolean} 是否为全角字符
 */
const isFullWidthChar = (char) => {
  // 检查字符的Unicode编码范围
  const code = char.charCodeAt(0);
  // 常见的全角字符范围（中文、日文、韩文等）
  return (
    (code >= 0x3000 && code <= 0x9FFF) || // CJK统一表意文字及符号
    (code >= 0xFF00 && code <= 0xFFEF) || // 全角ASCII、全角标点
    (code >= 0x20000 && code <= 0x2A6DF) // CJK扩展B区
  );
};

/**
 * 计算字符串的"视觉宽度"
 * 全角字符计为1.8个单位，半角字符计为1个单位
 * @param {string} text 要计算的文本
 * @return {number} 文本的视觉宽度
 */
const calculateVisualWidth = (text) => {
  let width = 0;
  for (let i = 0; i < text.length; i++) {
    width += isFullWidthChar(text[i]) ? 1.8 : 1;
  }
  return width;
};

/**
 * 测量文本实际宽度
 * @param {string} text 要测量的文本
 * @param {string} fontWeight 字体粗细
 * @param {number} fontSize 字体大小(px)
 * @return {number} 实际像素宽度
 */
const measureTextWidth = (text, fontWeight = '600', fontSize = 20) => {
  // 创建一个临时元素用于测量
  const measurer = document.createElement('span');
  measurer.style.visibility = 'hidden';
  measurer.style.position = 'absolute';
  measurer.style.whiteSpace = 'nowrap';
  measurer.style.fontWeight = fontWeight;
  measurer.style.fontSize = `${fontSize}px`;
  measurer.textContent = text;
  
  // 添加到body以获取真实尺寸
  document.body.appendChild(measurer);
  const width = measurer.getBoundingClientRect().width;
  document.body.removeChild(measurer);
  
  return width;
};

/**
 * 识别元素使用场景
 * @param {Element} element 元素
 * @return {string} 场景类型：'main-app' | 'folder-item' | 'other'
 */
const identifyScenario = (element) => {
  const container = element.parentElement;
  if (!container) return 'other';

  // 检查是否为文件夹内的小图标
  if (container.classList.contains('app-icon-div') &&
      container.parentElement &&
      container.parentElement.classList.contains('folder-grid-item')) {
    return 'folder-item';
  }

  // 检查是否为主应用图标
  if (container.classList.contains('app-icon-div') &&
      element.classList.contains('app-icon-text')) {
    return 'main-app';
  }

  return 'other';
};

/**
 * 增强的容器尺寸检测
 * @param {Element} element 元素
 * @return {number} 容器宽度
 */
const getEnhancedContainerWidth = (element) => {
  const container = element.parentElement;
  let containerWidth = 0;

  if (container) {
    // 优先使用 clientWidth
    containerWidth = container.clientWidth;

    // 如果 clientWidth 为 0，尝试 offsetWidth
    if (!containerWidth) {
      containerWidth = container.offsetWidth;
    }

    // 如果还是 0，尝试 getBoundingClientRect
    if (!containerWidth) {
      const rect = container.getBoundingClientRect();
      containerWidth = rect.width;
    }

    // 尝试通过 getComputedStyle 获取计算后的宽度
    if (!containerWidth) {
      const computedStyle = getComputedStyle(container);
      const width = parseFloat(computedStyle.width);
      if (width && !isNaN(width)) {
        containerWidth = width;
      }
    }
  }

  // 如果容器宽度仍为 0，使用元素自身宽度
  if (!containerWidth) {
    containerWidth = element.clientWidth || element.offsetWidth;
    const rect = element.getBoundingClientRect();
    if (!containerWidth && rect.width) {
      containerWidth = rect.width;
    }
  }

  return containerWidth;
};

/**
 * 计算最佳缩放比例
 * @param {string} text 要显示的文本
 * @param {Element} element 元素
 * @param {boolean} isRetry 是否为重试调用
 * @return {number} 计算得出的缩放比例
 */
const calculateOptimalScale = (text, element, isRetry = false) => {
  if (!text || text.length === 0) return 1;

  // 识别使用场景
  const scenario = identifyScenario(element);

  // 获取容器宽度
  const containerWidth = getEnhancedContainerWidth(element);

  // 如果容器宽度仍然无效，根据场景返回不同的默认值
  if (!containerWidth || containerWidth < 10) {
    switch (scenario) {
      case 'main-app':
        return 0.6; // 主应用图标使用更保守的默认缩放
      case 'folder-item':
        return 0.5; // 文件夹内图标使用较小的默认缩放
      default:
        return 0.65;
    }
  }

  // 获取元素的计算样式
  const computedStyle = getComputedStyle(element);
  const fontWeight = computedStyle.fontWeight;
  const fontSize = parseInt(computedStyle.fontSize);

  // 测量文本的实际宽度
  const textWidth = measureTextWidth(text, fontWeight, fontSize);

  // 根据场景调整缩放策略
  let paddingRatio, minScale, maxScale;

  switch (scenario) {
    case 'main-app':
      paddingRatio = 0.75; // 主应用图标留出25%的内边距，更保守
      minScale = 0.3;      // 最小缩放比例
      maxScale = 0.85;     // 最大缩放比例，避免过大
      break;
    case 'folder-item':
      paddingRatio = 0.7;  // 文件夹内图标留出30%的内边距
      minScale = 0.25;     // 最小缩放比例
      maxScale = 0.8;      // 最大缩放比例，避免过大
      break;
    default:
      paddingRatio = 0.8;  // 其他场景留出20%的内边距
      minScale = 0.3;
      maxScale = 0.9;
  }

  // 计算缩放比例
  const optimalScale = Math.min(maxScale, (containerWidth * paddingRatio) / textWidth);
  const finalScale = Math.max(minScale, optimalScale);


  return finalScale;
};

/**
 * 延迟执行缩放计算
 * @param {Element} el 元素
 * @param {Object} binding 绑定对象
 * @param {number} delay 延迟时间(ms)
 */
const delayedCalculate = (el, binding, delay = 0) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const text = binding.value || el.textContent;
      const scale = calculateOptimalScale(text, el);
      el.style.transform = `scale(${scale})`;
      el._textScaleData = { text, scale };
      resolve(scale);
    }, delay);
  });
};

/**
 * 重试计算机制
 * @param {Element} el 元素
 * @param {Object} binding 绑定对象
 * @param {number} maxRetries 最大重试次数
 * @param {number} retryDelay 重试间隔(ms)
 */
const retryCalculation = async (el, binding, maxRetries = 3, retryDelay = 100) => {
  let retryCount = 0;
  const scenario = identifyScenario(el);

  while (retryCount < maxRetries) {
    const text = binding.value || el.textContent;
    const containerWidth = getEnhancedContainerWidth(el);

    // 如果容器宽度有效，执行计算
    if (containerWidth && containerWidth > 10) {
      const scale = calculateOptimalScale(text, el, retryCount > 0);
      el.style.transform = `scale(${scale})`;
      el._textScaleData = { text, scale };
      return scale;
    }

    retryCount++;
    if (retryCount < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount));
    }
  }

  // 重试失败，使用场景相关的默认缩放
  const defaultScale = calculateOptimalScale(binding.value || el.textContent, el, true);
  el.style.transform = `scale(${defaultScale})`;
  el._textScaleData = { text: binding.value || el.textContent, scale: defaultScale };
  return defaultScale;
};

/**
 * 检查网格布局是否已应用
 * @param {Element} element 元素
 * @return {boolean} 网格布局是否已应用
 */
const isGridLayoutApplied = (element) => {
  // 查找网格容器
  const gridContainer = document.querySelector('.app-grid');
  if (!gridContainer) return false;

  // 检查网格样式是否已应用
  const computedStyle = getComputedStyle(gridContainer);
  const gridTemplateColumns = computedStyle.gridTemplateColumns;
  const gridAutoRows = computedStyle.gridAutoRows;

  // 如果网格样式已应用，应该不是 'none' 或空值
  const hasGridStyles = gridTemplateColumns && gridTemplateColumns !== 'none' &&
                       gridAutoRows && gridAutoRows !== 'auto';

  return hasGridStyles;
};

/**
 * 等待网格布局完成
 * @param {Element} element 元素
 * @return {Promise<boolean>} 是否检测到布局完成
 */
const waitForGridLayout = (element) => {
  return new Promise((resolve) => {
    const scenario = identifyScenario(element);

    let attempts = 0;
    const maxAttempts = 50; // 大幅增加最大尝试次数
    const checkInterval = 200; // 增加检查间隔

    const checkLayout = () => {
      const containerWidth = getEnhancedContainerWidth(element);
      const gridApplied = isGridLayoutApplied(element);

      // 同时检查容器宽度和网格布局是否已应用
      if (containerWidth && containerWidth > 10 && gridApplied) {
        resolve(true);
        return;
      }

      attempts++;
      if (attempts >= maxAttempts) {
        resolve(false);
        return;
      }

      // 每10次尝试输出一次调试信息
      if (attempts % 10 === 0) {
      }

      setTimeout(checkLayout, checkInterval);
    };

    // 立即开始第一次检查
    checkLayout();
  });
};

export default {
  mounted(el, binding) {
    // 设置样式以确保正确计算
    el.style.transformOrigin = 'center center';

    // 获取绑定的文本内容
    const text = binding.value || el.textContent;

    // 使用多层次延迟的初始化流程
    const executeCalculation = async () => {
      const scenario = identifyScenario(el);

      // 第一层延迟：等待Vue的nextTick
      await new Promise(resolve => {
        if (typeof window !== 'undefined' && window.Vue && window.Vue.nextTick) {
          window.Vue.nextTick(resolve);
        } else {
          setTimeout(resolve, 0);
        }
      });

      // 第二层延迟：等待DOM完全渲染
      await new Promise(resolve => setTimeout(resolve, 100));

      // 第三层延迟：对于主应用图标，等待网格布局完成
      if (scenario === 'main-app') {
        await waitForGridLayout(el);

        // 网格布局完成后，再等待一个短暂的时间确保样式完全应用
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // 尝试计算
      const containerWidth = getEnhancedContainerWidth(el);

      if (containerWidth && containerWidth > 10) {
        // 容器宽度有效，直接计算
        const scale = calculateOptimalScale(text, el);
        el.style.transform = `scale(${scale})`;
        el._textScaleData = { text, scale };
      } else {
        // 容器宽度无效，使用重试机制
        await retryCalculation(el, binding, 5, 300); // 增加重试次数和间隔
      }
    };

    // 使用 requestAnimationFrame 确保在下一帧执行
    requestAnimationFrame(() => {
      executeCalculation().catch(error => {
        // 失败时使用默认缩放
        const defaultScale = calculateOptimalScale(text, el, true);
        el.style.transform = `scale(${defaultScale})`;
        el._textScaleData = { text, scale: defaultScale };
      });
    });

    // 添加窗口大小变化监听，使用防抖处理
    let resizeTimeout;
    const resizeHandler = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        const currentText = binding.value || el.textContent;
        const newScale = calculateOptimalScale(currentText, el);
        el.style.transform = `scale(${newScale})`;
        el._textScaleData = { text: currentText, scale: newScale };
      }, 150); // 150ms 防抖
    };

    window.addEventListener('resize', resizeHandler);
    el._resizeHandler = resizeHandler;
    el._resizeTimeout = resizeTimeout;
  },
  
  updated(el, binding) {
    const newText = binding.value || el.textContent;

    // 如果文本没有变化，不需要重新计算
    if (el._textScaleData && el._textScaleData.text === newText) return;

    // 文本变化时也使用重试机制，但重试次数较少
    const executeUpdate = async () => {
      const scenario = identifyScenario(el);
      const containerWidth = getEnhancedContainerWidth(el);

      if (containerWidth && containerWidth > 10) {
        // 容器宽度有效，直接计算
        const scale = calculateOptimalScale(newText, el);
        el.style.transform = `scale(${scale})`;
        el._textScaleData = { text: newText, scale };
      } else {
        // 容器宽度无效，使用简化的重试机制
        await retryCalculation(el, binding, 2, 50); // 最多重试2次，间隔50ms
      }
    };

    // 使用 requestAnimationFrame 确保在下一帧执行
    requestAnimationFrame(() => {
      executeUpdate();
    });
  },

  unmounted(el) {
    // 移除resize事件监听
    if (el._resizeHandler) {
      window.removeEventListener('resize', el._resizeHandler);
    }

    // 清理防抖定时器
    if (el._resizeTimeout) {
      clearTimeout(el._resizeTimeout);
    }
  }
}; 