import axios from 'axios'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['token'] = `${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => response,
  error => {
    // 处理 401 未授权错误
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} 返回登录结果Promise
 */
export const login = (username, password) => {
  // 创建FormData对象
  const formData = new FormData()
  formData.append('username', username)
  formData.append('password', password)
  
  return apiClient.post('/login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(response => {
    // 确保响应中包含access_token
    if (response.data && response.data.access_token) {
      // 设置默认Authorization头
      apiClient.defaults.headers.common['Authorization'] = 
        `${response.data.token_type} ${response.data.access_token}`
    }
    return response
  })
}

/**
 * 退出登录
 * @returns {Promise} 返回登出结果Promise
 */
export const logout = () => {
  return apiClient.post('/logout')
}

/**
 * 获取当前用户信息
 * @returns {Promise} 返回用户信息Promise
 */
export const getUserInfo = () => {
  return apiClient.get('/user/info')
}

export default {
  login,
  logout,
  getUserInfo
} 