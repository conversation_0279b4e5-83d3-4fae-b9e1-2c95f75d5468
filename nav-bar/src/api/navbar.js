import { get, post } from '@/utils/request'

/**
 * 获取菜单
 * @returns {Promise} 返回访问总数Promise，数据格式为 { total_visits: number }
 */
export function getMenuList() {
  return get('/websiteHome/gethome')
}

/**
 * 获取用户娱乐的菜单
 */

export function getUserMenuHomeList() {
  return get('/user/gethome')
}

/**
 * 获取用户office的菜单
 */
export function getUserOfficeList() {
  return get('/user/getOffice')
}



/**
 * 获取办公菜单
 * @returns {Promise} 返回访问总数Promise，数据格式为 { total_visits: number }
 */
export function getOfficeList() {
  return get('/websiteHome/getOffice')
}


/**
 * 获取微信登录二维码
 * @returns {Promise} 返回包含二维码URL的Promise
 */
export function getWxLoginCode() {
  return get('/vxLogin/getWechatCode')
}

/**
 * 检查微信登录状态
 * @param {string} sessionId - 微信登录会话ID或二维码URL
 * @returns {Promise} 返回登录状态的Promise
 */
export function checkWxLoginStatus(sessionId) {
  // 确保sessionId是一个合法参数
  const params = sessionId ? { sessionId } : {}
  return get('/vxLogin/checkLogin', params)
}

/**
 * 获取导航树结构
 * @returns {Promise} 返回导航树结构Promise
 */
export function getNavTree() {
  return get('/navbar/getTree')
}

/**
 * 创建导航项
 * @param {Object} data - 导航项数据
 * @returns {Promise} 返回创建结果Promise
 */
export function createNavItem(data) {
  return post('/navbar/create', data)
}

/**
 * 更新导航项
 * @param {Object} data - 导航项更新数据
 * @returns {Promise} 返回更新结果Promise
 */
export function updateNavItem(data) {
  return post('/navbar/update', data)
}

/**
 * 删除导航项
 * @param {string|number} id - 导航项ID
 * @returns {Promise} 返回删除结果Promise
 */
export function deleteNavItem(id) {
  return post('/navbar/delete', { id })
}

/**
 * 获取摸鱼收藏栏
 * @returns {Promise} 返回收藏栏Promise
 */
export function getCollectList() {
  return get('/websiteHome/listCollect/1')
}

/**
 * 获取办公收藏栏
 * @returns {Promise} 返回收藏栏Promise
 */
export function getOfficeCollectList() {
  return get('/websiteHome/listCollect/2')
}

export function getAllAppList(page, pageSize) {
  console.log(page, pageSize, 'page,pageSize');
  return get('/websiteHome/getAllApp', {
    page,
    pageSize
  })
}

export function queryAppName(data){
  return post(`/websiteHome/search/`, data, {
    headers: {
      'Content-Type': 'multipart/form-data' // 确保设置正确的Content-Type
    }
  })
}

export function getAboutUs(){
  return get('/stepup/aboutMe')
}

// 上传自定义图标
export function uploadFiles(formData){
  console.log('上传文件FormData:', formData);
  // 直接传递FormData对象，不要包装在对象中
  return post('/file/remoteUpload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data' // 确保设置正确的Content-Type
    }
  });
}


// 通过id获取合集数据
export function getCollectListByid(id){
  return get(`/websiteHome/listGatherById/${id}`)
}
// 备份
export function backup(data){
  return post('/user/userBackup', data)
}
// 备份list
export function restoreBackup(){
  return get('user/userRestoreBackup')
}

// 备份list
export function getBackupList(){
  return get('user/listUserBackup')
}

// 用户新增app
export function addUserApp(data) {
  return post('/user/addApp', data, {
    headers: {
      'Content-Type': 'multipart/form-data' // 确保设置正确的Content-Type
    }
  });
}
 

// 用户删除app
export function deleteUserApp(id){
  return get(`user/deleteApp/${id}`)
}





export default {
  getMenuList,
  getWxLoginCode,
  checkWxLoginStatus,
  getNavTree,
  createNavItem,
  updateNavItem,
  deleteNavItem,
  getCollectList,
  getAllAppList,
  getOfficeCollectList,
  backup
} 