import { get, post } from '@/utils/request'


/**
 * 获取所有用户推荐壁纸
 * @param {number} page - 页码，从1开始
 * @param {number} pageSize - 每页数量
 * @returns {Promise} API请求的Promise对象
 */
export function getOfficePaper(page = 1, pageSize = 100){
  return get(`/wallpaper/listSystemWallpaper?page=${page}&pageSize=${pageSize}`)
}

/**
 * 用户上传
 * @param {*} data 
 * @returns 
 */

export function useRuploadWallpaper(data) {
  return post('/file/wallpaperUpload', data, {
    headers: {
      'Content-Type': 'multipart/form-data' // 确保设置正确的Content-Type
    }
  });
}

/**
 * 用户壁纸库
 */

export function listUserWallpaper(){
  return get(`/wallpaper/listUserWallpaper`)
}


/**
 * 用户共享壁纸 id share
 */
export function userWallpaperIsShare(data) {
  return post('/wallpaper/userWallpaperIsShare', data);
}

/**
 * 获取所有用户共享壁纸
 */

export function getAllPublickWallpaper() {
  return get('/wallpaper/listUserlWallpaperShare');
}

/**
 * 用户下载记录
 */
export function userDownloadWallpaper(code) {
  return get(`/wallpaper/userDownloadWallpaper/${code}`);
}

/**
 * 用户下载记录列表
 */
export function listDownloadUserWallpaper() {
  return get('/wallpaper/listDownloadUserWallpaper');
}

/**
 * 删除用户个人壁纸库 在个人壁纸库中可以删除
 */

export function userDeleteWallpaper(id) {
  return get(`wallpaper/userDeleteWallpaper/${id}`)
}
  
/**
 * 列出用户收藏壁纸
 */
export function listUserCollectWallpaper() {
  return get('/wallpaper/listUserCollectWallpaper');
}

/**
 * 用户收藏壁纸
 */
export function userCollectWallpaper(code) {
  return get(`/wallpaper/userCollectWallpaper/${code}`);
}
   