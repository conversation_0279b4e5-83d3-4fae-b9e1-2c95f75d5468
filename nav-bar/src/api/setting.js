import { get, post } from '@/utils/request'




/**
 * 清空用户所有备份还原
 * @param {*} data 
 * @returns 
 */
export function userRestBackup() {
  return get('/user/userRestBackup')
}

/**
 * 版本信息
 */
export function versionInfo() {
  return get('/common/listVersion')
}


/**
 * 合作信息接口
 */
  
export function getExchangeList() {
  return get('/common/listCooperationExchange')
}

/**
 * 上下边距设置
 */
export function getPaddings() {
  return get('/stepup/timeAndSearchSetting')
}
  