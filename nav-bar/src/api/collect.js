/**
 * 网格布局配置相关API
 */

import { get, post } from '@/utils/request.js'

/**
 * 获取网格布局配置
 * @returns {Promise} 返回网格配置数据
 */
export function AddUseCollect(id) {
  return get(`/collect/addCollect/${id}`)
}

export function getUserCollect() {
  return get(`/collect/listCollect`)
}

// 删除收集逻辑
export function deleteCollect(id) {
  return get(`/collect/cancalCollect/${id}`)
}

// 新增dock栏固定
export function fixedApp(id) {
  return get(`/collect/fixedApp/${id}`)
}

// 取消dock栏固定
export function cancalfixedApp(id) {
  return get(`/collect/cancalfixedApp/${id}`)
}