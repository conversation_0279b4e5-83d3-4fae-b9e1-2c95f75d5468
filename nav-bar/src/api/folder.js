/**
 * 文件夹相关逻辑
 */

import { get, post } from '@/utils/request.js'

/**
 * 获取网格布局配置
 * @returns {Promise} 返回网格配置数据
 */
export function getFolderList(id) {
  return get(`/websiteHome/listFolderById/${id}`)
}

/**
 * 保存文件夹
 * @param {Object} data - 文件夹数据
 * @returns {Promise} 返回保存结果Promise
 */
export function saveFolder(data) {
  return post('/user/addFolder', data, {
    headers: {
      'Content-Type': 'multipart/form-data' // 确保设置正确的Content-Type
    }
  });
}

/**
 * 文件夹添加应用
 * get
 * http://************:8888/user/addFolderUnderApp    
 */

export function saveFolderApp(folderId, appId) {
  return get(`/user/addFolderUnderApp?folderId=${folderId}&appId=${appId}`,);
}


/**
 * 删除文件夹
 */
export function deleteFolder(folderId) {
  return get(`/user/deleteFolder/${folderId}`,);
}