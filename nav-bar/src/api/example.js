/**
 * 示例API模块
 * @description 展示如何使用封装的request工具
 */

import { get, post, put, del } from '@/utils/request'

/**
 * 获取示例列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回数据列表
 */
export function getExampleList(params) {
  return get('/examples', params)
}

/**
 * 获取示例详情
 * @param {string|number} id - 示例ID
 * @returns {Promise} - 返回详情数据
 */
export function getExampleDetail(id) {
  return get(`/examples/${id}`)
}

/**
 * 创建示例
 * @param {Object} data - 示例数据
 * @returns {Promise} - 返回创建结果
 */
export function createExample(data) {
  return post('/examples', data)
}

/**
 * 更新示例
 * @param {string|number} id - 示例ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 返回更新结果
 */
export function updateExample(id, data) {
  return put(`/examples/${id}`, data)
}

/**
 * 删除示例
 * @param {string|number} id - 示例ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteExample(id) {
  return del(`/examples/${id}`)
}

/**
 * 批量删除示例
 * @param {Array} ids - ID数组
 * @returns {Promise} - 返回批量删除结果
 */
export function batchDeleteExamples(ids) {
  return post('/examples/batch-delete', { ids })
}

/**
 * 上传示例图片
 * @param {FormData} formData - 表单数据，包含图片文件
 * @returns {Promise} - 返回上传结果
 */
export function uploadExampleImage(formData) {
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }
  return post('/examples/upload', formData, config)
}

export default {
  getExampleList,
  getExampleDetail,
  createExample, 
  updateExample,
  deleteExample,
  batchDeleteExamples,
  uploadExampleImage
} 