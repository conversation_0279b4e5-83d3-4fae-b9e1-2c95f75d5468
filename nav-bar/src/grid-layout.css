/* grid-layout 相关样式 */
.vue-grid-layout {
  position: relative;
  width: 100%;
  height: auto !important;
}

.vue-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, right;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.vue-grid-item.cssTransforms {
  transition-property: transform;
  left: 0;
  right: auto;
}

.vue-grid-item.resizing {
  opacity: 0.9;
  z-index: 3;
}

.vue-grid-item.vue-draggable-dragging {
  transition: none;
  z-index: 3;
  opacity: 0.8;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.vue-grid-item.vue-grid-placeholder {
  background: rgba(0, 0, 0, 0.1);
  border: 2px dashed var(--accent-color, #4285F4);
  border-radius: 15px;
  transition-duration: 100ms;
  z-index: 2;
  user-select: none;
}

.vue-grid-item > .vue-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjODg4ODg4Ij48cGF0aCBkPSJNMzQgNDBsNCA0VjMwSDI4djE0bDQtNHptMC03SDQ0djRIMzR6bTcgN2g0djRoLTR6Ii8+PC9zdmc+');
  background-position: bottom right;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
  opacity: 0.5;
}

.vue-grid-item > .vue-resizable-handle:hover {
  opacity: 1;
}

.vue-grid-item > .vue-rtl-resizable-handle {
  bottom: 0;
  left: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjODg4ODg4Ij48cGF0aCBkPSJNMTQgNDBsLTQgNFYzMGgxMHYxNGwtNC00em0wLTdINHY0aDEwem0tNyA3SDN2NGg0eiIvPjwvc3ZnPg==');
  background-position: bottom left;
  cursor: sw-resize;
}

.vue-grid-item.disable-user-select {
  user-select: none;
} 