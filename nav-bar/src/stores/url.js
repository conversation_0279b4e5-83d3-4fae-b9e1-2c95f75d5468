import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useUrlStore = defineStore('url', () => {
  // 从本地存储获取URL，如果没有则为空字符串
  const appUrl = ref(localStorage.getItem('appUrl') || '')
  const collectUrl = ref(localStorage.getItem('collectUrl') || '')
  
  // 监听appUrl变化，更新本地存储
  watch(appUrl, (newUrl) => {
    localStorage.setItem('appUrl', newUrl)
  })
  
  // 监听collectUrl变化，更新本地存储
  watch(collectUrl, (newUrl) => {
    localStorage.setItem('collectUrl', newUrl)
  })
  
  // 设置appUrl
  function setAppUrl(url) {
    appUrl.value = url
  }
  
  // 设置collectUrl
  function setCollectUrl(url) {
    collectUrl.value = url
  }

  // 添加一个函数来正确拼接URL
  function concatUrl(path) {
    // 如果path为null那么就随机a-z加上.png
    if(path == null || path == undefined || path == '') {
      // 生成随机a-z字母
      // const randomChar = String.fromCharCode(97 + Math.floor(Math.random() * 26));
      // path = randomChar + '.png';
    }
    if(path.includes('http')){
      return path;
    }
    
    // 直接使用内部的appUrl.value而不是urlStore.appUrl
    const baseUrl = appUrl.value;
    
    if (!baseUrl) return path;
    if (!path) return baseUrl;
    
    // 处理baseUrl结尾和path开头的斜杠，确保只有一个斜杠
    const baseEndsWithSlash = baseUrl.endsWith('/');
    const pathStartsWithSlash = path.startsWith('/');
    
    if (baseEndsWithSlash && pathStartsWithSlash) {
      return baseUrl + path.substring(1);
    } else if (!baseEndsWithSlash && !pathStartsWithSlash) {
      return baseUrl + '/' + path;
    } else {
      return baseUrl + path;
    }
  }
  
  return {
    appUrl,
    collectUrl,
    setAppUrl,
    setCollectUrl,
    concatUrl // 将函数添加到返回对象中以便外部访问
  }
}) 