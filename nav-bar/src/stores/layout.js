import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useLayoutStore = defineStore('layout', () => {
  // 从 localStorage 初始化状态，如果不存在则使用默认值
  const isSidebarFixed = ref(JSON.parse(localStorage.getItem('isSidebarFixed') || 'false'))
  const isDockBarFixed = ref(JSON.parse(localStorage.getItem('isDockBarFixed') || 'true'))

  // 切换侧边栏固定状态
  function setSidebarFixed(value) {
    isSidebarFixed.value = value
  }

  // 切换Dock栏固定状态
  function setDockBarFixed(value) {
    isDockBarFixed.value = value
  }

  // 监听状态变化并持久化到 localStorage
  watch(isSidebarFixed, (newValue) => {
    localStorage.setItem('isSidebarFixed', JSON.stringify(newValue))
  })

  watch(isDockBarFixed, (newValue) => {
    localStorage.setItem('isDockBarFixed', JSON.stringify(newValue))
  })

  return {
    isSidebarFixed,
    isDockBarFixed,
    setSidebarFixed,
    setDockBarFixed,
  }
})
