import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSettingStore = defineStore('setting', {
  state: () => ({
    closeModalOnOutsideClick: ref(true),
    closeModalOnButtonClick: ref(true),
  }),
  actions: {
    setCloseModalOnOutsideClick(value) {
      this.closeModalOnOutsideClick = value
    },
    setCloseModalOnButtonClick(value) {
      this.closeModalOnButtonClick = value
    },
  },
  persist: true,
})
