import { defineStore } from 'pinia'
import { ref, watch, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 支持的主题
  const themes = ['light', 'dark', 'blue']
  
  // 默认从本地存储获取主题，如果没有则使用light
  const currentTheme = ref(localStorage.getItem('theme') || 'light')
  
  // 监听主题变化，更新CSS变量和本地存储
  watch(currentTheme, (newTheme) => {
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
    
    // 添加过渡效果
    document.documentElement.classList.add('theme-transition')
    setTimeout(() => {
      document.documentElement.classList.remove('theme-transition')
    }, 1000)
  }, { immediate: true })
  
  // 切换主题
  function toggleTheme() {
    const currentIndex = themes.indexOf(currentTheme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    currentTheme.value = themes[nextIndex]
  }
  
  // 设置特定主题
  function setTheme(theme) {
    if (themes.includes(theme)) {
      currentTheme.value = theme
    }
  }
  
  // 应用主题（设置CSS变量）
  function applyTheme(theme) {
    const root = document.documentElement
    
    // 移除所有主题类
    themes.forEach(t => {
      root.classList.remove(`theme-${t}`)
    })
    
    // 移除旧的data-theme属性
    root.removeAttribute('data-theme')
    
    // 添加当前主题类
    root.classList.add(`theme-${theme}`)
    
    // 设置data-theme属性
    root.setAttribute('data-theme', theme)
    
    // 设置主题颜色
    switch(theme) {
      case 'light':
        root.style.setProperty('--bg-primary', '#ffffff')
        root.style.setProperty('--bg-secondary', '#f8f9fa')
        root.style.setProperty('--sidebar-bg', '#ffffff')
        root.style.setProperty('--text-primary', '#333333')
        root.style.setProperty('--text-secondary', '#6c757d')
        root.style.setProperty('--accent-color', '#3b82f6')
        root.style.setProperty('--accent-color-light', '#eff6ff')
        root.style.setProperty('--border-color', '#e5e7eb')
        root.style.setProperty('--hover-bg', '#f3f4f6')
        root.style.setProperty('--card-bg', '#ffffff')
        root.style.setProperty('--sidebar-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1)')
        break
        
      case 'dark':
        root.style.setProperty('--bg-primary', '#111827')
        root.style.setProperty('--bg-secondary', '#1f2937')
        root.style.setProperty('--sidebar-bg', '#1f2937')
        root.style.setProperty('--text-primary', '#f3f4f6')
        root.style.setProperty('--text-secondary', '#9ca3af')
        root.style.setProperty('--accent-color', '#60a5fa')
        root.style.setProperty('--accent-color-light', '#1e3a8a20')
        root.style.setProperty('--border-color', '#374151')
        root.style.setProperty('--hover-bg', '#374151')
        root.style.setProperty('--card-bg', '#1f2937')
        root.style.setProperty('--sidebar-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.3)')
        break
        
      case 'blue':
        root.style.setProperty('--bg-primary', '#eef2ff')
        root.style.setProperty('--bg-secondary', '#e0e7ff')
        root.style.setProperty('--sidebar-bg', '#312e81')
        root.style.setProperty('--text-primary', '#1e1b4b')
        root.style.setProperty('--text-secondary', '#4338ca')
        root.style.setProperty('--accent-color', '#4f46e5')
        root.style.setProperty('--accent-color-light', '#c7d2fe')
        root.style.setProperty('--border-color', '#a5b4fc')
        root.style.setProperty('--hover-bg', '#c7d2fe')
        root.style.setProperty('--card-bg', '#ffffff')
        root.style.setProperty('--sidebar-shadow', '0 4px 6px -1px rgba(79, 70, 229, 0.2)')
        
        // 在蓝色主题下，侧边栏文字颜色需要调整
        root.style.setProperty('--sidebar-text-primary', '#e0e7ff')
        root.style.setProperty('--sidebar-text-secondary', '#a5b4fc')
        break
    }
  }
  
  // 初始化主题
  function initTheme() {
    applyTheme(currentTheme.value)
  }
  
  // 计算当前是否为暗色主题
  const isDarkTheme = computed(() => currentTheme.value === 'dark')
  
  return { 
    currentTheme, 
    themes, 
    toggleTheme, 
    setTheme,
    initTheme,
    isDarkTheme
  }
}) 