import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import './style.css'
import App from './App.vue'
import 'uno.css'
// 导入Ant Design Vue样式和组件
import 'ant-design-vue/dist/reset.css'
import { ConfigProvider } from 'ant-design-vue'
// 导入全局自定义样式，需要添加网格布局相关的样式
import './grid-layout.css'
// 导入鱼形光标样式
import './assets/css/fish-cursor.css'
// 导入dayjs配置
import dayjs from './utils/dayjs.js'
// 导入Ant Design Vue配置
import setupAntd from './plugins/antd'
import textScale from './directives/textScale'

import BaiduCalendar from "vue-baidu-calendar"

const app = createApp(App)

// 初始化Ant Design Vue配置
setupAntd()

// 注册路由
app.use(router)
// 注册Pinia状态管理
app.use(createPinia())
app.use(BaiduCalendar)
// 配置Ant Design Vue
app.use(ConfigProvider)
// 全局提供dayjs
app.provide('dayjs', dayjs)
// 注册自定义指令
app.directive('text-scale', textScale)

app.mount('#app')
