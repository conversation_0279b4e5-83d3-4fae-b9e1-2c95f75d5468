<template>
  <div ref="timelineContainerRef" class="timeline-container" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="timeline-header">
      <h2 class="timeline-header__title"><PERSON></h2>
      <h3 class="timeline-header__subtitle">FATHER OF THE TURKS</h3>
    </div>
    <div class="timeline">
      <div 
        v-for="(item, index) in timelineItems" 
        :key="index" 
        :ref="el => { if (el) timelineItemRefs[index] = el }"
        class="timeline-item" 
        :data-text="item.dataText"
        :class="{ 'timeline-item--active': activeIndex === index }"
      >
        <div class="timeline__content">
          <img class="timeline__img" :src="item.imgSrc" :alt="item.imgAlt">
          <h2 class="timeline__content-title">{{ item.title }}</h2>
          <p class="timeline__content-desc" v-html="item.desc"></p>
        </div>
      </div>
    </div>
  </div>
  <div class="demo-footer">
    <a href="http://www.turkishnews.com/Ataturk/life.htm" target="_blank">Source/Kaynak</a>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

const timelineContainerRef = ref(null);
const timelineItemRefs = ref([]);
const activeIndex = ref(0);
const backgroundImage = ref('');

const timelineItems = ref([
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_3t/23231.jpg',
        imgAlt: 'Historical portrait 1881',
        title: '1881',
        desc: 'He was born in 1881 (probably in the spring) in Salonica, then an Ottoman city, now inGreece. His father Ali Riza, a customs official turned lumber merchant, died when Mustafawas still a boy. His mother Zubeyde, adevout and strong-willed woman, raised him and his sister.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23270.jpg',
        imgAlt: 'Historical portrait 1893',
        title: '1893',
        desc: 'First enrolled in a traditionalreligious school, he soon switched to a modern school. In 1893, he entered a military highschool where his mathematics teacher gave him the second name Kemal (meaning perfection)in recognition of young Mustafa\'s superior achievement.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23275.jpg',
        imgAlt: 'Historical portrait 1905',
        title: '1905',
        desc: 'In 1905, Mustafa Kemal graduated from the War Academy in Istanbul with the rank of Staff Captain. Posted in Damascus, he started with several colleagues, a clandestinesociety called "Homeland and Freedom" to fight against the Sultan\'sdespotism.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23161.jpg',
        imgAlt: 'Historical portrait 1908',
        title: '1908',
        desc: 'In 1908 he helped the group of officers who toppled the Sultan. Mustafa Kemal\'scareer flourished as he won his heroism in the far corners of the Ottoman Empire,including Albania and Tripoli. He also briefly served as a staff officer in Salonica andIstanbul and as a military attache in Sofia.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23272.jpg',
        imgAlt: 'Historical portrait 1915',
        title: '1915',
        desc: 'In 1915, when Dardanelles campaign was launched, Colonel Mustafa Kemal became anational hero by winning successive victories and finally repelling the invaders.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23245.png',
        imgAlt: 'Historical portrait 1916',
        title: '1916',
        desc: 'Promotedto general in 1916, at age 35, he liberated two major provinces in eastern Turkey thatyear. In the next two years, he served as commander of several Ottoman armies inPalestine, Aleppo, and elsewhere, achieving another major victory by stopping the enemyadvance at Aleppo.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23166.jpg',
        imgAlt: 'Historical portrait 1919',
        title: '1919',
        desc: 'On May 19, 1919, Mustafa Kemal Pasha landed in the Black Sea port of Samsun to startthe War of Independence. In defiance of the Sultan\'s government, he rallied a liberationarmy in Anatolia and convened the Congress of Erzurum and Sivas which established thebasis for the new national effort under his leadership.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23278.jpg',
        imgAlt: 'Historical portrait 1920',
        title: '1920',
        desc: 'On April 23, 1920, the GrandNational Assembly was inaugurated. Mustafa Kemal Pasha was elected to its Presidency. Fighting on many fronts, he led his forces to victory against rebels and invadingarmies. Following the Turkish triumph at the two major battles at Inonu in Western Turkey,the Grand National Assembly conferred on Mustafa Kemal Pasha the title ofCommander-in-Chief with the rank of Marshal.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23236.jpg',
        imgAlt: 'Historical portrait 1922',
        title: '1922',
        desc: 'At the end of August 1922, the Turkish armieswon their ultimate victory. Within a few weeks, the Turkish mainland was completelyliberated, the armistice signed, and the rule of the Ottoman dynasty abolished'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23096.jpg',
        imgAlt: 'Historical portrait 1923',
        title: '1923',
        desc: 'In July 1923, the national government signed the Lausanne Treaty with Great Britain,France, Greece, Italy, and others. In mid-October, Ankara became the capital of the new Turkish State. On October 29, the Republic was proclaimed and Mustafa Kemal Pasha wasunanimously elected President of the Republic.'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23240.jpg',
        imgAlt: 'Historical portrait 1934',
        title: '1934',
        desc: 'The account of Atatürk\'s fifteen year Presidency is a saga of dramatic modernization.With indefatigable determination, he created a new political and legal system, abolished the Caliphate and made both government and education secular, gave equal rights to women,changed the alphabet and the attire, and advanced the arts and the sciences, agricultureand industry. In 1934, when the surname law was adopted, the national parliament gave him the name <i><strong>"Atatürk" (Father of the Turks)</strong></i>'
    },
    {
        dataText: 'FATHER OF THE TURKS',
        imgSrc: 'https://4kwallpapers.com/images/walls/thumbs_2t/23178.jpg',
        imgAlt: 'Historical portrait 1938',
        title: '1938',
        desc: 'On November 10, 1938, following an illness of a few months, the national liberator and the Father of modern Turkey died. But his legacy to his people and to the world endures.'
    }
]);

const handleScroll = () => {
    // 使用容器的scrollTop而不是window.scrollY
    const pos = timelineContainerRef.value ? timelineContainerRef.value.scrollTop : 0;
    const items = timelineItemRefs.value;
    console.log('🔄 容器滚动事件触发 - 滚动位置:', pos, '元素数量:', items?.length || 0);

    if (!items || items.length === 0) {
        console.log('❌ 没有找到timeline元素或元素数量为0');
        return;
    }

    const itemLength = items.length;

    items.forEach((item, i) => {
        if (!item) {
            console.log(`❌ 元素 ${i} 为空`);
            return;
        }
        const min = item.offsetTop;
        const max = item.offsetHeight + item.offsetTop;

        console.log(`📍 元素 ${i}: offsetTop=${min}, height=${item.offsetHeight}, max=${max}, 当前滚动=${pos}`);

        // Special case for the last item
        if (i === itemLength - 2 && pos > min + item.offsetHeight / 2) {
            console.log(`🎯 激活最后一个元素 (${itemLength - 1})`);
            if (activeIndex.value !== itemLength - 1) {
                activeIndex.value = itemLength - 1;
                backgroundImage.value = timelineItems.value[itemLength - 1].imgSrc;
                console.log('🖼️ 背景图切换到:', backgroundImage.value);
            }
        } else if (pos <= max - 40 && pos >= min) {
            console.log(`🎯 激活元素 ${i}`);
            if (activeIndex.value !== i) {
                activeIndex.value = i;
                backgroundImage.value = timelineItems.value[i].imgSrc;
                console.log('🖼️ 背景图切换到:', backgroundImage.value);
            }
        }
    });
};

onMounted(() => {
    // Set initial state
    if (timelineItems.value.length > 0) {
        activeIndex.value = 0;
        backgroundImage.value = timelineItems.value[0].imgSrc;
        console.log('🚀 初始化完成 - 数据项数量:', timelineItems.value.length);
        console.log('🖼️ 初始背景图:', backgroundImage.value);
    }

    // 监听容器的滚动事件，而不是window的滚动事件
    if (timelineContainerRef.value) {
        timelineContainerRef.value.addEventListener('scroll', handleScroll);
        console.log('👂 容器滚动事件监听器已添加');
    }

    // Use nextTick to ensure refs are populated before calculating positions
    nextTick(() => {
        console.log('🔍 检查元素引用 - timelineItemRefs数量:', timelineItemRefs.value.length);
        timelineItemRefs.value.forEach((ref, index) => {
            if (ref) {
                console.log(`✅ 元素 ${index}: offsetTop=${ref.offsetTop}, height=${ref.offsetHeight}`);
            } else {
                console.log(`❌ 元素 ${index}: 引用为空`);
            }
        });
        handleScroll();
    });
});

onUnmounted(() => {
    // 移除容器的滚动事件监听器
    if (timelineContainerRef.value) {
        timelineContainerRef.value.removeEventListener('scroll', handleScroll);
    }
});

</script>

<style>
@import url('https://fonts.googleapis.com/css?family=Cardo|Pathway+Gothic+One');

/* 重置全局样式，确保test页面能正常滚动 */
html, body {
    margin: 0 !important;
    padding: 0 !important;
    height: auto !important;
    min-height: 100vh !important;
    display: block !important;
    place-items: unset !important;
}

#app {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    height: auto !important;
}

/* Timeline Styles */
.timeline {
    display: flex;
    margin: 0 auto;
    flex-wrap: wrap;
    flex-direction: column;
    max-width: 700px;
    position: relative;
}

.timeline:before {
    position: absolute;
    left: 50%;
    width: 2px;
    height: 100%;
    margin-left: -1px;
    content: "";
    background: rgba(255, 255, 255, .07);
}

@media only screen and (max-width: 767px) {
    .timeline:before {
        left: 40px;
    }
}

.timeline__content-title {
    font-weight: normal;
    font-size: 66px;
    margin: -10px 0 0 0;
    transition: .4s;
    padding: 0 10px;
    box-sizing: border-box;
    font-family: 'Pathway Gothic One', sans-serif;
    color: #fff;
}

.timeline__content-desc {
    margin: 0;
    font-size: 15px;
    box-sizing: border-box;
    color: rgba(255, 255, 255, .7);
    font-family: Cardo;
    font-weight: normal;
    line-height: 25px;
}

.timeline-item {
    padding: 40px 0;
    opacity: .3;
    filter: blur(2px);
    transition: .5s;
    box-sizing: border-box;
    width: calc(50% - 40px);
    display: flex;
    position: relative;
    transform: translateY(-80px);
}

.timeline-item:before {
    content: attr(data-text);
    letter-spacing: 3px;
    width: 100%;
    position: absolute;
    color: rgba(255, 255, 255, .5);
    font-size: 13px;
    font-family: 'Pathway Gothic One', sans-serif;
    border-left: 2px solid rgba(255, 255, 255, .5);
    top: 70%;
    margin-top: -5px;
    padding-left: 15px;
    opacity: 0;
    right: calc(-100% - 39px);
}

.timeline-item:nth-child(even) {
    align-self: flex-end;
}

.timeline-item:nth-child(even):before {
    right: auto;
    text-align: right;
    left: calc(-100% - 39px);
    padding-left: 0;
    border-left: none;
    border-right: 2px solid rgba(255, 255, 255, .5);
    padding-right: 15px;
}

.timeline-item--active {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0px);
}

.timeline-item--active:before {
    top: 50%;
    transition: .3s all .2s;
    opacity: 1;
}

.timeline-item--active .timeline__content-title {
    margin: -50px 0 20px 0;
}

@media only screen and (max-width: 767px) {
    .timeline-item {
        align-self: baseline !important;
        width: 100%;
        padding: 0 30px 150px 80px;
    }

    .timeline-item:before {
        left: 10px !important;
        padding: 0 !important;
        top: 50px;
        text-align: center !important;
        width: 60px;
        border: none !important;
    }

    .timeline-item:last-child {
        padding-bottom: 40px;
    }
}

.timeline__img {
    max-width: 100%;
    box-shadow: 0 10px 15px rgba(0, 0, 0, .4);
}

.timeline-container {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    padding: 80px 0;
    transition: .3s ease 0s;
    background-attachment: fixed;
    background-size: cover;
    /* 启用容器滚动 */
    overflow: auto;
    display: block;
}

.timeline-container:before {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(99, 99, 99, 0.8);
    content: "";
}

.timeline-header {
    width: 100%;
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.timeline-header__title {
    color: #fff;
    font-size: 46px;
    font-family: Cardo;
    font-weight: normal;
    margin: 0;
}

.timeline-header__subtitle {
    color: rgba(255, 255, 255, .5);
    font-family: 'Pathway Gothic One', sans-serif;
    font-size: 16px;
    letter-spacing: 5px;
    margin: 10px 0 0 0;
    font-weight: normal;
}

.demo-footer {
    padding: 60px 0;
    text-align: center;
    background-color: #222; /* Added a background for visibility */
}

.demo-footer a {
    color: #999;
    display: inline-block;
    font-family: Cardo;
}
</style>
