:root {
  --icon-transition-duration: 0.3s;
}
.home-container {
  display: flex;
  flex-direction: column;
  //max-width: 1000px;
  margin: 0 auto;
  // gap: 20px;
  height: 100vh;
  box-sizing: border-box;
  overflow: hidden; /* 防止出现滚动条 */
  user-select: none; /* 防止双击选中文本和图标 */
  @media (max-width: 768px) {
    padding: 10px;
    gap: 15px;
  }
  
  @media (max-width: 480px) {
    padding: 8px;
    gap: 10px;
  }
}

.time-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  margin-top: 50px;
  .time {
    font-size: 2.8rem;
    @media (max-width: 768px) {
      font-size: 2rem;
    }
    
    @media (max-width: 480px) {
      font-size: 1.6rem;
    }
  }
  .date {
    font-size: 1rem;
    color: var(--text-secondary);
  }
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  h2 {
    font-size: 1.5rem;
    font-weight: 500;
    margin: 0;
    min-width: 80px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .category-icon {
      font-size: 1.3rem;
      display: inline-block;
    }
  }
  
  /* 标题淡入淡出动画 */
  .title-fade-enter-active,
  .title-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  .title-fade-enter-from,
  .title-fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.category-tabs {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  position: relative;
  overflow-x: auto;
  padding-bottom: 5px;
  width: 100%;
  
  @media (max-width: 480px) {
    gap: 5px;
  }
  
  &::-webkit-scrollbar {
    height: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 3px;
  }
  
  .category-tab {
    padding: 6px 12px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    font-size: 14px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    flex-shrink: 0;
    
    @media (max-width: 480px) {
      padding: 5px 10px;
      font-size: 12px;
    }
    
    .category-tab-icon {
      display: inline-flex;
      font-size: 1.1rem;
      
      @media (max-width: 480px) {
        font-size: 1rem;
      }
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--accent-color);
      border-radius: 20px;
      transform: scale(0);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      z-index: -1;
    }
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.active {
      background: transparent;
      color: white;
      box-shadow: 0 4px 12px rgba(var(--accent-color-rgb), 0.4);
      transform: translateY(-2px);
      
      &::before {
        transform: scale(1);
        opacity: 1;
      }
    }
  }
}

/* 应用网格区域 */
.apps-section {
  width: 100%;
  position: relative;
  flex: 1;
  overflow: hidden;
  transition: padding-right 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.grid-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.app-grid {
  overflow: hidden; /* 隐藏滚动条 */
  // -ms-overflow-style: none !important; /* IE和Edge */
  // scrollbar-width: none !important; /* Firefox */
  
  /* 以下样式用于在不显示滚动条的情况下仍然能滚动 */
  -webkit-overflow-scrolling: touch; /* Safari和Chrome */
  overflow-y: auto; /* 允许应用网格内部滚动 */
  display: grid;
  width: 100%;
  flex: 1;
  position: relative;
  grid-auto-flow: dense;
  justify-content: start; /* 改为左对齐，配合margin: 0 auto使整体居中 */
  height: 100%;
  // max-height: calc(100vh - 320px); /* 留出顶部和底部的空间 */
  padding-top: 10px !important;
  margin: 0 auto; /* 整体居中 */
  // min-height: 500px;
  /* 添加容器自身的触摸事件处理 */
  touch-action: pan-y;
  @media (max-width: 768px) {
    max-height: calc(100vh - 200px);
    justify-content: center; /* 小屏幕时居中显示 */
  }
  
  &::-webkit-scrollbar {
    display: none;
  }
}

/* 解决内部组件滚动冲突 */
.app-item {
  position: relative;
  transition: transform var(--icon-transition-duration), width 0.8s, height 0.8s;
  will-change: transform, width, height;
  /* 使内部组件在触摸时可以独立滚动 */
  touch-action: auto;
  border-radius: 10px;
  &.hidden {
    display: none;
  }
  
  /* 特殊处理带滚动条的卡片组件，防止滚动事件冒泡 */
  .hotnet-container,
  .video-card,
  [class*="card-component"] {
    overflow-y: auto;
    overscroll-behavior: contain; /* 防止滚动传递 */
  }
  .video-card{
      opacity: 0.8;
  }
  .image-card{
    opacity: 0.8;
  }
}

.app-item.size-changing {
  z-index: 10;
  box-shadow: 0 0 20px rgba(var(--accent-color-rgb, 0, 120, 212), 0.4);
}


.sortable-fallback {
  opacity: 0.8 !important;
  transform: rotate(1deg) scale(1.05) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  pointer-events: none !important;
  will-change: transform, box-shadow !important;
}

/* 不同尺寸的卡片 */
.size-1x1 {
  grid-column: span 1;
  grid-row: span 1;
}
.size-1x2 {
  grid-column: span 1;
  grid-row: span 2;
}
.size-1x3 {
  grid-column: span 1;
  grid-row: span 3;
}
.size-1x4 {
  grid-column: span 1;
  grid-row: span 4;
}
.size-1x5 {
  grid-column: span 1;
  grid-row: span 5;
}

.size-2x1 {
  grid-column: span 2;
  grid-row: span 1;
}
.size-2x1 .app-icon img{
  object-fit: cover !important;
}

.size-2x2 {
  grid-column: span 2;
  grid-row: span 2;
}
.size-2x3{
  grid-column: span 2;
  grid-row: span 3;
}
.size-2x4{
  grid-column: span 2;
  grid-row: span 4;
}
.size-2x5{
  grid-column: span 2;
  grid-row: span 5;
}
.size-3x1 {
  grid-column: span 3;
  grid-row: span 1;
}

.size-3x2 {
  grid-column: span 3;
  grid-row: span 2;
}
.size-3x3{
  grid-column: span 3;
  grid-row: span 3;
}
.size-3x4{
  grid-column: span 3;
  grid-row: span 4;
}
.size-3x5{
  grid-column: span 3;
  grid-row: span 5;
}
.size-4x1{
  grid-column: span 4;
  grid-row: span 1;
}
.size-4x2{
  grid-column: span 4;
  grid-row: span 2;
}
.size-4x3{
  grid-column: span 4;
  grid-row: span 3;
}
.size-4x4{
  grid-column: span 4;
  grid-row: span 4;
}
.size-4x5{
  grid-column: span 4;
  grid-row: span 5;
}
.size-4x6{
  grid-column: span 4;
  grid-row: span 6;
}
.size-6x8{
  grid-column: span 6;
  grid-row: span 8;
}
.size-10x6{
  grid-column: span 10;
  grid-row: span 6;
}
.size-5x8{
  grid-column: span 5;
  grid-row: span 8;
}
.size-8x5{
  grid-column: span 8;
  grid-row: span 5;
}
.size-8x6{
  grid-column: span 8;
  grid-row: span 6;
}
.size-7x8{
  grid-column: span 7;
  grid-row: span 8;
}
.size-8x7{
  grid-column: span 8;
  grid-row: span 7;
}
.size-8x8{
  grid-column: span 8;
  grid-row: span 8;
}
.size-5x3{
  grid-column: span 5;
  grid-row: span 3;
}

.size-6x4{
  grid-column: span 6;
  grid-row: span 4;
}


/* 应用卡片样式 */
.app-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 100%;
  cursor: grab;
  will-change: transform;
  position: relative;
  // overflow: hidden;
  border-radius: 10px;
  transition: transform 0.15s, opacity 0.15s !important;
  &:active {
    cursor: grabbing;
    transform: scale(0.98);
  }
}

.app-card .app-icon {
  flex: 1;
  display: flex;
  align-items: center;
  border-radius: 15px;
  width: 100%;
  justify-content: center;
  max-height: 100%;
}

.app-name {
  font-size: 12px !important;
  font-weight: unset !important;
  text-align: center;
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ffffff !important;
  @media (max-width: 480px) {
    font-size: 0.8rem;
    bottom: -18px;
  }
}

.app-description {
  font-size: 10px;
  text-align: center;
  color: #666;
  margin-top: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 100%;
  padding: 0 5px;
  
  @media (max-width: 480px) {
    font-size: 9px;
    -webkit-line-clamp: 1;
  }
}

/* 卡片组件样式 */
.card-component {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
  will-change: transform;
  overflow-y: hidden !important;
  &:active {
    cursor: grabbing;
  }
}

.placeholder-card, .placeholder-text, .card-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 16px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 16px;
  
  @media (max-width: 480px) {
    font-size: 14px;
  }
}

/* FLIP动画效果 */
.flip-list-move {
  transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.flip-list-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.flip-list-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.9);
  position: absolute;
}

:global(body.dragging) {
  cursor: grabbing;
}

/* 右键菜单样式已移至ContextMenu组件 */

/* 自定义卡片包装器 */
.custom-component-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 纯净模式相关样式 */
.pure-mode {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  user-select: none; /* 防止选中文字 */
}

.time-section-pure {
  margin-top: 60px;
  margin-bottom: 20px;
  
  .time {
    font-size: 6rem;
    font-weight: 200;
    
    @media (max-width: 768px) {
      font-size: 4.5rem;
    }
    
    @media (max-width: 480px) {
      font-size: 3.5rem;
    }
  }
}

.search-pure {
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform: scale(1.1);
  max-width: 60%;
}

/* 提示用户可以双击切换的样式 */
.home-container::before {
  // content: '';
  // position: absolute;
  // top: 10px;
  // right: 10px;
  // width: 10px;
  // height: 10px;
  // border-radius: 50%;
  // background-color: var(--accent-color, rgba(255, 255, 255, 0.2));
  // opacity: 0.6;
  // transition: all 0.3s;
}

.home-container:hover::before {
  opacity: 1;
  transform: scale(1.2);
}

/* 防止时间区域文字被选中 */
.time-section {
  user-select: none;
}

/* 编辑模式样式 */
.edit-mode .app-item {
  position: relative !important;
}

.delete-icon {
  position: absolute;
  top: -8px;
  right: 0px;
  width: 22px;
  height: 22px;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, background-color 0.2s;
}

.delete-icon:hover {
  transform: scale(1.2);
  background-color: #ff0000;
}

/* 卡片组件在编辑模式下的样式 */
.edit-mode .app-card,
.edit-mode .card-component {
  animation: none !important; /* 停止其他动画 */
  cursor: default;
}

.edit-mode .app-item.shake-animation {
  animation: shake 0.5s ease-in-out infinite !important; /* 重要：强制使用摇晃动画 */
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 纯净模式控制按钮组 */
.pure-mode-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1001;
  animation: fadeIn 0.5s;
}

/* 纯净模式按钮基础样式 */
.pure-mode-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}



.exit-pure-mode-button .i-carbon-view {
  font-size: 24px;
}

/* 底部Dock栏样式已移至DockBar.vue组件 */

/* 添加自定义图标表单样式 */
.add-icon-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 500;
  font-size: 14px;
  color: var(--text-primary);
}

.form-tip {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.color-picker-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 66, 133, 244), 0.3);
    transform: scale(1.1);
  }
}

.size-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 文件夹卡片样式 */
.folder-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(24,63,80,0.25);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.folder-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}


.folder-top {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.folder-icon {
  font-size: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 新增 - 文件夹自定义图标 */
.folder-custom-icon {
  width: 80%;
  height: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.folder-custom-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

/* 优化 - 文件夹内应用网格 */
.folder-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 6px;
  width: 85%;
  height: 85%;
  padding: 0px !important;
  place-items: center;
  place-content: start;
  box-sizing: border-box;
}

.folder-grid-item {
  width: 100%;
  height: 100%;
  // width: 34px;
  // height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.folder-grid-item:hover {
  transform: scale(1.05);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.folder-grid-item img {
  width: 85%;
  height: 85%;
  object-fit: contain;
  border-radius: 4px;
}

.folder-grid-item .app-icon-div {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.folder-grid-item .app-icon-text {
  font-size: 10px;
  font-weight: 600;
  color: #333;
  text-align: center;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.folder-app-item {
  z-index: 1;
}

/* 文件夹拖拽样式 */
.folder-drag-over {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(245, 186, 66, 0.8);
  border: 2px dashed #f5ba42;
  background-color: rgba(245, 186, 66, 0.3);
}

/* 文件夹模态框样式 */
.folder-modal-content {
  padding: 20px;
  min-height: 300px;
  background-color: #fafafa;
  border-radius: 12px;
}

.folder-modal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 16px;
}

.folder-app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 12px;
  border-radius: 12px;
}

.folder-app-item:hover {
  transform: translateY(-5px);
}

.folder-app-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  background-color: #f7f7f7;
}

.folder-app-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.folder-app-name {
  font-size: 13px;
  text-align: center;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.empty-folder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.empty-folder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #e0e0e0;
}

.empty-folder-text {
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.empty-folder-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
}
.newWindow{
  position: absolute;
  background: rgba($color: #ffffff, $alpha: 0.5);
  padding: 5px;
  right: 0px;
  top: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 滑动切换分类提示 */
.slide-hint-container {
  position: fixed;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  pointer-events: none;
  width: 100%;
  display: flex;
  justify-content: center;
}

.slide-hint {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  animation: slide-in-fade 0.3s forwards;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes slide-in-fade {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 媒体查询适配移动端 */
@media (max-width: 480px) {
  .slide-hint {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .slide-hint-container {
    bottom: 100px;
  }
}

/* 分类切换动画 */
.slide-up-animation {
  animation: slideUp 0.4s ease-in-out;
}

.slide-down-animation {
  animation: slideDown 0.4s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-30px);
    opacity: 0;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(30px);
    opacity: 0;
  }
}

/* 图标动画定义 */
.animate-bounce {
  animation: bounce 1s ease infinite !important;
}

.animate-rotate {
  animation: rotate 1.5s linear infinite !important;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite !important;
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(.36, .07, .19, .97) infinite !important;
}

.animate-swing {
  transform-origin: center top;
  animation: swing 1.5s ease infinite !important;
}

/* 图标动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-15px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  20%,
  60% {
    transform: translateX(-5px);
  }

  40%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes swing {

  0%,
  100% {
    transform: rotate(0deg);
  }

  20% {
    transform: rotate(15deg);
  }

  40% {
    transform: rotate(-10deg);
  }

  60% {
    transform: rotate(5deg);
  }

  80% {
    transform: rotate(-5deg);
  }
}



/* 卡片动画关键帧 */
@keyframes card-fade-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-fade-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-zoom-in {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes card-flip {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }

  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

@keyframes card-rotate {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }

  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

@keyframes card-reveal {
  from {
    opacity: 0;
    transform: scaleY(0);
  }

  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes card-glitch {
  0% {
    opacity: 0;
    transform: translate(0);
    clip-path: inset(100% 0 0 0);
  }

  20% {
    opacity: 1;
    transform: translate(-5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }

  40% {
    transform: translate(5px, -5px);
    clip-path: inset(40% 0 30% 0);
  }

  60% {
    transform: translate(5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }

  80% {
    transform: translate(-5px, -5px);
    clip-path: inset(20% 0 20% 0);
  }

  100% {
    transform: translate(0);
    opacity: 1;
    clip-path: inset(0);
  }
}

@keyframes card-pulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0.5);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 0 15px rgba(var(--accent-color-rgb), 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0);
  }
}

/* 全局纯净模式样式 - 隐藏侧边栏 */
/* body.pure-mode-active .side-navigation,
body.pure-mode-active header,
body.pure-mode-active .fixed-logo-container {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
} */

/* 全局编辑模式摇晃样式 - 确保最高优先级 */
.edit-mode .app-item * {
  pointer-events: none !important;
}

.edit-mode .app-item .delete-icon {
  pointer-events: auto !important;
}

:deep(.ant-modal) {
  top: 0% !important;
}

/* 让模态框居中显示 */
.ant-modal-centered .ant-modal {
  top: 50% !important;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%) !important;
}

/* 编辑模式摇晃动画 - 直接定义，确保最高优先级 */
@keyframes shake-edit {
  0% {
    transform: rotate(0) scale(1);
  }

  20% {
    transform: rotate(-1deg) scale(1);
  }

  60% {
    transform: rotate(0) scale(1);
  }

  80% {
    transform: rotate(1deg) scale(1);
  }

  100% {
    transform: rotate(0) scale(1);
  }
}

/* 编辑模式下强制覆盖其他动画 */
.edit-mode .app-grid .app-item {
  animation: shake-edit ease .3s infinite !important;
  transform-origin: center !important;
  perspective: 1000px !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  /* transition: none !important;
  transform: none !important; */
}

/* 禁用其他所有动画，但允许拖动 */
.edit-mode .app-grid .app-item * {
  /* animation: none !important;
  transition: none !important; */
  pointer-events: auto !important;
  /* 修改：允许事件交互 */
}

/* 确保删除按钮可以点击 */
.edit-mode .delete-icon {
  pointer-events: auto !important;
}

/* 确保卡片内容可以拖动 */
.edit-mode .app-card,
.edit-mode .card-component {
  pointer-events: auto !important;
}

.wallpaper-button .i-carbon-image {
  font-size: 24px;
}

/* 纯净模式动画效果 - 全局样式 */
.pure-mode-animation {
  transform: translateY(200px) !important;
  // transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

