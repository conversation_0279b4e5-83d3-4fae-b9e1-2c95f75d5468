<template>
  <div class="multi-iframe-page">
    <!-- 页面标题和控制栏 -->
    <div class="page-header">
      <img  src="../assets/office/sideBar.png" style="width: 40px; height: 40px;" @click="goLinkFun" />
      <div class="layout-controls">
        <div class="layout-buttons">
          <button
            v-for="layout in layoutOptions"
            :key="layout.type"
            :class="['layout-btn', { active: currentLayout === layout.type }]"
            @click="changeLayout(layout.type)"
            :title="layout.label"
          >
            <div :class="['layout-preview', `layout-${layout.type}`]">
              <div v-for="i in layout.count" :key="i" class="preview-card"></div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 卡片容器 -->
    <div class="cards-container" :class="[`layout-${currentLayout}`]">
      <div 
        v-for="(card, index) in visibleCards" 
        :key="card.id"
        class="card-wrapper"
      >
        <EnhancedIframeCard
          :card-id="card.id"
          :url="card.url"
          :title="card.title"
          @url-change="handleUrlChange(index, $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import EnhancedIframeCard from '@/components/iframe/EnhancedIframeCard.vue'

// 布局选项配置
const layoutOptions = [
  { type: '2x1', label: '', count: 2 },
  { type: '3x1', label: '', count: 3 },
  { type: '4x1', label: '', count: 4 },
  { type: '2x2', label: '', count: 4 }
]

// 当前布局类型
const currentLayout = ref('2x1')

// 卡片数据
const cards = ref([
  { id: 'card-1', url: '', title: '窗口 1' },
  { id: 'card-2', url: '', title: '窗口 2' },
  { id: 'card-3', url: '', title: '窗口 3' },
  { id: 'card-4', url: '', title: '窗口 4' }
])

// 根据当前布局显示的卡片
const visibleCards = computed(() => {
  const layoutCounts = {
    '2x1': 2,
    '3x1': 3,
    '4x1': 4,
    '2x2': 4
  }
  return cards.value.slice(0, layoutCounts[currentLayout.value])
})

// 更改布局
const changeLayout = (layoutType) => {
  currentLayout.value = layoutType
  saveConfig()
}

// 处理URL变化
const handleUrlChange = (index, newUrl) => {
  cards.value[index].url = newUrl
  saveConfig()
}

// 保存配置到本地存储
const saveConfig = () => {
  const config = {
    layoutType: currentLayout.value,
    cards: cards.value
  }
  localStorage.setItem('multi-iframe-page-config', JSON.stringify(config))
}

const goLinkFun = () => {
  window.open('https://www.linkfun.fun', '_blank')
}

// 加载配置
const loadConfig = () => {
  try {
    const savedConfig = localStorage.getItem('multi-iframe-page-config')
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      currentLayout.value = config.layoutType || '2x2'
      if (config.cards && Array.isArray(config.cards)) {
        // 合并保存的配置和默认配置
        config.cards.forEach((savedCard, index) => {
          if (cards.value[index]) {
            cards.value[index] = { ...cards.value[index], ...savedCard }
          }
        })
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})
</script>

<style lang="scss" scoped>
.multi-iframe-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  padding: 10px;
  box-sizing: border-box;
  position: absolute;
  z-index: 999;
}

.page-header {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.page-title {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.layout-controls {
  display: flex;
  align-items: center;
}

.layout-buttons {
  display: flex;
  gap: 12px;
}

.layout-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  // border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  &.active {
    background: rgba(113, 198, 255, 0.3);
    border-color: #71C6FF;
  }
}

.layout-preview {
  display: grid;
  gap: 2px;
  width: 32px;
  height: 18px;

  &.layout-2x1 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-3x1 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-4x1 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
}

.preview-card {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 1px;
}

.layout-label {
  font-size: 12px;
  white-space: nowrap;
}

.cards-container {
  flex: 1;
  display: grid;
  gap: 10px;
  min-height: 0;

  &.layout-2x1 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-3x1 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-4x1 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.layout-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
}

.card-wrapper {
  min-height: 0;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .multi-iframe-page {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .layout-buttons {
    gap: 8px;
  }
  
  .layout-btn {
    padding: 6px 8px;
  }
  
  .cards-container {
    gap: 12px;

    &.layout-2x1,
    &.layout-3x1,
    &.layout-4x1,
    &.layout-2x2 {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(auto-fit, 1fr);
    }
  }
}
</style>
