import { DatePicker } from 'ant-design-vue';
import dayjs from '../utils/dayjs';

// 为DatePicker配置默认属性
export default function setupAntd() {
  // 为DatePicker设置全局配置
  DatePicker.props.dayjs = { 
    type: Function,
    default: () => dayjs 
  };
  
  // 确保DatePicker和TimePicker使用我们配置的dayjs
  const generateConfig = {
    // 提供dayjs实例
    getNow: () => dayjs(),
    getFixedDate: (string) => dayjs(string),
    getEndDate: (date) => date.endOf('month'),
    
    // 格式化相关
    getWeekDay: (date) => date.day(),
    getYear: (date) => date.year(),
    getMonth: (date) => date.month(),
    getDate: (date) => date.date(),
    getHour: (date) => date.hour(),
    getMinute: (date) => date.minute(),
    getSecond: (date) => date.second(),
    
    // 转换相关
    addYear: (date, diff) => date.add(diff, 'year'),
    addMonth: (date, diff) => date.add(diff, 'month'),
    addDate: (date, diff) => date.add(diff, 'day'),
    setYear: (date, year) => date.set('year', year),
    setMonth: (date, month) => date.set('month', month),
    setDate: (date, num) => date.set('date', num),
    setHour: (date, hour) => date.set('hour', hour),
    setMinute: (date, minute) => date.set('minute', minute),
    setSecond: (date, second) => date.set('second', second),
    
    // 比较相关
    isAfter: (date1, date2) => date1.isAfter(date2),
    isValidate: (date) => date.isValid(),
    locale: {
      getWeekFirstDay: (locale) => {
        const localeData = dayjs().locale(locale).localeData();
        return localeData.firstDayOfWeek();
      },
      getWeekFirstDate: (locale, date) => {
        return date.locale(locale).weekday(0);
      },
      getWeek: (locale, date) => {
        return date.locale(locale).week();
      },
      format: (locale, date, format) => {
        return date.locale(locale).format(format);
      },
      parse: (locale, text, formats) => {
        for (let i = 0; i < formats.length; i++) {
          const date = dayjs(text, formats[i], locale, true);
          if (date.isValid()) {
            return date;
          }
        }
        return null;
      },
    },
  };

  // 设置生成配置
  if (DatePicker.generateConfig) {
    Object.assign(DatePicker.generateConfig, generateConfig);
  } else {
    DatePicker.generateConfig = generateConfig;
  }
} 