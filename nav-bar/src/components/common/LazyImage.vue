<template>
  <div class="lazy-image-container" ref="containerRef">
    <!-- 占位符 - 未进入视口时显示 -->
    <div v-if="!hasStartedLoading" class="lazy-placeholder">
      <div class="skeleton-loader"></div>
    </div>

    <!-- 加载中状态 - 已开始加载但未完成时显示 -->
    <div v-else-if="hasStartedLoading && !imageLoaded && !imageError" class="lazy-loading">
      <div class="loading-spinner"></div>
    </div>

    <!-- 加载失败状态 -->
    <div v-else-if="imageError" class="lazy-error" @click="retryLoad">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <span>加载失败，点击重试</span>
    </div>

    <!-- 实际图片 - 一旦开始加载就设置src，加载完成后显示 -->
    <img
      v-if="hasStartedLoading"
      :src="currentSrc"
      :alt="alt"
      :loading="loading"
      :class="['lazy-image', { 'fade-in': imageLoaded }]"
      @load="handleImageLoad"
      @error="handleImageError"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: ''
  },
  rootMargin: {
    type: String,
    default: '100px'
  },
  threshold: {
    type: Number,
    default: 0.1
  },
  loading: {
    type: String,
    default: 'lazy'
  }
})

const emit = defineEmits(['load', 'error'])

// 状态管理
const containerRef = ref(null)
const imageLoaded = ref(false)
const imageError = ref(false)
const hasStartedLoading = ref(false)
const retryCount = ref(0)
const maxRetries = 3

// 当前图片src
const currentSrc = ref('')

// IntersectionObserver 相关状态
const isIntersecting = ref(false)
const observerInstance = ref(null)

// 初始化 Intersection Observer
const initIntersectionObserver = () => {
  if (!containerRef.value) {
    console.warn('LazyImage: containerRef 不存在，无法初始化 IntersectionObserver')
    return
  }

  try {
    // 使用 @vueuse/core 的 useIntersectionObserver
    const { stop } = useIntersectionObserver(
      containerRef,
      (entries) => {
        // 直接在回调中处理加载逻辑
        const entry = entries[0]
        if (entry) {
          if (entry.isIntersecting) {
            startLoading()
          }
        }
      },
      {
        rootMargin: props.rootMargin,
        threshold: props.threshold
      }
    )

    // 保存 stop 函数用于清理
    observerInstance.value = { stop }

    // 主动检查初始状态
    nextTick(() => {
      checkInitialVisibility()
    })

  } catch (error) {
    console.error('LazyImage: useIntersectionObserver 初始化失败:', error)

    // 降级方案：直接加载图片
    startLoading()
  }
}

// 主动检查元素是否在视口内
const checkInitialVisibility = () => {
  if (!containerRef.value) {
    console.warn('LazyImage: containerRef 不存在，无法检查初始可见性')
    return
  }

  try {
    const rect = containerRef.value.getBoundingClientRect()
    const windowHeight = window.innerHeight || document.documentElement.clientHeight
    const windowWidth = window.innerWidth || document.documentElement.clientWidth

    // 检查元素是否在视口内（考虑rootMargin）
    const rootMarginValue = parseInt(props.rootMargin) || 100
    const isVisible = (
      rect.top < windowHeight + rootMarginValue &&
      rect.bottom > -rootMarginValue &&
      rect.left < windowWidth + rootMarginValue &&
      rect.right > -rootMarginValue
    )


    if (isVisible) {
      startLoading()
    }
  } catch (error) {
    console.error('LazyImage: 检查初始可见性失败:', error)
    // 如果检查失败，为了保险起见直接开始加载
    startLoading()
  }
}

// 统一的加载启动函数
const startLoading = () => {
  if (!props.src) {
    console.warn('LazyImage: 没有src，无法开始加载')
    return
  }

  if (hasStartedLoading.value) {
    return
  }

  hasStartedLoading.value = true
  currentSrc.value = props.src
}

// 组件挂载后初始化
onMounted(async () => {
  await nextTick()
  initIntersectionObserver()

  // 添加超时fallback机制，如果3秒内没有开始加载，就强制开始
  setTimeout(() => {
    if (!hasStartedLoading.value && props.src) {
      console.warn('LazyImage: 超时fallback触发，强制开始加载:', props.src)
      startLoading()
    }
  }, 3000)
})

// 监听 src 变化，重置状态
watch(() => props.src, () => {
  resetState()
})




// 重置状态
const resetState = () => {
  imageLoaded.value = false
  imageError.value = false
  hasStartedLoading.value = false
  currentSrc.value = ''
  retryCount.value = 0
}

// 图片加载成功
const handleImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
  emit('load')
}

// 图片加载失败
const handleImageError = () => {

  if (retryCount.value < maxRetries) {
    // 自动重试，使用指数退避策略
    const delay = Math.min(1000 * Math.pow(2, retryCount.value), 5000) // 最大延迟5秒
    setTimeout(() => {
      retryCount.value++
      // 添加时间戳避免缓存
      const separator = props.src.includes('?') ? '&' : '?'
      currentSrc.value = props.src + `${separator}retry=${retryCount.value}&t=${Date.now()}`
    }, delay)
  } else {
    imageError.value = true
    emit('error')
  }
}

// 手动重试
const retryLoad = () => {
  resetState()
  retryCount.value = 0

  // 如果元素在视口内，重新触发加载
  if (isIntersecting.value) {
    hasStartedLoading.value = true

    // 延迟一下再重新加载，确保状态已重置
    setTimeout(() => {
      const separator = props.src.includes('?') ? '&' : '?'
      currentSrc.value = props.src + `${separator}retry=manual&t=${Date.now()}`
    }, 100)
  }
}

// 组件卸载时清理
const cleanup = () => {
  if (observerInstance.value && observerInstance.value.stop) {
    observerInstance.value.stop()
    observerInstance.value = null
  }
}

// 在组件卸载时清理
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.lazy-image.fade-in {
  opacity: 1;
}

/* 占位符样式 */
.lazy-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skeleton-loader {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 加载中样式 */
.lazy-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(113, 198, 255, 0.3);
  border-radius: 50%;
  border-top-color: #71C6FF;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误状态样式 */
.lazy-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
}

.lazy-error:hover {
  background-color: #eee;
  color: #666;
}

.lazy-error span {
  font-size: 12px;
  text-align: center;
}

.lazy-error svg {
  opacity: 0.6;
}
</style>
