<template>
  <div class="layout-settings" style="color: black;">
    <div class="setting-group">
      <h3 style="font-size: 16px; font-weight: 600; color: #333;">弹窗关闭设置</h3>
      <div class="layout-setting-item">
        <span>关闭按钮</span>
        <Switch :checked="settingStore.closeModalOnButtonClick" @change="settingStore.setCloseModalOnButtonClick" />
      </div>
      <div class="layout-setting-item">
        <span>窗口以外区域</span>
        <Switch :checked="settingStore.closeModalOnOutsideClick" @change="settingStore.setCloseModalOnOutsideClick" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useSettingStore } from '@/stores/setting.js'
import { Switch } from 'ant-design-vue'

const settingStore = useSettingStore()
</script>

<style lang="scss" scoped>
.layout-settings .setting-group {
  padding-bottom: 15px;
}
.layout-settings .setting-group:last-child {
  border-bottom: none;
}
.layout-setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}
.layout-setting-item span {
  font-size: 14px;
  color: #555;
}
</style>
