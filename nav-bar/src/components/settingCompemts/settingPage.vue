<template>
  <div class="layout-settings" style="color: black;">
    <div class="setting-group">
      <h3 style="font-size: 16px; font-weight: 600; color: #333;">菜单栏设置</h3>
      <div class="layout-setting-item">
        <span>自动隐藏</span>
        <Switch :checked="!layoutStore.isSidebarFixed" @change="(checked) => layoutStore.setSidebarFixed(!checked)" />
      </div>
      <div class="layout-setting-item">
        <span>常驻固定</span>
        <Switch :checked="layoutStore.isSidebarFixed" @change="layoutStore.setSidebarFixed" />
      </div>
    </div>
    <div class="setting-group">
      <h3 style="font-size: 16px; font-weight: 600; color: #333;">Dock栏设置</h3>
      <div class="layout-setting-item">
        <span>自动隐藏</span>
        <Switch :checked="!layoutStore.isDockBarFixed" @change="(checked) => layoutStore.setDockBarFixed(!checked)" />
      </div>
      <div class="layout-setting-item">
        <span>常驻固定</span>
        <Switch :checked="layoutStore.isDockBarFixed" @change="layoutStore.setDockBarFixed" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useLayoutStore } from '@/stores/layout.js'
import { Switch } from 'ant-design-vue'

const layoutStore = useLayoutStore()
</script>

<style lang="scss" scoped>
.layout-settings .setting-group {
  padding-bottom: 15px;
}
.layout-settings .setting-group:last-child {
  border-bottom: none;
}
.layout-setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}
.layout-setting-item span {
  font-size: 14px;
  color: #555;
}
</style>
