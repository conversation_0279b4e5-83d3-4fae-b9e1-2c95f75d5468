<template>
  <div class="exchange-container">
    <h2 class="setting-title">合作交流</h2>
    <!-- 顶部标题区域 -->
    <div class="header-section">
      <img :src="exchangeImg" />
    </div>

    <!-- 卡片列表容器 -->
    <div class="card-list">
      <div
        v-for="item in exchangeList"
        :key="item.id"
        class="exchange-card"
      >
        <div class="project-info">
            <span class="project-name">{{ item.name }}</span>--
            <span class="project-source">{{ item.source }}</span>
        </div>
        <div class="project-content">
          <!-- 左侧头像区域 -->
          <div class="avatar-section">
            <div class="avatar-container">
              <img
                v-if="item.officialUrl"
                :src="item.officialUrl"
                :alt="item.name"
                class="avatar-img"
                @error="handleImageError"
              />
              <div v-else class="avatar-placeholder">
                <span>{{ item.name ? item.name.charAt(0) : '?' }}</span>
              </div>
            </div>
            <!-- <p class="platform-name">{{ item.officialAccounts || '公众号' }}</p> -->
          </div>

          <!-- 右侧信息区域 -->
          <div class="info-section">
            <div class="info-item">
              <span class="info-label">联系方式：</span>
              <span class="info-value">{{ item.contactWay || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">联系人：</span>
              <span class="info-value">{{ item.linkman || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">公众号</span>
              <span class="info-value">{{ item.name || '暂无' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Button type="primary" @click="openFriendLinksModal">友情链接</Button>
    <!-- <IframeCard :url="iframeUrl" class="kind-applyFor" /> -->
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { getExchangeList } from '@/api/setting'
import exchangeImg from '@/assets/setting/exchangeImg.png'
import IframeCard from '@/components/iframe/IframeCard.vue'
import { Button } from 'ant-design-vue'
import emitter from '@/utils/mitt';

// 响应式数据存储合作交流列表
const exchangeList = ref([])
const iframeUrl = ref('')
// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none'
  const nextElement = event.target.nextElementSibling
  if (nextElement) {
    nextElement.style.display = 'flex'
  }
}

function getList() {
    getExchangeList().then(res => {
        console.log(res,'res')
        if (res.status === 200 && Array.isArray(res.data)) {
            exchangeList.value = res.data.filter(item => item.type !== 2)
            iframeUrl.value = res.data.find(item => item.type === 2)?.questionUrl
        }
    })
}

function openFriendLinksModal() {
  emitter.emit('open-friend-links-modal')
}

onMounted(() => {
    getList()
})

</script>
<style scoped lang="scss">
.exchange-container {
  max-width: 800px;
  height: 100%;

  // 顶部标题区域样式
  .header-section {
    text-align: center;
    margin-bottom: 30px;
    img{
      width: 100%;
    }
    .main-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .sub-title {
      font-size: 14px;
      color: #666;
      margin: 0;
      opacity: 0.8;
    }
  }

  // 卡片列表容器
  .card-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
  }

  // 单个交流卡片样式
  .exchange-card {
    display: flex;
    flex-direction: column;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    .project-info {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        padding: 7px 0px;
        border-bottom: 1px solid #e8e8e8;
      }
    .project-content{
      display: flex;
      padding: 10px;
      }  
    }

    // 左侧头像区域
    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      min-width: 80px;

      .avatar-container {
        width: 76px;
        height: 76px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 8px;
        position: relative;
        box-shadow: 0px 0px 4px 0px #00000024;
        .avatar-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;

          span {
            color: #fff;
            font-size: 20px;
            font-weight: 600;
          }
        }
      }

      .platform-name {
        font-size: 12px;
        color: #666;
        margin: 0;
        text-align: center;
        line-height: 1.2;
      }
    }

    // 右侧信息区域
    .info-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .info-label {
          font-size: 12px;
          color: #666;
          min-width: 70px;
          font-weight: 500;
        }

        .info-value {
          font-size: 12px;
          color: #333;
          flex: 1;
        }
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .exchange-container {
    padding: 16px;

    .exchange-card {
      padding: 16px;

      .avatar-section {
        margin-right: 16px;
        min-width: 70px;

        .avatar-container {
          width: 50px;
          height: 50px;
        }
      }

      .info-section {
        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .info-label {
            margin-bottom: 2px;
            min-width: auto;
          }
        }
      }
    }
  }
}
.setting-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.kind-applyFor{
  height: 1000px;
}
</style>
