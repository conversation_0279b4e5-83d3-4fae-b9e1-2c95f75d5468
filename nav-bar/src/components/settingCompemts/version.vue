<template>
  <div class="version-container">
    <h2 class="setting-title">日志公告</h2>
    <div
      v-for="version in versionList"
      :key="version.id"
      class="version-card"
    >
      <!-- 版本标题 -->
      <div class="version-header">
        <h3 class="version-title">{{ version.name }} v{{ version.version }}</h3>
        <div class="version-date">{{ formatDate(version.createtime) }}</div>
      </div>

      <!-- 版本内容 -->
      <div class="version-content">
        {{ version.context }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { versionInfo } from '@/api/setting'

// 版本列表数据
const versionList = ref([])

// 日期格式化函数
function formatDate(dateString) {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    })
}

function getVersionInfo() {
    versionInfo().then(res => {
        console.log(res,'res')
        if (res.status === 200 && res.data) {
            versionList.value = res.data
        }
    })
}

onMounted(() => {
    getVersionInfo()
})

</script>
<style lang="scss" scoped>
.version-container {
  height: 100%;
  overflow-y: auto;
}

.version-card {
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #ffffff;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
}

.version-title {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: #545454;
}

.version-date {
  font-size: 13px;
  color: #545454;
}

.version-content {
  font-size: 14px;
  line-height: 1.6;
  color: #8F8E8E;
  white-space: pre-wrap;
  word-break: break-word;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.setting-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
