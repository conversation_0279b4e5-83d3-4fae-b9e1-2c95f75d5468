<script setup>
import { ref, watch, onMounted } from 'vue'
import FlipCard from '../FlipCard/FlipCard.vue'

const props = defineProps({
  hours: { type: Number, default: 0 },
  minutes: { type: Number, default: 0 },
  seconds: { type: Number, default: 0 },
  size: { type: String, default: 'normal' }, // compact, small, normal, medium, large
  customStyle: { type: Object, default: null } // 自定义样式对象
})

const flipCardHour1Ref = ref(null)
const flipCardHour2Ref = ref(null)
const flipCardMinute1Ref = ref(null)
const flipCardMinute2Ref = ref(null)
const flipCardSecond1Ref = ref(null)
const flipCardSecond2Ref = ref(null)
const flipClockRef = ref(null)

const flipCards = [
  flipCardHour1Ref,
  flipCardHour2Ref,
  flipCardMinute1Ref,
  flipCardMinute2Ref,
  flipCardSecond1Ref,
  flipCardSecond2Ref,
]

function pad2(n) {
  return n.toString().padStart(2, '0')
}

function getTimeStr(h, m, s) {
  return pad2(h) + pad2(m) + pad2(s)
}

// 记录上一次的时间字符串
let lastTimeStr = getTimeStr(props.hours, props.minutes, props.seconds)

// 监听props变化，驱动翻页
watch(
  () => [props.hours, props.minutes, props.seconds],
  ([h, m, s]) => {
    const nowTimeStr = lastTimeStr
    const nextTimeStr = getTimeStr(h, m, s)
    for (let i = 0; i < flipCards.length; i++) {
      if (nowTimeStr[i] === nextTimeStr[i]) continue
      flipCards[i].value?.flipDown(nowTimeStr[i], nextTimeStr[i])
    }
    lastTimeStr = nextTimeStr
  }
)

// 组件挂载后监听尺寸变化
onMounted(() => {
  // 确保组件DOM已经挂载后再添加监听
  setTimeout(() => {
    if (!flipClockRef.value) return;
    
    // 监听组件尺寸变化，以适应极端情况
    const resizeObserver = new ResizeObserver(() => {
      // 每次回调中再次检查ref是否存在
      if (!flipClockRef.value) return;
      
      const clockWidth = flipClockRef.value.offsetWidth;
      
      // 如果宽度小于临界值，应用紧急压缩比例
      if (clockWidth < 180) {
        flipClockRef.value.classList.add('ultra-compact');
      } else if (clockWidth < 240) {
        flipClockRef.value.classList.add('very-compact');
      } else {
        flipClockRef.value.classList.remove('ultra-compact', 'very-compact');
      }
    });

    resizeObserver.observe(flipClockRef.value);
    
    // 组件卸载时清理
    return () => {
      resizeObserver.disconnect();
    };
  }, 0);
})

// 初始化
const initNowTimeStr = getTimeStr(props.hours, props.minutes, props.seconds)
</script>

<template>
  <div ref="flipClockRef" class="FlipClock" :class="`size-${size}`" :style="customStyle ? { fontSize: customStyle.colonSize + 'px' } : {}">
    <FlipCard ref="flipCardHour1Ref" :initFrontText="initNowTimeStr[0]" customClass="odd-corner" :size="size" :customStyle="customStyle" />
    <FlipCard ref="flipCardHour2Ref" :initFrontText="initNowTimeStr[1]" customClass="even-corner" :size="size" :customStyle="customStyle" />
    <em :style="customStyle ? { fontSize: customStyle.colonSize + 'px !important', lineHeight: customStyle.height + 'px !important', verticalAlign: 'middle' } : {}">:</em>
    <FlipCard ref="flipCardMinute1Ref" :initFrontText="initNowTimeStr[2]" customClass="odd-corner" :size="size" :customStyle="customStyle" />
    <FlipCard ref="flipCardMinute2Ref" :initFrontText="initNowTimeStr[3]" customClass="even-corner" :size="size" :customStyle="customStyle" />
    <em :style="customStyle ? { fontSize: customStyle.colonSize + 'px', lineHeight: customStyle.height + 'px', verticalAlign: 'middle' } : {}">:</em>
    <FlipCard ref="flipCardSecond1Ref" :initFrontText="initNowTimeStr[4]" customClass="odd-corner" :size="size" :customStyle="customStyle" />
    <FlipCard ref="flipCardSecond2Ref" :initFrontText="initNowTimeStr[5]" customClass="even-corner" :size="size" :customStyle="customStyle" />
  </div>
</template>

<style scoped>
.FlipClock {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* FlipCard 样式由 FlipCard 组件自身控制 */

/* 默认冒号样式 */
.FlipClock em {
  display: inline-block;
  line-height: 100px;
  font-size: 30px;
  font-style: normal;
  vertical-align: middle;
  color: black;
  margin: 0px 5px;
  font-weight: bold;
  /* 添加平滑过渡动画 */
  transition: font-size 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              line-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 紧凑尺寸 - 2x1 */
.FlipClock.size-compact em {
  font-size: 20px;
  line-height: 60px;
  margin: 0px 2px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 小尺寸 - 3x2 */
.FlipClock.size-small em {
  font-size: 25px;
  line-height: 80px;
  margin: 0px 3px;
  color: black;
}

/* 正常尺寸 - 2x2 */
.FlipClock.size-normal em {
  font-size: 30px;
  line-height: 100px;
  margin: 0px 5px;
  color: black;
}

/* 中等尺寸 - 4x2 */
.FlipClock.size-medium em {
  font-size: 35px;
  line-height: 110px;
  margin: 0px 6px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 大尺寸 - 4x3 */
.FlipClock.size-large em {
  font-size: 40px;
  line-height: 130px;
  margin: 0px 8px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 额外紧凑模式，当宽度受限时自动应用 */
.FlipClock.very-compact {
  transform-origin: center center;
}

.FlipClock.ultra-compact {
  transform-origin: center center;
}

.FlipClock.ultra-compact em {
  margin: 0px 1px;
}

/* 响应式调整 */
@media (max-width: 350px) {
  .FlipClock {
    transform-origin: center center;
  }
  
  .FlipClock em {
    margin: 0 2px;
  }
}

@media (max-width: 250px) {
  .FlipClock em {
    margin: 0 1px;
  }
}

/* 确保自定义样式优先级最高 */
.FlipClock[style*="font-size"] em[style*="font-size"] {
  /* 当存在内联样式时，确保其优先级最高 */
  font-size: inherit !important;
  line-height: inherit !important;
}
</style>
