<template>
  <div ref="relaxCardRef" :class="['relax-card', `layout-${layoutType}`, { 'play': isPlaying }]" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
    <div class="relax-card-content">
      <!-- 设置按钮 -->
      <div v-show="isHovered" class="settingTime" @click="openSettings">
        <svg t="1749017934141" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4363" width="30" height="30"><path d="M904.533333 422.4l-85.333333-14.933333-17.066667-38.4 49.066667-70.4c14.933333-21.333333 12.8-49.066667-6.4-68.266667l-53.333333-53.333333c-19.2-19.2-46.933333-21.333333-68.266667-6.4l-70.4 49.066666-38.4-17.066666-14.933333-85.333334c-2.133333-23.466667-23.466667-42.666667-49.066667-42.666666h-74.666667c-25.6 0-46.933333 19.2-53.333333 44.8l-14.933333 85.333333-38.4 17.066667L296.533333 170.666667c-21.333333-14.933333-49.066667-12.8-68.266666 6.4l-53.333334 53.333333c-19.2 19.2-21.333333 46.933333-6.4 68.266667l49.066667 70.4-17.066667 38.4-85.333333 14.933333c-21.333333 4.266667-40.533333 25.6-40.533333 51.2v74.666667c0 25.6 19.2 46.933333 44.8 53.333333l85.333333 14.933333 17.066667 38.4L170.666667 727.466667c-14.933333 21.333333-12.8 49.066667 6.4 68.266666l53.333333 53.333334c19.2 19.2 46.933333 21.333333 68.266667 6.4l70.4-49.066667 38.4 17.066667 14.933333 85.333333c4.266667 25.6 25.6 44.8 53.333333 44.8h74.666667c25.6 0 46.933333-19.2 53.333333-44.8l14.933334-85.333333 38.4-17.066667 70.4 49.066667c21.333333 14.933333 49.066667 12.8 68.266666-6.4l53.333334-53.333334c19.2-19.2 21.333333-46.933333 6.4-68.266666l-49.066667-70.4 17.066667-38.4 85.333333-14.933334c25.6-4.266667 44.8-25.6 44.8-53.333333v-74.666667c-4.266667-27.733333-23.466667-49.066667-49.066667-53.333333z m-19.2 117.333333l-93.866666 17.066667c-10.666667 2.133333-19.2 8.533333-23.466667 19.2l-29.866667 70.4c-4.266667 10.666667-2.133333 21.333333 4.266667 29.866667l53.333333 76.8-40.533333 40.533333-76.8-53.333333c-8.533333-6.4-21.333333-8.533333-29.866667-4.266667L576 768c-10.666667 4.266667-17.066667 12.8-19.2 23.466667l-17.066667 93.866666h-57.6l-17.066666-93.866666c-2.133333-10.666667-8.533333-19.2-19.2-23.466667l-70.4-29.866667c-10.666667-4.266667-21.333333-2.133333-29.866667 4.266667l-76.8 53.333333-40.533333-40.533333 53.333333-76.8c6.4-8.533333 8.533333-21.333333 4.266667-29.866667L256 576c-4.266667-10.666667-12.8-17.066667-23.466667-19.2l-93.866666-17.066667v-57.6l93.866666-17.066666c10.666667-2.133333 19.2-8.533333 23.466667-19.2l29.866667-70.4c4.266667-10.666667 2.133333-21.333333-4.266667-29.866667l-53.333333-76.8 40.533333-40.533333 76.8 53.333333c8.533333 6.4 21.333333 8.533333 29.866667 4.266667L448 256c10.666667-4.266667 17.066667-12.8 19.2-23.466667l17.066667-93.866666h57.6l17.066666 93.866666c2.133333 10.666667 8.533333 19.2 19.2 23.466667l70.4 29.866667c10.666667 4.266667 21.333333 2.133333 29.866667-4.266667l76.8-53.333333 40.533333 40.533333-53.333333 76.8c-6.4 8.533333-8.533333 21.333333-4.266667 29.866667L768 448c4.266667 10.666667 12.8 17.066667 23.466667 19.2l93.866666 17.066667v55.466666z" fill="#ffffff" p-id="4364"></path><path d="M512 394.666667c-64 0-117.333333 53.333333-117.333333 117.333333s53.333333 117.333333 117.333333 117.333333 117.333333-53.333333 117.333333-117.333333-53.333333-117.333333-117.333333-117.333333z m0 170.666666c-29.866667 0-53.333333-23.466667-53.333333-53.333333s23.466667-53.333333 53.333333-53.333333 53.333333 23.466667 53.333333 53.333333-23.466667 53.333333-53.333333 53.333333z" fill="#ffffff" p-id="4365"></path></svg>
      </div>

      <!-- 2x1 紧凑横向布局：只显示时钟 -->
      <template v-if="layoutType === 'compact-horizontal'">
        <div class="compact-layout">
          <div class="flip-clock-container compact">
            <template v-if="!workEndCountdown.isWorkEnd">
              <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" :customStyle="adaptiveLayout.customFlipCardStyle" />
            </template>
            <template v-else>
              <div class="offwork-tip compact">🎉 下班啦！</div>
            </template>
          </div>
        </div>
      </template>

      <!-- 2x2 标准布局：优化的紧凑设计 -->
      <template v-else-if="layoutType === 'standard'">
        <div class="standard-layout">
          <div class="countdown-header compact">
            <span class="header-icon">⏰</span>
            <span class="header-text">下班倒计时</span>
          </div>
          <div class="main-clock-area">
            <div class="flip-clock-container compact">
              <template v-if="!workEndCountdown.isWorkEnd">
                <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" :customStyle="adaptiveLayout.customFlipCardStyle" />
              </template>
              <template v-else>
                <div class="offwork-tip compact">🎉 下班啦！</div>
              </template>
            </div>
          </div>
          <div class="info-footer compact">
            <div class="countdown-info compact">
              <span class="info-label">💰</span>
              <div class="info-data">
                <span>发薪日</span>
                <strong>{{ salaryCountdown }}</strong>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 3x2 扩展横向布局：时钟 + 左右信息 -->
      <template v-else-if="layoutType === 'extended-horizontal'">
        <div :class="['extended-horizontal-layout', { 'layout-compact': adaptiveLayout.containerSize === 'tiny' || adaptiveLayout.containerSize === 'small' }]">
          <div class="countdown-label compact">⏰ 下班倒计时</div>
          <div class="main-content">
            <div class="info-side left">
              <div class="countdown-info side-card">
                <div class="info-icon">💰</div>
                <div class="info-content">
                  <span>发薪日</span>
                  <strong>{{ salaryCountdown }}</strong>
                </div>
              </div>
            </div>
            <div class="clock-center">
              <div class="flip-clock-container center">
                <template v-if="!workEndCountdown.isWorkEnd">
                  <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" :customStyle="adaptiveLayout.customFlipCardStyle" />
                </template>
                <template v-else>
                  <div class="offwork-tip">🎉 下班啦！</div>
                </template>
              </div>
            </div>
            <div class="info-side right">
              <div class="countdown-info side-card">
                <div class="info-icon">🎊</div>
                <div class="info-content">
                  <span>{{ nextHolidayName }}</span>
                  <strong>{{ holidayCountdown }}</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 3x3 完整布局：优化的正方形布局 -->
      <template v-else-if="layoutType === 'full'">
        <div class="full-layout">
          <div class="countdown-label enhanced">⏰ 下班倒计时</div>
          <div class="main-clock-section">
            <div class="flip-clock-container centered">
              <template v-if="!workEndCountdown.isWorkEnd">
                <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" size="normal" :customStyle="adaptiveLayout.customFlipCardStyle" />
              </template>
              <template v-else>
                <div class="offwork-tip enhanced">🎉 下班了早点回家吧！</div>
              </template>
            </div>
          </div>
          <div class="info-grid-section">
            <div class="countdown-info-grid">
              <div class="countdown-info enhanced-card">
                <div class="info-icon">💰</div>
                <div class="info-content">
                  <span>发薪日</span>
                  <strong>{{ salaryCountdown }}</strong>
                </div>
              </div>
              <div class="countdown-info enhanced-card">
                <div class="info-icon">🎊</div>
                <div class="info-content">
                  <span>{{ nextHolidayName }}</span>
                  <strong>{{ holidayCountdown }}</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 4x2 宽横向布局：时钟 + 横向信息 -->
      <template v-else-if="layoutType === 'wide-horizontal'">
        <div :class="['wide-horizontal-layout', { 'layout-compact': adaptiveLayout.containerSize === 'tiny' || adaptiveLayout.containerSize === 'small' }]">
          <div class="countdown-label">⏰ 下班倒计时</div>
          <div class="main-content">
            <div class="clock-section">
              <div class="flip-clock-container">
                <template v-if="!workEndCountdown.isWorkEnd">
                  <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" :customStyle="adaptiveLayout.customFlipCardStyle" />
                </template>
                <template v-else>
                  <div class="offwork-tip">🎉 下班了早点回家吧！</div>
                </template>
              </div>
            </div>
            <div class="info-section">
              <div class="countdown-footer horizontal">
                <div class="countdown-info enhanced">
                  <div class="info-icon">💰</div>
                  <div class="info-text">
                    <span>发薪日倒计时</span>
                    <strong>{{ salaryCountdown }}</strong>
                  </div>
                </div>
                <div class="countdown-info enhanced">
                  <div class="info-icon">🎊</div>
                  <div class="info-text">
                    <span>{{ nextHolidayName }}倒计时</span>
                    <strong>{{ holidayCountdown }}</strong>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 4x3 宽完整布局：所有信息 + 更大字体 -->
      <template v-else>
        <div :class="['wide-full-layout', { 'layout-compact': adaptiveLayout.containerSize === 'tiny' || adaptiveLayout.containerSize === 'small' }]">
          <div class="countdown-label large">⏰ 下班倒计时</div>
          <div class="clock-section large">
            <div class="flip-clock-container large">
              <template v-if="!workEndCountdown.isWorkEnd">
                <FlipClock :hours="workEndCountdown.hours" :minutes="workEndCountdown.minutes" :seconds="workEndCountdown.seconds" :customStyle="adaptiveLayout.customFlipCardStyle" />
              </template>
              <template v-else>
                <div class="offwork-tip large">🎉 下班了早点回家吧！</div>
              </template>
            </div>
          </div>
          <div class="info-grid">
            <div class="countdown-info large">
                <span>发薪日</span>
                <strong>{{ salaryCountdown }}</strong>
            </div>
            <div class="countdown-info large">
                <span>{{ nextHolidayName }}</span>
                <strong>{{ holidayCountdown }}</strong>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 设置弹窗组件 -->
    <Teleport to="body">
      <RelaxSettings
        v-if="showSettings"
        v-model:visible="showSettings"
        :settings="settings"
        @update-settings="updateSettings"
      />
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
import RelaxSettings from './RelaxSettings.vue';
import FlipClock from './FlipClock/FlipClock.vue';

// 添加组件宽度监听
const relaxCardRef = ref(null);

const props = defineProps({
  appId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '下班倒计时'
  },
  headerColor: {
    type: String,
    default: '#FF6B6B'
  },
  size: {
    type: Object,
    default: () => ({ w: 2, h: 2 })
  }
});

// 设置状态
const showSettings = ref(false);
const isPlaying = ref(false);
const isHovered = ref(false); // 鼠标悬停状态
const timeRefresher = ref(0); // 用于强制刷新时间的变量
const settings = ref({
  workEndTime: '18:00',
  salaryDay: 15,
  holidays: [
    { name: '元旦', date: '2025-01-01' },
    { name: '春节', date: '2025-01-29' },
    { name: '清明节', date: '2025-04-04' },
    { name: '劳动节', date: '2025-05-01' },
    { name: '端午节', date: '2025-05-31' },
    { name: '中秋节', date: '2025-09-12' },
    { name: '国庆节', date: '2025-10-01' }
  ]
});

// 翻牌当前位置与前一位置
const activeDigits = reactive({
  hourTens: 0,
  hourOnes: 0,
  minuteTens: 0,
  minuteOnes: 0,
  secondTens: 0,
  secondOnes: 0
});

const beforeDigits = reactive({
  hourTens: 0,
  hourOnes: 0,
  minuteTens: 0,
  minuteOnes: 0,
  secondTens: 0,
  secondOnes: 0
});

const animations = reactive({
  hourTens: false,
  hourOnes: false,
  minuteTens: false,
  minuteOnes: false,
  secondTens: false,
  secondOnes: false
});

// 使用纯粹的尺寸自适应逻辑，摒弃固定布局类型
const adaptiveLayout = computed(() => {
  // 显式依赖props.size和强制刷新触发器，确保size变化时重新计算
  const gridSize = `${props.size?.w || 2}x${props.size?.h || 2}`;
  const refreshTrigger = forceRefreshTrigger.value; // 确保依赖刷新触发器
  const clearCache = shouldClearCache.value; // 确保依赖缓存清除标志

  // 获取实际渲染尺寸 - 移除默认值，用0作为初始值，确保逻辑完全基于实际尺寸
  let actualWidth = 0
  let actualHeight = 0
  let browserZoomFactor = 1; // 浏览器缩放因子

  if (relaxCardRef.value) {
    const rect = relaxCardRef.value.getBoundingClientRect()
    actualWidth = rect.width
    actualHeight = rect.height

    // 检测浏览器缩放级别
    browserZoomFactor = window.devicePixelRatio || 1;
  }

  // 如果尺寸为0，则不进行计算（组件可能尚未渲染）
  if (actualWidth === 0 || actualHeight === 0) {
    return {
      layoutType: 'compact-horizontal',  // 提供最简单的默认值以防组件尚未渲染
      containerSize: 'unknown',
      orientation: 'unknown',
      showExtraInfo: false,
      showCountdownLabel: false,
      customFlipCardStyle: {
        width: 20,
        height: 40,
        fontSize: 24,
        lineHeight: 40,
        colonSize: 10
      },
      actualWidth: 0,
      actualHeight: 0,
      area: 0,
      aspectRatio: 1,
      configSize: gridSize,
      availableWidth: 0,
      availableHeight: 0,
      widthFactor: 0.8,
      heightFactor: 0.7
    }
  }

  // 计算关键指标
  const area = actualWidth * actualHeight
  const aspectRatio = actualWidth / actualHeight

  // === 步骤1：确定容器类别和空间 ===

  // 根据面积和宽高比确定容器类型
  let containerSize;
  if (area < 12000) containerSize = 'tiny';
  else if (area < 20000) containerSize = 'small';
  else if (area < 40000) containerSize = 'medium';
  else if (area < 80000) containerSize = 'large';
  else containerSize = 'xlarge';
  
  // 根据宽高比确定布局方向
  let orientation;
  if (aspectRatio > 1.5) orientation = 'wide';           // 宽屏
  else if (aspectRatio < 0.67) orientation = 'tall';     // 竖屏
  else orientation = 'square';                          // 近似正方形
  
  // 基于容器特征决定是否显示额外信息
  const showExtraInfo = area >= 16000;                   // 降低阈值，更容易触发额外信息显示
  const showCountdownLabel = area >= 10000;              // 降低阈值，更容易显示标题
  
  
  // === 步骤2：计算时钟尺寸 ===
  
  // 动态计算时钟占用的空间比例
  let widthFactor, heightFactor;
  
  // 根据网格尺寸和方向优化空间利用率
  if (gridSize === '2x1') {
    // 2x1布局特殊优化 - 最紧凑布局，只显示时钟，应该充分利用所有空间
    widthFactor = 0.95; // 时钟占95%宽度
    heightFactor = 0.95; // 时钟占95%高度，2x1布局没有其他元素，应该最大化利用高度
  } else if (gridSize === '2x2') {
    // 2x2布局特殊优化 - 标准布局，需要为标题和底部信息预留空间
    if (containerSize === 'tiny' || containerSize === 'small') {
      // 小尺寸容器：时钟占据更多空间
      widthFactor = 0.85; // 时钟占85%宽度
      heightFactor = 0.7; // 时钟占70%高度，为标题和信息预留空间
    } else {
      // 正常尺寸容器：平衡时钟和信息显示
      widthFactor = showExtraInfo ? 0.8 : 0.85; // 时钟占80-85%宽度
      heightFactor = showExtraInfo ? 0.6 : 0.7; // 时钟占60-70%高度
    }
  } else if (gridSize === '4x2') {
    // 4x2布局特殊优化 - 宽横向布局，需要为右侧信息区域预留空间
    if (containerSize === 'tiny' || containerSize === 'small') {
      // 小尺寸容器：时钟占据更多空间，信息区域紧凑
      widthFactor = 0.75; // 时钟占75%宽度
      heightFactor = 0.85; // 充分利用高度
    } else {
      // 正常尺寸容器：平衡时钟和信息区域
      widthFactor = showExtraInfo ? 0.6 : 0.7; // 时钟占60-70%宽度
      heightFactor = 0.8; // 增加高度利用率
    }
  } else if (gridSize === '4x3') {
    // 4x3布局优化 - 充分利用高度空间，让时钟尽可能大
    if (containerSize === 'tiny' || containerSize === 'small') {
      // 小尺寸容器：时钟占据更多空间
      widthFactor = 0.85; // 时钟占85%宽度
      heightFactor = 0.9; // 充分利用高度，时钟占90%高度
    } else {
      // 正常尺寸容器：最大化时钟显示
      widthFactor = showExtraInfo ? 0.75 : 0.85; // 时钟占75-85%宽度
      heightFactor = showExtraInfo ? 0.8 : 0.9; // 时钟占80-90%高度，充分利用空间
    }
  } else if (gridSize === '3x2') {
    // 3x2布局特殊优化 - 三栏布局，时钟居中，左右信息卡片
    if (containerSize === 'tiny' || containerSize === 'small') {
      // 小尺寸容器：时钟占据更多空间，左右信息卡片紧凑
      widthFactor = 0.6; // 时钟占60%宽度
      heightFactor = 0.8; // 充分利用高度
    } else {
      // 正常尺寸容器：平衡三栏布局
      widthFactor = showExtraInfo ? 0.5 : 0.65; // 时钟占50-65%宽度
      heightFactor = 0.75; // 适中的高度利用率
    }
  } else if (orientation === 'wide') {
    // 其他宽屏布局
    widthFactor = showExtraInfo ? 0.5 : 0.8;
    heightFactor = 0.7;
  } else if (orientation === 'tall') {
    // 竖屏 - 时钟放在上部
    widthFactor = 0.85;
    heightFactor = showExtraInfo ? 0.5 : 0.7;
  } else {
    // 近似正方形
    widthFactor = showExtraInfo ? 0.75 : 0.85;
    heightFactor = showExtraInfo ? 0.6 : 0.7;
  }
  
  // 小尺寸容器需要提高空间利用率
  if (containerSize === 'tiny' || containerSize === 'small') {
    widthFactor += 0.1;
    heightFactor += 0.1;
  }
  
  // === 步骤3：精确计算FlipCard尺寸 ===
  
  // 计算时钟可用空间
  const availableWidth = actualWidth * widthFactor;
  const availableHeight = actualHeight * heightFactor;
  
  // 计算每个FlipCard的理想尺寸
  // 时钟由6个数字卡片+2个冒号组成，约8个单位
  let idealCardWidth = availableWidth / 8;

  // 对于不同布局，优化卡片宽度计算
  if (gridSize === '2x1') {
    // 2x1布局只有时钟，可以使用最多的水平空间
    idealCardWidth = availableWidth / 7.0; // 最大化利用宽度
  } else if (gridSize === '2x2') {
    // 2x2布局可以使用较多的水平空间
    idealCardWidth = availableWidth / 7.2; // 适中的宽度利用
  } else if (gridSize === '4x2' || gridSize === '4x3') {
    // 4x2和4x3布局可以使用更多的水平空间
    idealCardWidth = availableWidth / 7.5; // 减少分母，增加卡片宽度
  } else if (gridSize === '3x2') {
    // 3x2布局需要在三栏中平衡时钟大小
    idealCardWidth = availableWidth / 7.8; // 稍微减少卡片宽度，为左右信息卡片留空间
  }

  // 理想高度基于经验比例(高:宽 = 2.3:1)，但对宽屏布局稍作调整
  let heightRatio = 2.3;
  if (gridSize === '4x2') {
    heightRatio = 2.1; // 4x2布局使用稍微扁一点的比例
  } else if (gridSize === '4x3') {
    heightRatio = 2.2; // 4x3布局使用中等比例
  }

  const idealCardHeight = idealCardWidth * heightRatio;
  
  // 确保高度不超过可用空间
  const maxAvailableHeight = availableHeight * 0.95;

  let finalCardWidth, finalCardHeight;

  if (idealCardHeight > maxAvailableHeight) {
    // 如果理想高度太高，从高度反推宽度
    finalCardHeight = maxAvailableHeight;
    finalCardWidth = finalCardHeight / 2.3;
  } else {
    // 否则使用理想尺寸
    finalCardWidth = idealCardWidth;
    finalCardHeight = idealCardHeight;
  }

  // 根据容器尺寸和实际尺寸动态调整最小值（使用相对比例而不是固定像素）
  let minCardWidth, minCardHeight, maxCardWidth, maxCardHeight;

  // 基于容器实际尺寸的百分比来设置限制，并考虑浏览器缩放因子
  // 对于2x1布局，应该使用更大的基础高度比例，因为它只有时钟
  let baseWidthRatio = 0.08;
  let baseHeightRatio = 0.15;

  if (gridSize === '2x1') {
    // 2x1布局只有时钟，应该使用更大的基础比例
    baseWidthRatio = 0.12; // 增加到12%
    baseHeightRatio = 0.4; // 增加到40%，因为2x1布局没有其他元素
  } else if (gridSize === '2x2') {
    // 2x2布局需要为标题和信息预留空间，但仍可以比默认值大一些
    baseWidthRatio = 0.1; // 增加到10%
    baseHeightRatio = 0.25; // 增加到25%
  }

  const baseWidth = actualWidth * baseWidthRatio;
  const baseHeight = actualHeight * baseHeightRatio;

  // 根据浏览器缩放调整最小值，避免在高缩放下文字过小
  // 对于高缩放（>1.5），减少调整幅度，保持更好的视觉效果
  let zoomAdjustment;
  if (browserZoomFactor > 1.5) {
    // 高缩放时使用更温和的调整
    zoomAdjustment = Math.max(0.7, Math.min(1.2, 1 / Math.sqrt(browserZoomFactor)));
  } else {
    // 正常缩放时使用标准调整
    zoomAdjustment = Math.max(0.6, Math.min(1.5, 1 / browserZoomFactor));
  }

  if (containerSize === 'tiny') {
    minCardWidth = Math.max(6 * zoomAdjustment, baseWidth * 0.6);   // 动态最小值
    minCardHeight = Math.max(14 * zoomAdjustment, baseHeight * 0.6);
    maxCardWidth = baseWidth * 1.5;
    maxCardHeight = baseHeight * 1.5;
  } else if (containerSize === 'small') {
    minCardWidth = Math.max(8 * zoomAdjustment, baseWidth * 0.75);
    minCardHeight = Math.max(18 * zoomAdjustment, baseHeight * 0.75);
    maxCardWidth = baseWidth * 1.8;
    maxCardHeight = baseHeight * 1.8;
  } else {
    minCardWidth = Math.max(10 * zoomAdjustment, baseWidth * 0.9);
    minCardHeight = Math.max(22 * zoomAdjustment, baseHeight * 0.9);
    maxCardWidth = baseWidth * 2.5;
    maxCardHeight = baseHeight * 2.5;
  }

  // 应用动态的最小值和最大值限制
  finalCardWidth = Math.max(minCardWidth, Math.min(finalCardWidth, maxCardWidth));
  finalCardHeight = Math.max(minCardHeight, Math.min(finalCardHeight, maxCardHeight));
  // === 步骤4：计算字体和其他样式 ===
  
  // 字体大小比例始终保持为卡片高度的60%
  const fontSize = Math.floor(finalCardHeight * 0.6);
  
  // 冒号大小始终是字体大小的40%
  const colonSize = Math.floor(fontSize * 0.4);
  
  // 行高与卡片高度一致
  const lineHeight = finalCardHeight;
  
  // 构建最终样式
  const customFlipCardStyle = {
    width: finalCardWidth,
    height: finalCardHeight,
    fontSize: fontSize,
    lineHeight: lineHeight,
    colonSize: colonSize
  }
  
  // 打印自定义样式信息，便于调试
  // console.log(`📏 RelaxCard 布局调整 [${gridSize}] - customStyle:`, {
  //   gridSizeConfig: gridSize,  // 添加网格尺寸配置，方便调试
  //   cardStyle: customFlipCardStyle,
  //   containerDetails: {
  //     size: containerSize,
  //     orientation,
  //     area,
  //     aspectRatio: aspectRatio.toFixed(2),
  //     dimensions: `${Math.round(actualWidth)}×${Math.round(actualHeight)}px`,
  //   },
  //   factors: {
  //     widthFactor: widthFactor.toFixed(2),
  //     heightFactor: heightFactor.toFixed(2)
  //   },
  //   计算过程: {
  //     理想卡片宽度: idealCardWidth.toFixed(2),
  //     理想卡片高度: idealCardHeight.toFixed(2),
  //     最大卡片高度: maxCardHeight.toFixed(2),
  //     最终宽度: finalCardWidth,
  //     最终高度: finalCardHeight,
  //     字体大小: fontSize,
  //     冒号大小: colonSize,
  //     浏览器缩放: browserZoomFactor.toFixed(2),
  //     缩放调整: zoomAdjustment.toFixed(2),
  //     高宽比: heightRatio.toFixed(2),
  //     可用宽度: availableWidth.toFixed(2),
  //     可用高度: availableHeight.toFixed(2)
  //   },
  //   状态标志: {
  //     强制刷新触发器: refreshTrigger,
  //     清除缓存标志: clearCache,
  //     计算时间戳: Date.now()
  //   },
  //   样式变化检测: {
  //     customStyle哈希: JSON.stringify(customFlipCardStyle),
  //     样式对象: customFlipCardStyle
  //   }
  // });
  
  // 结合网格尺寸与实际容器特征来决定布局类型
  let layoutType;
  
  // 确保gridSize始终是最新的
  
  // 改进的布局类型判断逻辑：结合网格尺寸与实际容器特征
  if (gridSize === '2x1') {
    // 2x1网格固定使用紧凑布局
    layoutType = 'compact-horizontal';
  } else if (gridSize === '2x2') {
    // 2x2网格使用标准布局
    layoutType = 'standard';
  } else if (gridSize === '4x2') {
    // 4x2网格使用宽横向布局
    layoutType = 'wide-horizontal';
  } else if (gridSize === '4x3') {
    // 4x3网格使用宽完整布局
    layoutType = 'wide-full';
  } else if (gridSize === '3x2') {
    // 3x2网格使用扩展横向布局
    layoutType = 'extended-horizontal';
  } else if (gridSize === '3x3') {
    // 3x3网格使用完整布局
    layoutType = 'full';
  } else {
    // 对于其他未明确定义的网格尺寸，根据实际特征选择最合适的布局
    if (area < 12000 || !showExtraInfo) {
      // 极小尺寸或不显示额外信息时用最紧凑布局
      layoutType = 'compact-horizontal';
    } else if (area >= 30000 && orientation === 'wide') {
      // 大面积且宽屏
      layoutType = 'wide-horizontal';
    } else if (area >= 30000 && orientation === 'tall') {
      // 大面积且竖屏
      layoutType = 'wide-full';
    } else if (area >= 20000 && orientation === 'wide') {
      // 中等面积且宽屏
      layoutType = 'extended-horizontal';
    } else if (orientation === 'tall') {
      // 任何竖屏
      layoutType = 'full';
    } else {
      // 默认为标准布局
      layoutType = 'standard';
    }
  }
  
  // 添加更多调试信息，帮助诊断布局类型问题



  
  
  return {
    layoutType,
    containerSize,
    orientation,
    showExtraInfo,
    showCountdownLabel,
    customFlipCardStyle,
    actualWidth,
    actualHeight,
    area,
    aspectRatio,
    configSize: gridSize,
    availableWidth,
    availableHeight,
    widthFactor,
    heightFactor,
    waitForCorrectSize
  }
})

// 添加对props.size的监听，确保尺寸变化时强制更新样式
const forceRefreshTrigger = ref(0);
// 添加一个缓存清除标志
const shouldClearCache = ref(false);

watch(() => props.size, (newSize, oldSize) => {
  // 安全检查：确保newSize和oldSize都是有效对象
  if (!newSize) return;

  // 如果oldSize不存在或尺寸确实发生变化
  if (!oldSize || newSize.w !== oldSize.w || newSize.h !== oldSize.h) {
    console.log('🔄 RelaxCard 尺寸变化:', `${oldSize?.w || 0}x${oldSize?.h || 0} → ${newSize.w}x${newSize.h}`);

    // 标记需要清除缓存，确保重新计算时使用最新的DOM尺寸
    shouldClearCache.value = true;

    // 立即触发一次更新
    forceRefreshTrigger.value++;

    // 延迟触发更多更新，确保DOM尺寸正确
    nextTick(() => {
      // 第二次更新
      setTimeout(() => {
        forceRefreshTrigger.value++;

        // 第三次更新，确保尺寸稳定
        setTimeout(() => {
          forceRefreshTrigger.value++;

          // 重置缓存清除标志
          shouldClearCache.value = false;

          console.log('✅ RelaxCard 尺寸更新完成');
        }, 200);
      }, 100);
    });
  }
}, { immediate: true, deep: true });

// 兼容性：保持原有的 layoutType 计算属性
const layoutType = computed(() => adaptiveLayout.value.layoutType);

// 添加一个专门等待CSS动画完成和正确尺寸的函数
const waitForCorrectSize = (expectedGridSize, maxAttempts = 80) => { // 增加到80次尝试
  return new Promise((resolve) => {
    let attempts = 0;
    let lastSize = '';
    let stableCount = 0;
    const requiredStableCount = 3; // 减少到3次，但增加检查频率

    const checkSize = () => {
      attempts++;
      if (!relaxCardRef.value || attempts > maxAttempts) {
        resolve(false);
        return;
      }

      const rect = relaxCardRef.value.getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        const currentSize = `${Math.round(rect.width)}×${Math.round(rect.height)}`;

        // 检查尺寸是否稳定（连续几次相同）
        if (currentSize === lastSize) {
          stableCount++;
        } else {
          stableCount = 0;
          lastSize = currentSize;
        }

        // 根据网格尺寸判断期望的宽高比
        let expectedAspectRatio = 1.0;
        if (expectedGridSize === '4x3') expectedAspectRatio = 1.33;
        else if (expectedGridSize === '4x2') expectedAspectRatio = 2.0;
        else if (expectedGridSize === '3x2') expectedAspectRatio = 1.5;
        else if (expectedGridSize === '2x2') expectedAspectRatio = 1.0;
        else if (expectedGridSize === '2x1') expectedAspectRatio = 2.0;

        const actualAspectRatio = rect.width / rect.height;
        const aspectRatioDiff = Math.abs(actualAspectRatio - expectedAspectRatio);


        // 只有当尺寸稳定且符合预期时才认为成功
        if (stableCount >= requiredStableCount && aspectRatioDiff < 0.25) { // 放宽误差到25%
          resolve(true);
          return;
        }

        // 如果已经检查了很多次，但尺寸基本符合预期，也认为成功
        if (attempts > 40 && aspectRatioDiff < 0.3) {
          resolve(true);
          return;
        }
      }

      setTimeout(checkSize, 80); // 减少检查间隔到80ms，提高响应性
    };

    // 考虑到CSS动画时间和用户体验：
    // - vue-grid-item: 200ms ease
    // - Office.vue transform: 700ms cubic-bezier
    // - 我们的过渡动画: 400ms cubic-bezier
    // 减少初始等待时间，让过渡动画更早开始，提升用户体验
    setTimeout(checkSize, 500);
  });
};

// 从本地存储加载设置
onMounted(() => {
  try {
    const savedSettings = localStorage.getItem('relaxCardSettings');
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      settings.value = parsedSettings;
    }
  } catch (err) {
    console.error('无法加载设置:', err);
  }

  // 启动定时器，每秒更新一次
  startTimers();

  // 强化尺寸监听 - 在组件挂载后立即尝试获取尺寸
  const updateSizeAndRefresh = () => {
    if (relaxCardRef.value) {
      const rect = relaxCardRef.value.getBoundingClientRect();

      // 检查尺寸是否有效
      if (rect.width > 0 && rect.height > 0) {
        // 触发重新计算
        forceRefreshTrigger.value++;
      } else {
        // 尺寸还未正确计算，设置短延迟后再次尝试
        setTimeout(updateSizeAndRefresh, 50);
      }
    }
  };
  
  // 使用requestAnimationFrame确保在下一帧进行尺寸检查
  requestAnimationFrame(() => {
    // 启动尺寸检查
    updateSizeAndRefresh();
  });

  // 设置ResizeObserver进行持续监听
  if (relaxCardRef.value) {
    try {
      // 使用节流函数避免过于频繁的更新
      let resizeTimeout = null;
      const throttledResizeHandler = () => {
        if (resizeTimeout !== null) {
          clearTimeout(resizeTimeout);
        }
        
        resizeTimeout = setTimeout(() => {
          if (relaxCardRef.value) {
            const rect = relaxCardRef.value.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              
              // 触发重新计算
              forceRefreshTrigger.value++;
            }
          }
          resizeTimeout = null;
        }, 100); // 100ms节流
      };
      
      const resizeObserver = new ResizeObserver(() => {
        // 处理尺寸变化
        throttledResizeHandler();
      });
      
      resizeObserver.observe(relaxCardRef.value);
      
      // 保存observer以便清理
      relaxCardRef.value._resizeObserver = resizeObserver;
    } catch (err) {
      console.error('ResizeObserver 初始化失败:', err);
      // 降级方案：使用事件监听
      window.addEventListener('resize', () => {
        forceRefreshTrigger.value++;
      });
    }
  }
});

// 保存设置到本地
function updateSettings(newSettings) {
  settings.value = { ...newSettings };
  try {
    const settingsJson = JSON.stringify(settings.value);
    localStorage.setItem('relaxCardSettings', settingsJson);
  } catch (err) {
    console.error('无法保存设置:', err);
  }
}

// 打开设置弹窗
function openSettings() {
  showSettings.value = true;
}

// 下班倒计时计算
const workEndCountdown = computed(() => {
  // 使用 timeRefresher 触发计算属性重新计算
  const _ = timeRefresher.value;
  
  const now = new Date();
  
  const [hours, minutes] = settings.value.workEndTime.split(':').map(Number);
  
  const endTime = new Date();
  endTime.setHours(hours, minutes, 0, 0);
  
  // 如果已经过了下班时间，显示"已下班"
  if (now > endTime) {
    return { hours: 0, minutes: 0, seconds: 0, isWorkEnd: true };
  }
  
  const diffMs = endTime - now;
  const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  const diffSecs = Math.floor((diffMs % (1000 * 60)) / 1000);
  
  
  return { 
    hours: diffHrs, 
    minutes: diffMins, 
    seconds: diffSecs,
    isWorkEnd: false
  };
});

// 获取下一个节假日名称
const nextHolidayName = computed(() => {
  return holidayInfo.value.nextHolidayName;
});

// 获取节假日倒计时
const holidayCountdown = computed(() => {
  return holidayInfo.value.holidayCountdown;
});

// 计算最近节假日及倒计时
const holidayInfo = computed(() => {
  const now = new Date();
  
  // 按日期排序节假日
  if (!settings.value.holidays || !Array.isArray(settings.value.holidays) || settings.value.holidays.length === 0) {
    return {
      nextHolidayName: '节日',
      holidayCountdown: '待添加'
    };
  }
  
  const sortedHolidays = [...settings.value.holidays]
    .filter(holiday => holiday && holiday.date) // 确保日期存在
    .sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });
  
  if (sortedHolidays.length === 0) {
    return {
      nextHolidayName: '节日',
      holidayCountdown: '待添加'
    };
  }
  
  // 查找下一个未过的节假日
  let nextHoliday = null;
  for (const holiday of sortedHolidays) {
    try {
      const holidayDate = new Date(holiday.date);
      if (holidayDate > now) {
        nextHoliday = holiday;
        break;
      }
    } catch (err) {
      console.error('解析节假日日期错误:', err);
    }
  }
  
  // 如果今年所有节假日都已过去，使用第一个（明年的）
  if (!nextHoliday && sortedHolidays.length > 0) {
    nextHoliday = sortedHolidays[0];
    try {
      // 调整至明年
      const nextYear = new Date(nextHoliday.date);
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      nextHoliday = { ...nextHoliday, date: nextYear.toISOString().split('T')[0] };
    } catch (err) {
      console.error('处理明年节假日错误:', err);
    }
  }
  
  if (nextHoliday) {
    try {
      const holidayDate = new Date(nextHoliday.date);
      const diffTime = Math.abs(holidayDate - now);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return {
        nextHolidayName: nextHoliday.name || '节日',
        holidayCountdown: `${diffDays}天`
      };
    } catch (err) {
      console.error('计算节假日倒计时错误:', err);
      return {
        nextHolidayName: nextHoliday.name || '节日',
        holidayCountdown: '计算中'
      };
    }
  } else {
    return {
      nextHolidayName: '节日',
      holidayCountdown: '待添加'
    };
  }
});

// 发薪倒计时计算
const salaryCountdown = computed(() => {
  const now = new Date();
  const salaryDate = new Date(now.getFullYear(), now.getMonth(), settings.value.salaryDay);
  
  // 如果今天已过发薪日，计算下月发薪日
  if (now.getDate() > settings.value.salaryDay) {
    salaryDate.setMonth(salaryDate.getMonth() + 1);
  }
  
  const diffTime = Math.abs(salaryDate - now);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return `${diffDays}天`;
});

// 更新翻牌数字
const updateDigits = () => {
  const time = workEndCountdown.value;
  // 如果已下班，全部显示0
  if (time.isWorkEnd) {
    updateDigit('hourTens', 0);
    updateDigit('hourOnes', 0);
    updateDigit('minuteTens', 0);
    updateDigit('minuteOnes', 0);
    updateDigit('secondTens', 0);
    updateDigit('secondOnes', 0);
    return;
  }
  
  // 解析时间到各个位
  const hourTens = Math.floor(time.hours / 10);
  const hourOnes = time.hours % 10;
  const minuteTens = Math.floor(time.minutes / 10);
  const minuteOnes = time.minutes % 10;
  const secondTens = Math.floor(time.seconds / 10);
  const secondOnes = time.seconds % 10;
  
  
  // 检查哪些数字发生变化并更新
  updateDigit('hourTens', hourTens);
  updateDigit('hourOnes', hourOnes);
  updateDigit('minuteTens', minuteTens);
  updateDigit('minuteOnes', minuteOnes);
  updateDigit('secondTens', secondTens);
  updateDigit('secondOnes', secondOnes);
}

// 更新单个翻牌数字
function updateDigit(position, newValue) {
  // 如果值没变，不更新也不触发动画
  if (activeDigits[position] === newValue) return;
  
  // 保存前一个值，设置新值
  beforeDigits[position] = activeDigits[position];
  activeDigits[position] = newValue;
  
  // 直接触发该位置的翻转动画
  animations[position] = false;
  
  // 使用 requestAnimationFrame 确保在下一帧开始动画
  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
      animations[position] = true;
    });
  });
}

// 触发翻转动画
function playFlip() {
  isPlaying.value = false;
  
  // 使用 nextTick 确保 DOM 更新
  nextTick(() => {
    setTimeout(() => {
      isPlaying.value = true;
    }, 50);
  });
}

// 定时器
let secondTimer = null;
function startTimers() {
  
  // 先立即执行一次更新
  updateDigits();
  
  // 每秒更新一次
  secondTimer = setInterval(() => {
    // 强制重新计算倒计时时间
    timeRefresher.value++;
    
    // 更新数字
    updateDigits();
  }, 1000);
}

onUnmounted(() => {
  if (secondTimer) {
    clearInterval(secondTimer);
    secondTimer = null;
  }

  // 清理 ResizeObserver
  if (relaxCardRef.value && relaxCardRef.value._resizeObserver) {
    relaxCardRef.value._resizeObserver.disconnect();
    relaxCardRef.value._resizeObserver = null;
  }
  
  // 清理可能存在的resize事件监听器
  window.removeEventListener('resize', () => {
    forceRefreshTrigger.value++;
  });
  
});
</script>

<style lang="scss" scoped>
.relax-card {
  width: 100%;
  height: 100%;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: 0 0.4em 1.2em rgba(0, 0, 0, 0.15);
  /* background: linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%); */
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 1em;
  user-select: none;
  /* 设置基础字体大小，让所有em单位基于此缩放 */
  font-size: clamp(8px, 2.5vw, 16px); /* 响应式基础字体大小 */
  /* CSS自定义属性用于动态缩放 */
  --scale-factor: 1;
  transform: scale(var(--scale-factor));
  transform-origin: center;
}

.relax-card:hover {
  transform: translateY(-0.3em);
  box-shadow: 0 0.6em 1.8em rgba(0, 0, 0, 0.2);
}

.relax-card-content {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

/* 2x1 紧凑横向布局 */
.layout-compact-horizontal .relax-card-content {
  justify-content: center;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.compact-layout {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flip-clock-container.compact {
  padding: 0;
}

.offwork-tip.compact {
  font-size: 16px;
  color: white;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: pulse 2s infinite;
}

/* 2x2 标准布局 - 优化的紧凑设计 */
.standard-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(80, 170, 241, 0.1) 100%);
}

.standard-layout .countdown-header.compact {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4em;
  background: linear-gradient(135deg, #4A90E2 0%, #50AAF1 100%);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  height: 15%;
  min-height: 2em;
  text-shadow: 0 0.1em 0.2em rgba(0, 0, 0, 0.2);
  border-radius: 0.5em 0.5em 0 0;
}

.standard-layout .header-icon {
  font-size: 1.1em;
}

.standard-layout .header-text {
  font-size: 0.9em;
}

.standard-layout .main-clock-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4% 0;
}

.standard-layout .flip-clock-container.compact {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.standard-layout .offwork-tip.compact {
  font-size: 1rem;
  color: #4A90E2;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 0.1em 0.2em rgba(0, 0, 0, 0.1);
}

.standard-layout .info-footer.compact {
  display: flex;
  gap: 0.4em;
  padding: 0 4% 4%;
  height: 25%;
  min-height: 2.5em;
}

.standard-layout .countdown-info.compact {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.4em;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.5em;
  padding: 0.4em 0.5em;
  box-shadow: 0 0.1em 0.3em rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.standard-layout .countdown-info.compact:hover {
  transform: translateY(-0.1em);
  box-shadow: 0 0.2em 0.4em rgba(0, 0, 0, 0.15);
}

.standard-layout .info-label {
  font-size: 0.9em;
  flex-shrink: 0;
}

.standard-layout .info-data {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.standard-layout .info-data span {
  font-size: 0.55em;
  color: #666;
  margin-bottom: 0.1em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.standard-layout .info-data strong {
  font-size: 0.7em;
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 3x2 扩展横向布局 - 响应式三栏设计 */
.extended-horizontal-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
  /* 设置基础字体大小，让所有em单位基于此缩放 */
  font-size: clamp(8px, 2.5vw, 16px);
}

.extended-horizontal-layout .countdown-label.compact {
  font-size: 1em;
  height: 18%;
  min-height: 2.2em;
  line-height: 2.2em;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  text-align: center;
  font-weight: 600;
  text-shadow: 0 0.1em 0.2em rgba(0, 0, 0, 0.2);
  border-radius: 0.5em 0.5em 0 0;
  flex-shrink: 0; /* 防止标题栏被压缩 */
}

.extended-horizontal-layout .main-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 3% 4%;
  gap: 3%;
  overflow: hidden; /* 防止内容溢出 */
}

.extended-horizontal-layout .info-side {
  flex: 0 0 auto; /* 不伸缩，固定宽度 */
  width: 22%; /* 稍微减少宽度，给时钟更多空间 */
  min-width: 3em; /* 设置最小宽度 */
  max-width: 5em; /* 增加最大宽度限制 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.extended-horizontal-layout .clock-center {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0; /* 允许收缩 */
}

.extended-horizontal-layout .countdown-info.side-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.6em;
  padding: 0.5em 0.4em;
  box-shadow: 0 0.1em 0.3em rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  height: 80%; /* 稍微减少高度，避免过于拥挤 */
  min-height: 3.5em; /* 增加最小高度 */
  overflow: hidden; /* 防止内容溢出 */
}

.extended-horizontal-layout .countdown-info.side-card:hover {
  transform: translateY(-0.1em);
  box-shadow: 0 0.3em 0.6em rgba(0, 0, 0, 0.15);
}

.extended-horizontal-layout .side-card .info-icon {
  font-size: 1.1em; /* 增大图标 */
  margin-bottom: 0.2em;
  flex-shrink: 0;
}

.extended-horizontal-layout .side-card .info-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 0; /* 允许收缩 */
  width: 100%;
  flex: 1;
  justify-content: center;
}

.extended-horizontal-layout .side-card .info-content span {
  font-size: 0.65em; /* 增大字体 */
  color: #666;
  margin-bottom: 0.15em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  line-height: 1.2;
}

.extended-horizontal-layout .side-card .info-content strong {
  font-size: 0.8em; /* 增大字体 */
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  line-height: 1.2;
}



.extended-horizontal-layout .flip-clock-container.center {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 3x2布局的响应式优化 - 针对小尺寸容器 */
.extended-horizontal-layout.layout-compact .main-content {
  padding: 2% 3%;
  gap: 2%;
}

.extended-horizontal-layout.layout-compact .info-side {
  width: 20%;
  min-width: 2.5em;
  max-width: 4em;
}

.extended-horizontal-layout.layout-compact .countdown-info.side-card {
  padding: 0.4em 0.3em;
  border-radius: 0.5em;
  min-height: 3em;
}

.extended-horizontal-layout.layout-compact .side-card .info-icon {
  font-size: 1em;
  margin-bottom: 0.1em;
}

.extended-horizontal-layout.layout-compact .side-card .info-content span {
  font-size: 0.55em;
}

.extended-horizontal-layout.layout-compact .side-card .info-content strong {
  font-size: 0.7em;
}

/* 3x3 完整布局 - 优化的正方形设计 */
.full-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
}

.full-layout .countdown-label.enhanced {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 0.1em 0.3em rgba(0, 0, 0, 0.3);
  background: linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%);
  width: 100%;
  text-align: center;
  height: 18%;
  min-height: 2.8em;
  line-height: 2.8em;
  border-radius: 0.8em 0.8em 0 0;
  margin-bottom: 2%;
}

.full-layout .main-clock-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 4% 0;
}

.full-layout .flip-clock-container.centered {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.full-layout .offwork-tip.enhanced {
  font-size: 1.25rem;
  color: #FF6B6B;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 0.1em 0.3em rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

.full-layout .info-grid-section {
  width: 100%;
  padding: 0 6% 6%;
  height: 25%;
  min-height: 3em;
}

.full-layout .countdown-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.6em;
  width: 100%;
  height: 100%;
}

.full-layout .countdown-info.enhanced-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.8em;
  padding: 0.2em 0.5em;
  gap: 0.5em;
  box-shadow: 0 0.1em 0.5em rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
}

.full-layout .countdown-info.enhanced-card:hover {
  transform: translateY(-0.1em);
  box-shadow: 0 0.3em 0.8em rgba(0, 0, 0, 0.15);
}

.full-layout .info-icon {
  font-size: 1.1em;
  flex-shrink: 0;
}

.full-layout .info-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  flex: 1;
}

.full-layout .info-content span {
  font-size: 0.7em;
  color: #666;
  margin-bottom: 0.1em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.full-layout .info-content strong {
  font-size: 0.9em;
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 4x2 宽横向布局 - 响应式优化 */
.wide-horizontal-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 设置基础字体大小，让所有em单位基于此缩放 */
  font-size: clamp(8px, 2.5vw, 16px);
}

.wide-horizontal-layout .countdown-label {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(0.6em);
  font-size: 1.1em;
  font-weight: 600;
  color: white;
  text-shadow: 0 0.1em 0.25em rgba(0, 0, 0, 0.2);
  background: linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%);
  width: 100%;
  text-align: center;
  height: 18%;
  min-height: 2.5em;
  line-height: 2.5em;
  border-radius: 0.5em 0.5em 0 0;
}

.wide-horizontal-layout .main-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0 4%;
  gap: 3%;
  height: 100%;
  overflow: hidden;
}

.wide-horizontal-layout .clock-section {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 0; /* 允许收缩 */
}

.wide-horizontal-layout .info-section {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0; /* 允许收缩 */
  max-width: 35%; /* 限制最大宽度，确保时钟有足够空间 */
}

.wide-horizontal-layout .countdown-footer.horizontal {
  display: flex;
  flex-direction: column;
  gap: 0.6em;
  width: 100%;
  height: 100%;
  justify-content: center;
}

.wide-horizontal-layout .countdown-info.enhanced {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.75em;
  padding: 0.3em 0.5em;
  gap: 0.5em;
  box-shadow: 0 0.125em 0.5em rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-height: 2.5em;
  width: 100%;
  overflow: hidden;
}

.wide-horizontal-layout .countdown-info.enhanced:hover {
  transform: translateY(-0.1em);
  box-shadow: 0 0.25em 0.75em rgba(0, 0, 0, 0.15);
}

.wide-horizontal-layout .info-icon {
  font-size: 1.25em;
  flex-shrink: 0;
}

.wide-horizontal-layout .info-text {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许收缩 */
  flex: 1;
}

.wide-horizontal-layout .info-text span {
  font-size: 0.7em;
  color: #666;
  margin-bottom: 0.125em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.wide-horizontal-layout .info-text strong {
  font-size: 0.875em;
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* 4x2布局的响应式优化 - 针对小尺寸容器 */
.wide-horizontal-layout.layout-compact .main-content {
  padding: 0 2%;
  gap: 2%;
}

.wide-horizontal-layout.layout-compact .info-section {
  max-width: 30%;
}

.wide-horizontal-layout.layout-compact .countdown-footer.horizontal {
  gap: 0.4em;
}

.wide-horizontal-layout.layout-compact .countdown-info.enhanced {
  padding: 0.4em 0.6em;
  border-radius: 0.6em;
  min-height: 2em;
}

.wide-horizontal-layout.layout-compact .info-icon {
  font-size: 1.1em;
}

.wide-horizontal-layout.layout-compact .info-text span {
  font-size: 0.6em;
}

.wide-horizontal-layout.layout-compact .info-text strong {
  font-size: 0.75em;
}

/* 4x3 宽完整布局 - 响应式优化，充分利用高度 */
.wide-full-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 设置基础字体大小，让所有em单位基于此缩放 */
  font-size: clamp(10px, 3vw, 20px);
}

.wide-full-layout .countdown-label.large {
  // font-size: 1.2em;
  height: 50px; /* 减少标题高度，给时钟更多空间 */
  min-height: 2.5em;
  line-height: 2.5em;
  background: linear-gradient(135deg, #F5655A 0%, #FF8E53 100%);
  backdrop-filter: blur(0.5em);
  color: white;
  text-shadow: 0 0.1em 0.2em rgba(0, 0, 0, 0.3);
  text-align: center;
  font-weight: 600;
  border-radius: 0.5em 0.5em 0 0;
  flex-shrink: 0;
}

.wide-full-layout .clock-section.large {
  flex: 1; /* 让时钟区域占据剩余的大部分空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3% 5%; /* 使用百分比padding */
  min-height: 0; /* 允许收缩 */
}

.wide-full-layout .flip-clock-container.large {
  padding: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-remaining {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.wide-full-layout .offwork-tip.large {
  font-size: 1.5em;
  color: white;
  text-shadow: 0 0.1em 0.2em rgba(0, 0, 0, 0.3);
  animation: bounce 2s infinite;
}

.wide-full-layout .info-grid {
  display: flex;
  justify-content: space-around;
  padding: 0 4% 3%; /* 使用百分比padding */
  gap: 3%;
  height: 15%; /* 限制信息区域高度，给时钟更多空间 */
  min-height: 3em;
  flex-shrink: 0;
}

.wide-full-layout .countdown-info.large {
  display: flex;
  align-items: center;
  border-radius: 1em;
  // padding: 0.2em 5em;
  font-size: .7em;
  gap: 0.2em;
  flex: 1;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-height: 2.5em;
}


.wide-full-layout .countdown-info.large span {
  font-size: 0.8em;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wide-full-layout .countdown-info.large strong {
  font-size: 1.1em;
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 4x3布局的紧凑模式 */
.wide-full-layout.layout-compact .countdown-label.large {
  height: 10%;
  min-height: 2em;
  font-size: 1em;
}

.wide-full-layout.layout-compact .info-grid {
  height: 12%;
  padding: 0 3% 2%;
  gap: 2%;
}

.wide-full-layout.layout-compact .countdown-info.large {
  padding: 0.6em 0.8em;
  gap: 0.6em;
  min-height: 2em;
}

.wide-full-layout.layout-compact .countdown-info.large span {
  font-size: 0.7em;
}

.wide-full-layout.layout-compact .countdown-info.large strong {
  font-size: 1em;
}

.countdown-label {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background: linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%);
  width: 100%;
  text-align: center;
  height: 50px;
  line-height: 50px;
}

.flip-clock-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  // padding: 20px 0px 10px 0;
  /* 添加过渡效果，让时钟容器变化更平滑 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.countdown-footer {
  display: flex;
  justify-content: space-between;
}

.countdown-info {
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: rgba(255, 255, 255, 0.2); */
  padding: 0px 16px;
  border-radius: 10px;
  min-width: 80px;
  color: black;
}

.countdown-info span {
  font-size: 14px;
  color: #000000;
  margin-right: 10px;
  // margin-bottom: 4px;
}

.countdown-info strong {
  font-size: 18px;
  color: rgb(0, 0, 0);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 翻牌样式 */
.flip {
  position: relative;
  float: left;
  margin: 0 2px;
  width: 30px;
  height: 50px;
  font-size: 32px;
  font-weight: bold;
  line-height: 50px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.7);
}

.flip li {
  z-index: 1;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  list-style: none;
  /* 添加过渡，使状态变化更平滑 */
  transition: transform 0.05s;
}

.flip li.before,
.flip li.active {
  visibility: visible;
}

.flip li:not(.before):not(.active) {
  visibility: hidden;
}

.flip li a {
  display: block;
  height: 100%;
  perspective: 200px;
}

.flip li a div {
  z-index: 1;
  position: absolute;
  left: 0;
  width: 100%;
  height: 50%;
  overflow: hidden;
}

.flip li a div .shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.flip li a div.up {
  transform-origin: 50% 100%;
  top: 0;
}

.flip li a div.up:after {
  content: "";
  position: absolute;
  top: 24px;
  left: 0;
  z-index: 5;
  width: 100%;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.4);
}

.flip li a div.down {
  transform-origin: 50% 0%;
  bottom: 0;
}

.flip li a div div.inn {
  position: absolute;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 200%;
  color: #ccc;
  text-shadow: 0 1px 2px #000;
  text-align: center;
  background-color: #333;
  border-radius: 4px;
}

.flip li a div.up div.inn {
  top: 0;
}

.flip li a div.down div.inn {
  bottom: 0;
}

/* 翻牌动画 */
.play .before {
  z-index: 3;
}

.play .active {
  animation: asd 0.8s 0.2s linear both;
  z-index: 2;
}

@keyframes asd {
  0% {
    z-index: 2;
  }
  20% {
    z-index: 4;
  }
  100% {
    z-index: 4;
  }
}

.play .active .down {
  z-index: 2;
  animation: turn 0.8s 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes turn {
  0% {
    transform: rotateX(90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.play .before .up {
  z-index: 2;
  animation: turn2 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes turn2 {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(-90deg);
  }
}

/* 阴影效果 */
.play .before .up .shadow {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
  animation: show 0.8s linear both;
}

.play .active .up .shadow {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
  animation: hide 0.8s 0.2s linear both;
}

.play .before .down .shadow {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
  animation: show 0.8s linear both;
}

.play .active .down .shadow {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
  animation: hide 0.8s 0.2s linear both;
}

@keyframes show {
  0% {
    opacity: 0;
  }
  30% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes hide {
  0% {
    opacity: 1;
  }
  70% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

.flip-divider {
  display: flex;
  align-items: center;
  margin: 0 4px;
  color: #333;
  font-size: 24px;
  font-weight: 700;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .flip {
    width: 25px;
    height: 40px;
    font-size: 28px;
    line-height: 40px;
  }
  
  .flip li a div.up:after {
    top: 20px;
  }
  
  .flip-divider {
    margin: 0 2px;
    font-size: 20px;
  }
}

/* 小尺寸卡片调整 */
@media (max-height: 300px) {
  .flip-clock-container {
    padding: 5px 0;
  }
  
  .flip {
    width: 22px;
    height: 34px;
    font-size: 24px;
    line-height: 34px;
    margin: 0 1px;
  }
  
  .flip li a div.up:after {
    top: 17px;
  }
  
  .flip-divider {
    margin: 0 1px;
    font-size: 18px;
  }
  
  .countdown-info {
    padding: 5px 10px;
  }
}

.offwork-tip {
  font-size: 1.75em;
  color: #FF6B6B;
  font-weight: bold;
  text-align: center;
  letter-spacing: 0.1em;
  border-radius: 0.8em;
}
.settingTime{
  position: absolute;
  top: 0.6em;
  right: 0.6em;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
  width: 2em;
  height: 2em;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(0.6em);
  z-index: 10;
}

.settingTime:hover {
  opacity: 1;
  transform: scale(1.1) rotate(90deg);
  background: rgba(255, 255, 255, 0.3);
}

.settingTime .icon {
  width: 1.2em;
  height: 1.2em;
}

/* 新增动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-0.6em);
  }
  60% {
    transform: translateY(-0.3em);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.2em);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 简化的响应式优化 - 主要依赖相对单位自适应 */
@media (max-width: 30em) {
  .relax-card {
    /* 在极小屏幕上稍微减小基础字体 */
    font-size: clamp(6px, 2vw, 12px);
  }
}

/* 适配极窄屏幕 */
@media (max-width: 12em) {
  .countdown-label {
    display: none; /* 隐藏标题为内容腾出空间 */
  }

  .flip-clock-container {
    transform: scale(0.8);
    transform-origin: center;
  }
}

/* 适配极矮屏幕 */
@media (max-height: 12em) {
  .countdown-label {
    display: none; /* 隐藏标题为内容腾出空间 */
  }

  .countdown-footer,
  .info-grid-section,
  .info-footer {
    display: none; /* 隐藏底部信息为时钟腾出空间 */
  }
}

/* 扩展横向布局 - 3x2 */
.relax-card.layout-extended-horizontal {
  background: linear-gradient(to right, #76daff, #86b9ff);
}

.extended-horizontal-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 针对尺寸接近正方形的扩展横向布局进行特殊处理 */
@media (max-aspect-ratio: 1.2/1) {
  .layout-extended-horizontal .flip-clock-container {
    transform: scale(0.9);
    transform-origin: center;
  }
  
  .layout-extended-horizontal .countdown-label.compact {
    padding: 5px 0;
    font-size: 12px;
  }
}

.countdown-label.compact {
  font-size: 0.9em;
  padding: 0.5em 0;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(0.3em);
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}
</style> 