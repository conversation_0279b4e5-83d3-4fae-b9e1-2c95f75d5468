<template>
  <div class="settings-modal-overlay" v-if="visible" @click.self="handleClose">
    <div class="settings-modal">
      <div class="settings-modal-header">
        <div class="settings-modal-title">摸鱼设置</div>
        <button class="close-btn" @click="handleClose">
          <img :src="closeSvg" alt="close" />
        </button>
      </div>
      
      <div class="settings-modal-content">
        <!-- 下班时间设置 -->
        <div class="settings-section">
          <h3 class="settings-title">下班时间</h3>
          <TimePicker 
            v-model:value="localSettings.workEndTime" 
            format="HH:mm"
            placeholder="请选择下班时间"
            class="time-picker"
            @change="(time) => console.log('TimePicker 时间变更:', time)"
          />
        </div>
        
        <!-- 发薪日设置 -->
        <div class="settings-section">
          <h3 class="settings-title">发薪日</h3>
          <div class="salary-day-selector">
            <InputNumber 
              v-model:value="localSettings.salaryDay" 
              :min="1" 
              :max="31"
              :precision="0"
              class="day-input" 
            />
            <span>日</span>
          </div>
          <p class="settings-tip">每月几号发薪</p>
        </div>
        
        <!-- 节假日设置 -->
        <div class="settings-section">
          <h3 class="settings-title">节假日设置</h3>
          <div class="holiday-list">
            <div v-for="(holiday, index) in localSettings.holidays" :key="index" class="holiday-item">
              <Input v-model:value="holiday.name" placeholder="节日名称" class="holiday-name"/>
              <DatePicker 
                v-model:value="holiday.dateObj" 
                @update:value="updateHolidayDate(index)"
                @change="(val) => console.log('DatePicker 组件 change 事件:', index, val)"
                @ok="(val) => console.log('DatePicker 组件 ok 事件:', index, val)"
                @openChange="(open) => console.log('DatePicker 组件 openChange 事件:', index, open)"
                :disabledDate="disabledDate"
                format="YYYY-MM-DD"
                placeholder="节日日期"
                class="holiday-date"
              />
              <Button 
                type="text" 
                danger 
                @click="removeHoliday(index)"
                class="delete-btn"
              >
                <i class="i-carbon-trash-can"></i>
              </Button>
            </div>
            
            <div class="add-holiday">
              <Button @click="addHoliday" type="dashed" block>
                <i class="i-carbon-add"></i> 添加节日
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="settings-modal-footer">
        <Button @click="handleClose">取消</Button>
        <Button type="primary" @click="saveSettings">保存</Button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, reactive, onMounted } from 'vue';
import { Button, TimePicker, DatePicker, Input, InputNumber, message } from 'ant-design-vue';
import dayjs from '../../utils/dayjs'; // 使用配置好的dayjs
import  closeSvg  from '@/assets/modal/close.svg'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  settings: {
    type: Object,
    default: () => ({
      workEndTime: '18:00',
      salaryDay: 15,
      holidays: []
    })
  }
});

const emit = defineEmits(['update:visible', 'update-settings']);

// 创建本地设置副本
const localSettings = reactive({
  workEndTime: props.settings.workEndTime ? dayjs(props.settings.workEndTime, 'HH:mm') : dayjs('18:00', 'HH:mm'),
  salaryDay: props.settings.salaryDay,
  holidays: []
});

// 监听props变化，更新本地设置
watch(() => props.settings, (newSettings) => {
  console.log('监听到 props.settings 变化:', newSettings);
  if (newSettings) {
    try {
      localSettings.workEndTime = newSettings.workEndTime ? dayjs(newSettings.workEndTime, 'HH:mm') : dayjs('18:00', 'HH:mm');
      localSettings.salaryDay = newSettings.salaryDay;
      
      // 将字符串日期转换为 dayjs 对象
      localSettings.holidays = newSettings.holidays.map(holiday => {
        try {
          console.log('处理节假日:', holiday.name, holiday.date);
          const dateObj = holiday.date ? dayjs(holiday.date) : null;
          console.log('转换后的 dayjs 对象:', dateObj);
          console.log('是否为有效的 dayjs 对象:', dateObj ? dayjs.isDayjs(dateObj) : false);
          return {
            name: holiday.name,
            date: holiday.date,
            dateObj: dateObj
          };
        } catch (err) {
          console.error('处理节假日出错:', holiday, err);
          return {
            name: holiday.name,
            date: holiday.date,
            dateObj: null
          };
        }
      });
      console.log('本地设置更新完成:', JSON.stringify(localSettings, (key, value) => {
        if (key === 'dateObj') return value ? '有效的dayjs对象' : null;
        return value;
      }));
    } catch (err) {
      console.error('设置更新出错:', err);
    }
  }
}, { immediate: true, deep: true });

// 添加节日
function addHoliday() {
  console.log('添加新节日');
  const today = dayjs().format('YYYY-MM-DD');
  console.log('今天日期:', today);
  const newHoliday = {
    name: '',
    date: today,
    dateObj: dayjs(today) // 使用 dayjs 对象
  };
  console.log('新节日对象:', newHoliday);
  localSettings.holidays.push(newHoliday);
}

// 移除节日
function removeHoliday(index) {
  console.log('移除节日, 索引:', index);
  localSettings.holidays.splice(index, 1);
}

// 更新节日日期
function updateHolidayDate(index) {
  console.log('更新节日日期, 索引:', index);
  const holiday = localSettings.holidays[index];
  console.log('待更新的节日:', holiday);
  if (holiday.dateObj) {
    // 确保 dateObj 是 dayjs 对象
    console.log('holiday.dateObj 类型:', typeof holiday.dateObj, holiday.dateObj);
    const dateValue = typeof holiday.dateObj === 'string' 
      ? dayjs(holiday.dateObj) 
      : holiday.dateObj;
    
    console.log('处理后的 dateValue:', dateValue);
    
    // 更新日期字符串
    holiday.date = dateValue.format('YYYY-MM-DD');
    holiday.dateObj = dateValue;
    console.log('更新后的节日:', holiday);
  }
}

// 日期选择器-禁用过去的日期
function disabledDate(current) {
  // 禁用过去的日期（今天可选）
  return current && current < dayjs().startOf('day');
}

  // 保存设置
  function saveSettings() {
    console.log('开始保存设置...');
    try {
      // 处理格式以保存
      const formattedSettings = {
        workEndTime: dayjs.isDayjs(localSettings.workEndTime) ? localSettings.workEndTime.format('HH:mm') : localSettings.workEndTime,
      salaryDay: localSettings.salaryDay,
      holidays: localSettings.holidays.map(h => {
        console.log('处理节日保存:', h);
        return {
          name: h.name,
          date: h.date
        };
      }).filter(h => h.name && h.date) // 过滤掉不完整的节日
    };
    
    console.log('保存的设置:', formattedSettings);
    emit('update-settings', formattedSettings);
    message.success('设置已保存');
    handleClose();
  } catch (err) {
    console.error('保存设置出错:', err);
    message.error('保存设置失败');
  }
}

// 关闭弹窗
function handleClose() {
  console.log('关闭设置弹窗');
  emit('update:visible', false);
}

// 组件挂载后，处理节假日日期对象
onMounted(() => {
  console.log('RelaxSettings 组件已挂载');
  console.log('接收到的设置:', props.settings);
  if (props.settings.holidays) {
    localSettings.holidays = props.settings.holidays.map(holiday => {
      console.log('处理节假日:', holiday);
      const dateObj = holiday.date ? dayjs(holiday.date) : null;
      console.log('转换后的 dayjs 对象:', dateObj);
      return {
        name: holiday.name,
        date: holiday.date,
        dateObj: dateObj
      };
    });
  }
  console.log('初始化后的本地设置:', localSettings);
});
</script>

<style scoped>
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.settings-modal {
  width: 90%;
  max-width: 500px;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.4s ease;
}

@keyframes modalSlideIn {
  from { opacity: 0; transform: translateY(50px); }
  to { opacity: 1; transform: translateY(0); }
}

.settings-modal-header {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-modal-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  outline: none;
  border: none;
}


.settings-modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.settings-tip {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.time-picker {
  width: 100%;
}

.salary-day-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.day-input {
  width: 100px;
}

.holiday-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.holiday-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.holiday-name {
  width: 30%;
}

.holiday-date {
  flex: 1;
}

.delete-btn {
  flex-shrink: 0;
}

.add-holiday {
  margin-top: 12px;
}

.settings-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 适配暗色模式 */
:global([data-theme="dark"]) .settings-modal {
  background-color: #1f1f1f;
}

:global([data-theme="dark"]) .settings-modal-header {
  border-color: #333;
}

:global([data-theme="dark"]) .settings-modal-footer {
  border-color: #333;
}

:global([data-theme="dark"]) .close-btn:hover {
  background-color: #333;
}

:global([data-theme="dark"]) .settings-title,
:global([data-theme="dark"]) .settings-modal-title {
  color: #e0e0e0;
}
</style> 