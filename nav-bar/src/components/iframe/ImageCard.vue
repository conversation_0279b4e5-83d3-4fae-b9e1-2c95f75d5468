<template>
  <div class="image-card" :data-id="appId" @wheel="handleCardWheel">
    <!-- API加载中显示 -->
    <div v-if="apiLoading" class="image-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载日历数据中...</div>
    </div>
    <!-- 主要内容区域 - 无论有无图片都可点击 -->
    <div v-else class="image-wrapper"
      @click="handleImageClick"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
      @mouseover="isHovered = true">

      <!-- 无数据或无图片时的占位显示 -->
      <div v-if="!currentApi && isHovered" class="image-placeholder">
        <div class="image-message">
          <div v-if="apiImageData && Array.isArray(apiImageData) && apiImageData.length === 0" class="no-data-content">
            <div class="no-data-icon">📅</div>
            <div class="no-data-text">今天还未发布摸鱼日报，请查看往期</div>
          </div>
          <span v-else-if="apiError">{{ apiError }}</span>
          <span v-else>暂无图片链接</span>
        </div>
      </div>
      <div v-if="loading" class="image-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
      <!-- 加载失败显示 -->
      <!-- 只有在有图片但加载失败时才显示加载失败 -->
      <div v-if="hasError && currentApi && apiImageData && Array.isArray(apiImageData) && apiImageData.length > 0" class="image-error">
        <div class="error-icon">!</div>
        <div class="error-text">加载失败</div>
      </div>
      <!-- 前景图片 - 非悬停状态显示 -->
      <img
        v-show="!isHovered"
        :src="frontBg"
        class="image-body front-image"
        alt="前景图片"
      />
      <!-- 原始图片 - 始终加载但根据悬停状态显示/隐藏 -->
      <img 
        v-show="isHovered && !hasError"
        :src="currentApi" 
        @load="onImageLoad"
        @error="onImageError"
        class="image-body original-image"
        :alt="title"
      />
    </div>

    <!-- 右上角更多按钮 -->
    <div class="more-button"
     @mouseenter="isHovered = true"
     @mouseleave="isHovered = false"
     @mouseover="isHovered = true"
     @click.stop="handleImageClick" title="查看详情">
      <img :src="more" alt="more" />
    </div>

    <!-- 使用Teleport将弹窗内容传送到body -->
    <Teleport to="body">
      <!-- 模态框 -->
      <div v-if="showModal" class="app-modal-overlay" @click.self="handleOverlayClick">
        <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
          <div class="app-modal-header">
            <div class="app-modal-title">{{ title || '图片查看' }}</div>
            <div class="app-modal-spacer"></div>
            <div class="app-modal-controls">
              <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
                <img :src="closeSvg" alt="close" />
              </button>
            </div>
          </div>
          <div class="app-modal-content">
            <!-- 添加弹窗加载动画，在图片完全加载前显示 -->
            <div v-if="modalLoading" class="modal-loading">
              <div class="loading-spinner"></div>
              <div class="loading-text">图片加载中...</div>
            </div>

            <!-- 图片控制面板 -->
            <div class="image-control-panel">
              <div class="control-panel-left">
                <button
                  v-if="apiList.length > 1"
                  class="panel-nav-btn"
                  @click.stop="prevImage"
                  title="上一张">
                  <svg t="1749020179338" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4359" width="16" height="16"><path d="M784.652701 955.6957 346.601985 517.644983c-2.822492-2.822492-2.822492-7.902977 0-11.289967l439.179713-439.179713c6.77398-6.77398 10.725469-16.370452 10.725469-25.966924L796.507166 36.692393c0-20.32194-16.370452-36.692393-36.692393-36.692393l-4.515987 0c-9.596472 0-19.192944 3.951488-25.966924 10.725469L250.072767 489.420066c-12.418964 12.418964-12.418964 32.740904 0 45.159868l477.565601 477.565601c7.338479 7.338479 17.499449 11.854465 28.224917 11.854465l0 0c22.015436 0 40.079383-18.063947 40.079383-40.079383l0 0C796.507166 973.759647 791.99118 963.598677 784.652701 955.6957z" p-id="4360" fill="#333333"></path></svg>
                </button>
                <div @click="openUrl(currentRecord.jumpLink)" class="contentText">
                  <span class="minaSpan">『{{ currentRecord?.source || '摸鱼人日历' }}』{{ currentRecord?.descs || '' }}</span> 
                  
                </div>
                <button
                  v-if="apiList.length > 1"
                  class="panel-nav-btn"
                  @click.stop="nextImage"
                  title="下一张">
                  <svg t="1749020744250" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7037" width="16" height="16"><path d="M246.121279 955.6957l438.050717-438.050717c2.822492-2.822492 2.822492-7.902977 0-11.289967L244.992282 67.175303c-6.77398-6.77398-10.725469-16.370452-10.725469-25.966924L234.266814 36.692393C234.266814 16.370452 250.637266 0 270.959206 0l4.515987 0c9.596472 0 19.192944 3.951488 25.966924 10.725469l478.694598 478.694598c12.418964 12.418964 12.418964 32.740904 0 45.159868l-477.565601 477.565601c-7.338479 7.338479-17.499449 11.854465-28.224917 11.854465l0 0c-22.015436 0-40.079383-18.063947-40.079383-40.079383l0 0C234.266814 973.759647 238.7828 963.598677 246.121279 955.6957z" p-id="7038" fill="#333333"></path></svg>
                </button>
              </div>
              <div class="control-panel-right">
                <!-- 上一天按钮 -->
                <button
                  class="panel-nav-btn date-nav-btn"
                  @click.stop="prevDay"
                  title="上一天">
                  <svg t="1749020179338" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4359" width="16" height="16"><path d="M784.652701 955.6957 346.601985 517.644983c-2.822492-2.822492-2.822492-7.902977 0-11.289967l439.179713-439.179713c6.77398-6.77398 10.725469-16.370452 10.725469-25.966924L796.507166 36.692393c0-20.32194-16.370452-36.692393-36.692393-36.692393l-4.515987 0c-9.596472 0-19.192944 3.951488-25.966924 10.725469L250.072767 489.420066c-12.418964 12.418964 12.418964 32.740904 0 45.159868l477.565601 477.565601c7.338479 7.338479 17.499449 11.854465 28.224917 11.854465l0 0c22.015436 0 40.079383-18.063947 40.079383-40.079383l0 0C796.507166 973.759647 791.99118 963.598677 784.652701 955.6957z" p-id="4360" fill="#333333"></path></svg>
                </button>

                <!-- 日期选择组件 -->
                 <DatePicker
                   :value="selectedDate"
                   format="MM-DD"
                   :show-today="false"
                   :picker="'date'"
                   :value-format="'MM-DD'"
                   :disabled-date="disabledDate"
                   @change="handleDateChange"
                   class="datePicker"
                   placeholder="请选择日期"
                 >
                 </DatePicker>

                 <!-- 下一天按钮 -->
                <button
                  class="panel-nav-btn date-nav-btn"
                  @click.stop="nextDay"
                  title="下一天">
                  <svg t="1749020744250" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7037" width="16" height="16"><path d="M246.121279 955.6957l438.050717-438.050717c2.822492-2.822492 2.822492-7.902977 0-11.289967L244.992282 67.175303c-6.77398-6.77398-10.725469-16.370452-10.725469-25.966924L234.266814 36.692393C234.266814 16.370452 250.637266 0 270.959206 0l4.515987 0c9.596472 0 19.192944 3.951488 25.966924 10.725469l478.694598 478.694598c12.418964 12.418964 12.418964 32.740904 0 45.159868l-477.565601 477.565601c-7.338479 7.338479-17.499449 11.854465-28.224917 11.854465l0 0c-22.015436 0-40.079383-18.063947-40.079383-40.079383l0 0C234.266814 973.759647 238.7828 963.598677 246.121279 955.6957z" p-id="7038" fill="#333333"></path></svg>
                </button>
              </div>
            </div>

            <div class="image-viewer" @wheel="handleWheel">
              <!-- 弹窗中的无数据显示 - 优先检查 -->
              <div v-if="apiImageData && Array.isArray(apiImageData) && apiImageData.length === 0" class="modal-no-data">
                <div class="no-data-icon">📅</div>
                <div class="no-data-text">今天还未发布摸鱼日报，请查看往期</div>
              </div>
              <!-- 弹窗中的图片加载失败显示 -->
              <div v-else-if="hasError && currentApi" class="modal-image-error">
                <div class="error-icon">!</div>
                <div class="error-text">图片加载失败</div>
                <div class="error-url">{{ currentApi }}</div>
              </div>
              <img
                v-show="currentApi && !hasError && apiImageData && Array.isArray(apiImageData) && apiImageData.length > 0"
                :src="currentApi"
                alt="preview"
                @load="onModalImageLoad"
                @error="onModalImageError"
                @click="handleModalImageClick"
              />
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted, onUnmounted, computed, watch, Teleport } from 'vue';
import { useSettingStore } from '@/stores/setting.js';
import  closeSvg  from '@/assets/modal/close.svg'
import frontBg from '@/assets/imageCard/imgFront2.png'
import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';
import { getCalendarList } from '@/api/imageCard.js';
  import more from '@/assets/icon/more.svg'


const settingStore = useSettingStore();

const props = defineProps({
  appId: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  headerColor: {
    type: String,
    default: ''
  }
});

// 确保组件注册
const ADatePicker = DatePicker;

const loading = ref(false);
const hasError = ref(false);
const modalLoading = ref(true); // 新增：弹窗图片加载状态
// 新增：鼠标悬停状态
const isHovered = ref(false);
// 新增：前景图片错误状态
const frontImageError = ref(false);

// API数据相关的响应式变量
const apiImageData = ref(null); // 存储API返回的图片数据
const apiLoading = ref(false); // API调用loading状态
const apiError = ref(null); // API调用错误信息

// API数据列表
const apiList = computed(() => {
  // 只使用API数据
  if (apiImageData.value && Array.isArray(apiImageData.value) && apiImageData.value.length > 0) {
    return apiImageData.value;
  }
  return [];
})

// 滚轮事件相关变量
const lastWheelTime = ref(0); // 上次滚轮事件时间戳
const WHEEL_COOLDOWN = 500; // 滚轮事件冷却时间（毫秒）
const isWheelScrolling = ref(false); // 滚轮滚动状态标记
// 当前选中的API索引
const currentApiIndex = ref(0)
// 当前选中的记录
const currentRecord = computed(() => {
  if (apiList.value.length > 0) {
    const currentItem = apiList.value[currentApiIndex.value];
    // 如果是API数据对象，直接返回
    if (currentItem && typeof currentItem === 'object') {
      return currentItem;
    }
  }
  return null;
})
// 当前API地址 - 从API数据数组中获取当前记录的url字段
const currentApi = computed(() => {
  if (apiList.value.length > 0) {
    const currentItem = apiList.value[currentApiIndex.value];
    // 返回API数据对象的url字段
    if (currentItem && typeof currentItem === 'object' && currentItem.url) {
      return currentItem.url;
    }
  }
  return '';
})

// 模态框状态
const showModal = ref(false);
const isFullscreen = ref(false);
// 选中的日期 - 初始化为今天
const selectedDate = ref(dayjs().format('MM-DD'));

// 格式化时间为月日格式
const formmatTime = computed(() => {
  return dayjs().format('MM-DD');
});

// 加载日历数据的函数
const loadCalendarData = async (date) => {
  try {
    apiLoading.value = true;
    apiError.value = null;

    // 将MM-DD格式转换为完整日期格式，用于API调用
    const currentYear = dayjs().year();
    const fullDate = dayjs(`${currentYear}-${date}`, 'YYYY-MM-DD');
    const timeString = fullDate.format('YYYY-MM-DD');

    const response = await getCalendarList(timeString);

    if (response && response.data && Array.isArray(response.data)) {
      apiImageData.value = response.data;
      // 重置索引到第一条记录
      currentApiIndex.value = 0;
    } else {
      throw new Error('API返回数据格式错误或无数据');
    }
  } catch (error) {
    console.error('获取日历数据失败:', error);
    apiError.value = error.message || '获取数据失败';
    apiImageData.value = null;
  } finally {
    apiLoading.value = false;
  }
};

// 禁用未来日期的方法
const disabledDate = (current) => {
  // 禁用今天之后的所有日期
  return current && current > dayjs().endOf('day');
};

// 切换到前一天
const prevDay = async () => {
  const currentDate = dayjs(selectedDate.value, 'MM-DD');
  const prevDate = currentDate.subtract(1, 'day');
  selectedDate.value = prevDate.format('MM-DD');
  // 触发日期变更处理
  await handleDateChange(selectedDate.value);
};

// 切换到后一天
const nextDay = async () => {
  const currentDate = dayjs(selectedDate.value, 'MM-DD');
  const nextDate = currentDate.add(1, 'day');
  // 确保不超过今天
  if (nextDate.isSameOrBefore(dayjs(), 'day')) {
    selectedDate.value = nextDate.format('MM-DD');
    // 触发日期变更处理
    await handleDateChange(selectedDate.value);
  }
};

// 切换到下一个图片（在当前API数组中切换）
const nextImage = () => {
  if (apiList.value.length > 1) {
    // 在非弹窗状态下设置loading状态
    if (!showModal.value) {
      loading.value = true;
    }
    currentApiIndex.value = (currentApiIndex.value + 1) % apiList.value.length;
  }
};

// 切换到上一个图片（在当前API数组中切换）
const prevImage = () => {
  if (apiList.value.length > 1) {
    // 在非弹窗状态下设置loading状态
    if (!showModal.value) {
      loading.value = true;
    }
    currentApiIndex.value = (currentApiIndex.value - 1 + apiList.value.length) % apiList.value.length;
  }
};

// 移除定时器和随机切换逻辑，只使用API数据
// 组件挂载时加载今天的日历数据
onMounted(async () => {
  // 加载今天的日历数据
  const today = dayjs().format('MM-DD');
  await loadCalendarData(today);

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
})
// 组件卸载时的清理工作
onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
})

// 点击图片打开弹窗
const handleImageClick = () => {
  showModal.value = true;
  modalLoading.value = true; // 重置加载状态
  // 添加body类以禁用滚动
  document.body.classList.add('modal-fullscreen-active');
};

// 弹窗中点击图片跳转URL
const handleModalImageClick = () => {
  // 只使用API数据中的URL
  if (currentRecord.value && currentRecord.value.url) {
    window.open(currentRecord.value.url, '_blank');
  }
};

const openUrl = (url => {
  window.open(url, '_blank');
})

// 关闭模态框
const closeModal = () => {
  showModal.value = false;
  // 移除body类以启用滚动
  document.body.classList.remove('modal-fullscreen-active');
  // 如果已全屏，退出全屏
  if (isFullscreen.value) {
    isFullscreen.value = false;
  }
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    closeModal();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    closeModal();
  }
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
};

// 在新窗口打开
const openInNewWindow = () => {
  if (currentApi.value) {
    window.open(currentApi.value, '_blank');
  }
};

const onImageLoad = () => {
  loading.value = false;
  hasError.value = false;
  // 图片加载完成，允许显示，不再等待
};

const onImageError = () => {
  loading.value = false;
  hasError.value = true;
  console.error('图片加载失败:', currentApi.value);
};

// 监听键盘事件
const handleKeyDown = (e) => {
  if (!showModal.value) return;

  if (e.key === 'Escape') {
    closeModal();
  } else if (e.key === 'ArrowLeft') {
    // 左箭头，切换到上一天
    e.preventDefault(); // 防止页面滚动
    prevDay();
  } else if (e.key === 'ArrowRight') {
    // 右箭头，切换到下一天
    e.preventDefault(); // 防止页面滚动
    nextDay();
  } else if (e.key === 'ArrowUp') {
    // 上箭头，显示上一张图片
    e.preventDefault(); // 防止页面滚动
    prevImage();
  } else if (e.key === 'ArrowDown') {
    // 下箭头，显示下一张图片
    e.preventDefault(); // 防止页面滚动
    nextImage();
  }
};

// 处理滚轮事件（弹窗内使用）
const handleWheel = (e) => {
  // 防止页面滚动和事件冒泡
  e.preventDefault();
  e.stopPropagation();

  // 获取当前时间
  const now = Date.now();

  // 检查是否在冷却期
  if (now - lastWheelTime.value < WHEEL_COOLDOWN) {
    return;
  }

  // 如果正在滚动处理中，不允许新的滚轮事件
  if (isWheelScrolling.value) {
    return;
  }

  // 只有在有多张图片时才响应滚轮事件
  if (apiList.value.length <= 1) {
    return;
  }

  // 立即设置滚动状态，防止重复触发
  isWheelScrolling.value = true;

  // 立即更新最后滚轮时间
  lastWheelTime.value = now;

  // 根据滚轮方向切换图片
  if (e.deltaY > 0) {
    // 向下滚动，下一张图片
    nextImage();
  } else if (e.deltaY < 0) {
    // 向上滚动，上一张图片
    prevImage();
  }

  // 重置滚动状态
  setTimeout(() => {
    isWheelScrolling.value = false;
  }, 100);
};

// 处理组件根元素的滚轮事件（内嵌状态下使用）
const handleCardWheel = (e) => {
  // 防止事件冒泡到父组件，避免触发页面滚动
  e.stopPropagation();
  e.preventDefault();

  // 如果当前处于弹窗状态，不处理根元素的滚轮事件
  if (showModal.value) {
    return;
  }

  // 调用原有的滚轮处理逻辑
  handleWheel(e);
};

// 键盘事件监听已在上面的onMounted和onUnmounted中处理

// 改进弹窗图片加载错误处理
const onModalImageLoad = (event) => {
  // 标记加载完成
  modalLoading.value = false;
  loading.value = false;
  hasError.value = false;
  
  // 获取加载的图片
  const img = event.target;
  
  // 检查图片尺寸是否适合容器
  const viewer = img.closest('.image-viewer');
  if (img && viewer) {
    // 重置图片样式以适应不同尺寸的图片
    const imgWidth = img.naturalWidth;
    const imgHeight = img.naturalHeight;
    const viewerWidth = viewer.clientWidth; // 不再需要减去按钮空间
    const viewerHeight = viewer.clientHeight;
    
    // 根据图片比例设置样式
    const imageRatio = imgWidth / imgHeight;
    const containerRatio = viewerWidth / viewerHeight;
    
    // if (imageRatio > containerRatio) {
    //   // 宽图片 - 限制宽度
    //   img.style.width = 'auto';
    //   img.style.height = '100%';
    // } else {
    //   // 长图片 - 限制高度
    //   img.style.width = 'auto';
    //   img.style.height = '100%';
    // }
  }
};

// 添加弹窗图片加载错误处理
const onModalImageError = () => {
  modalLoading.value = false;
  loading.value = false;
  hasError.value = true;
  console.error('弹窗图片加载失败:', currentApi.value);
};

// 监听currentApi变化，切换图片时重置loading状态
watch(() => currentApi.value, (newApi) => {
  if (showModal.value) {
    modalLoading.value = true;
  }
  
  // 预加载新的API图片
  if (newApi) {
    const preloadImg = new Image();
    preloadImg.src = newApi;
    preloadImg.onload = () => {
      if (currentApi.value === newApi) { // 确保还是同一张图片
        loading.value = false;
      }
    };
  }
});

// 日期变更处理
const handleDateChange = async (date) => {
  // 同步更新selectedDate状态
  if (date) {
    selectedDate.value = date;
    // 调用API获取指定日期的日历数据
    await loadCalendarData(date);
  }
};
</script>

<style lang="scss" scoped>
.image-card {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 右上角更多按钮 */
.more-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(84, 84, 84, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  opacity: 0;
  transform: scale(0.8);
}

.image-card:hover{
  opacity: 1 !important;
}

.image-card:hover .more-button {
  opacity: 1;
  transform: scale(1);
}

.more-button:hover {
  // background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.more-button img {
  width: 14px;
  height: 14px;
  opacity: 0.7;
}

.image-body {
  width: 100%;
  height: 100%;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  flex: 1;
  overflow: hidden;
  cursor: pointer; /* 移动指针样式到wrapper */
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  z-index: 2;
}

/* 加载失败样式 */
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  z-index: 2;
}

.error-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ff5f57;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.error-text {
  font-size: 14px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 120, 212, 0.2);
  border-top-color: rgba(0, 120, 212, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  flex: 1;
}

.image-message {
  padding: 8px 16px;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #555;
  border-radius: 20px;
  font-size: 14px;
}

/* 主界面无数据显示样式 */
.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.no-data-content .no-data-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.no-data-content .no-data-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 删除卡片上的左右导航按钮样式 */

/* 前景图片样式 */
.front-image {
  object-fit: cover; /* 确保图片填满容器 */
  transition: opacity 0.15s ease; /* 加快过渡效果 */
}

/* 原始图片样式 */
.original-image {
  transition: opacity 0.15s ease; /* 加快过渡效果 */
}

/* 模态框样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 95%; /* 增大宽度 */
  height: 90%; /* 增大高度 */
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 520px; /* 增大最大宽度 */
  max-height: 90vh; /* 限制最大高度 */
  position: relative;
  z-index: 1001;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 99999 !important;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 15px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  border: none;
}

.control-btn .icon {
  opacity: 0.7;
  font-size: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #f7f7f7; /* 改为白色背景 */
}

.image-viewer img {
  max-width: 100%;
  max-height: 100%;
  // object-fit: contain;
}

/* 弹窗中的图片加载失败样式 */
.modal-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #333;
  padding: 30px;
  text-align: center;
}

.modal-image-error .error-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #ff5f57;
  font-size: 36px;
  margin-bottom: 20px;
}

.modal-image-error .error-text {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.modal-image-error .error-url {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  max-width: 80%;
  word-break: break-all;
}

/* 模态框内的左右导航按钮 */
.modal-nav-btn {
  padding: 15px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.4); /* 半透明背景 */
  border: none;
  width: 50px; /* 减小按钮宽度 */
  height: 50px; /* 减小按钮高度 */
  font-size: 36px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6; /* 默认半透明 */
  z-index: 2;
  border-radius: 50%;
  backdrop-filter: blur(2px); /* 添加模糊效果使按钮在任何背景上都可见 */
  svg {
    width: 24px; /* 减小SVG图标大小 */
    height: 24px;
  }
}

.modal-prev-btn {
  left: 15px;
}

.modal-next-btn {
  right: 15px;
}

.image-viewer:hover .modal-nav-btn {
  opacity: 0.9; /* 悬停时增加不透明度 */
}

.modal-nav-btn:hover {
  background: rgba(0, 0, 0, 0.7); /* 悬停时更暗的背景 */
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 弹窗加载中的样式 */
.modal-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 5;
}

.modal-loading .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modal-loading .loading-text {
  margin-top: 15px;
  font-size: 16px;
  color: #333;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 图片控制面板样式 */
.image-control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px; /* 增加底部padding，为日历提供空间 */
  color: #333; /* 深色文字 */
  z-index: 3;
}

.control-panel-left {
  display: flex;
  gap: 10px;
  background: #ffffff;
  border-radius: 30px;
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0px 10px;
  flex: 1;
  margin-right: 10px;
  max-width: 320px;
  span{
    font-size: 12px;
    white-space: nowrap;
  }
  span:hover{
    color: #1890ff !important;
    text-decoration: underline;
  }
  .minaSpan{
    color: #484646;
  }
  .sickSpan{
    color:#959595;
  }
}

.control-panel-right {
  display: flex;
  gap: 3px;
  align-items: center;
  position: relative;
  background-color: #ffffff;
  border-radius: 32px;
  z-index: 10; /* 确保日历显示在最上层 */
  .datePicker{
    border-radius: 30px;
  }
  :deep(.ant-picker){
    border-radius: 30px;
    height: 30px;
    width: 100px;
    border: none;
    outline: none;
    :focus{
      border: none;
      outline: none;
      box-shadow: unset;
    }
  }
}

/* 日期导航按钮样式 */
.date-nav-btn {
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.panel-nav-btn {
  width: 22px;
  height: 22px;
  padding: 6px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  background-color: rgba(0, 0, 0, 0.1); /* 改为浅灰色 */
  color: #333; /* 深色图标 */
  background: #ffffff;
}

.panel-nav-btn:hover {
  background-color: rgba(0, 0, 0, 0.2); /* 改为更深的灰色 */
}

/* 为Ant Design Vue的DatePicker添加一些样式 */
:deep(.ant-picker) {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #333;
}

:deep(.ant-picker-input > input) {
  color: #333;
  font-size: 12px;
}

:deep(.ant-picker-suffix) {
  color: rgba(0, 0, 0, 0.45);
}

:deep(.ant-picker:hover),
:deep(.ant-picker-focused) {
  border-color: #1890ff;
}

/* 图片查看器背景 */
.image-viewer {
  width: 100%;
  height: calc(100% - 60px); /* 减去控制面板高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
  overflow: hidden; /* 防止出现滚动条 */
  padding: 0px 20px;
  overflow: hidden;
  img{
      border-radius: 10px;
  }
}

/* 修改SVG图标颜色 */
.panel-nav-btn svg path {
  fill: #333; /* 设置SVG路径颜色为深色 */
}

/* 确保日历弹出框有足够高的z-index */
:deep(.ant-picker-dropdown) {
  z-index: 2000 !important;
}

/* 弹窗中的无数据显示样式 */
.modal-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #333;
  padding: 30px;
  text-align: center;
  height: 100%;
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.no-data-text {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

.contentText{
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
