<template>
  <div class="enhanced-iframe-card" :data-card-id="cardId">
    <!-- 顶部链接选择栏 -->
    <div class="card-header">
      <div class="link-selector">
        <ASelect
          v-model:value="selectedPreset"
          @change="handlePresetChange"
          placeholder="选择预设链接"
          size="large"
          class="preset-select"
          :dropdown-style="{ borderRadius: '12px' }"
        >
          <ASelectOption
            v-for="preset in presetLinks"
            :key="preset.value"
            :value="preset.value"
          >
            <div class="option-content">
              <span class="option-icon">🌐</span>
              <span class="option-text">{{ preset.label }}</span>
            </div>
          </ASelectOption>
          <ASelectOption value="custom">
            <div class="option-content">
              <span class="option-icon">✏️</span>
              <span class="option-text">自定义URL</span>
            </div>
          </ASelectOption>
        </ASelect>
      </div>

      <!-- 自定义URL输入 -->
      <div v-if="showCustomInput" class="custom-url-input">
        <AInput
          v-model:value="customUrl"
          @pressEnter="applyCustomUrl"
          @blur="applyCustomUrl"
          placeholder="输入自定义URL并按回车确认"
          size="large"
          class="url-input"
        >
          <template #prefix>
            <span class="url-prefix">🔗</span>
          </template>
        </AInput>
      </div>
    </div>

    <!-- iframe内容区域 -->
    <div class="iframe-container">
      <div v-if="!currentUrl" class="iframe-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">🌐</div>
          <div class="placeholder-text">请选择或输入网页链接</div>
        </div>
      </div>
      
      <div v-else class="iframe-wrapper">
        <div v-if="loading" class="iframe-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
        
        <iframe 
          :src="currentUrl" 
          frameborder="0" 
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
          referrerpolicy="no-referrer"
          @load="onIframeLoad"
          @error="onIframeError"
          loading="lazy"
          class="iframe-content"
        ></iframe>
        
        <div v-if="loadError" class="iframe-error">
          <div class="error-icon">⚠️</div>
          <div class="error-message">页面加载失败</div>
          <button @click="retryLoad" class="retry-btn">重试</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineProps, defineEmits } from 'vue'
import { Select, Input } from 'ant-design-vue'
import { aiConversations } from '@/api/ai.js'

// 注册组件
const ASelect = Select
const ASelectOption = Select.Option
const AInput = Input

const props = defineProps({
  cardId: {
    type: String,
    required: true
  },
  url: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['url-change'])

// 预设链接列表
const presetLinks = ref([
  { label: 'Google', value: 'https://www.google.com' },
  { label: '百度', value: 'https://www.baidu.com' },
  { label: 'GitHub', value: 'https://github.com' },
  { label: 'Stack Overflow', value: 'https://stackoverflow.com' },
  { label: 'MDN Web Docs', value: 'https://developer.mozilla.org' },
  { label: 'Vue.js', value: 'https://vuejs.org' },
  { label: 'Ant Design Vue', value: 'https://antdv.com' }
])

// 组件状态
const selectedPreset = ref('')
const customUrl = ref('')
const currentUrl = ref(props.url)
const loading = ref(false)
const loadError = ref(false)

// 计算属性
const showCustomInput = computed(() => selectedPreset.value === 'custom')

// 处理预设链接选择
const handlePresetChange = () => {
  if (selectedPreset.value && selectedPreset.value !== 'custom') {
    currentUrl.value = selectedPreset.value
    emit('url-change', currentUrl.value)
    saveConfig()
  } else if (selectedPreset.value === 'custom') {
    // 显示自定义输入框
    customUrl.value = currentUrl.value
  }
}

// 应用自定义URL
const applyCustomUrl = () => {
  if (customUrl.value.trim()) {
    let url = customUrl.value.trim()
    // 自动添加协议
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    currentUrl.value = url
    emit('url-change', currentUrl.value)
    saveConfig()
  }
}

// iframe加载完成
const onIframeLoad = () => {
  loading.value = false
  loadError.value = false
}

// iframe加载错误
const onIframeError = () => {
  loading.value = false
  loadError.value = true
}

// 刷新iframe
const refreshIframe = () => {
  if (currentUrl.value) {
    loading.value = true
    loadError.value = false
    // 使用更精确的选择器来避免多个卡片时的冲突
    const iframe = document.querySelector(`[data-card-id="${props.cardId}"] iframe`)
    if (iframe) {
      iframe.src = iframe.src
    }
  }
}

// 重试加载
const retryLoad = () => {
  refreshIframe()
}

// 保存配置
const saveConfig = () => {
  const config = {
    selectedPreset: selectedPreset.value,
    customUrl: customUrl.value,
    currentUrl: currentUrl.value
  }
  localStorage.setItem(`enhanced-iframe-card-${props.cardId}`, JSON.stringify(config))
}

// 加载配置
const loadConfig = () => {
  try {
    const savedConfig = localStorage.getItem(`enhanced-iframe-card-${props.cardId}`)
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      selectedPreset.value = config.selectedPreset || ''
      customUrl.value = config.customUrl || ''
      currentUrl.value = config.currentUrl || props.url
      return false // 不是首次进入
    } else if (props.url) {
      currentUrl.value = props.url
      return false // 有props.url，不需要随机初始化
    }
    return true // 首次进入，需要随机初始化
  } catch (error) {
    console.error('加载卡片配置失败:', error)
    return true // 出错时也进行随机初始化
  }
}

// 按顺序初始化URL
const sequentialInitialize = () => {
  if (presetLinks.value.length > 0) {
    // 从cardId中提取数字索引（如'card-1' -> 0, 'card-2' -> 1）
    let index = 0
    try {
      const match = props.cardId.match(/(\d+)/)
      if (match) {
        // 将cardId中的数字转换为数组索引（从1开始的cardId转换为从0开始的索引）
        index = parseInt(match[1]) - 1
        // 确保索引在有效范围内，使用模运算处理越界情况
        index = Math.max(0, index) % presetLinks.value.length
      }
    } catch (error) {
      console.warn('解析cardId失败，使用默认索引0:', error)
      index = 0
    }

    const sequentialPreset = presetLinks.value[index]
    selectedPreset.value = sequentialPreset.value
    currentUrl.value = sequentialPreset.value
    emit('url-change', currentUrl.value)
    saveConfig()
  }
}

const getAiUrlList = (needRandomInit = false) => {
  aiConversations().then(res => {
    if(res.status == 200) {
      presetLinks.value = res.data.map(item => ({
        label: item.name,
        value: item.url
      }))
    }
    // API数据加载完成后，检查是否需要按顺序初始化
    if (needRandomInit) {
      sequentialInitialize()
    }
  })
}

// 监听URL变化
watch(() => currentUrl.value, (newUrl) => {
  if (newUrl) {
    loading.value = true
    loadError.value = false
  }
})

// 组件挂载时加载配置
onMounted(() => {
  const isFirstTime = loadConfig()
  getAiUrlList(isFirstTime)
})
</script>

<style lang="scss" scoped>
.enhanced-iframe-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
  position: relative;
}

.link-selector {
  width: 300px;
}

.preset-select {
  width: 100%;

  :deep(.ant-select-selector) {
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    background: #fff !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #495057 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    height: 38px !important;

    &:hover {
      border-color: #71C6FF !important;
      box-shadow: 0 6px 16px rgba(113, 198, 255, 0.2) !important;
    }
  }

  :deep(.ant-select-focused .ant-select-selector) {
    border-color: #71C6FF !important;
    box-shadow: 0 0 0 3px rgba(113, 198, 255, 0.15) !important;
  }

  :deep(.ant-select-selection-placeholder) {
    color: #adb5bd !important;
    font-weight: 400 !important;
  }

  :deep(.ant-select-selection-item) {
    display: flex !important;
    align-items: center !important;
    font-weight: 500 !important;
  }
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-icon {
  font-size: 16px;
}

.option-text {
  font-weight: 500;
}

.custom-url-input {
  width: 300px;
  margin-top: 16px;
}

.url-input {
  width: 100%;

  :deep(.ant-input) {
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    background: #fff !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #495057 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    height: 48px !important;

    &:hover {
      border-color: #71C6FF !important;
      box-shadow: 0 6px 16px rgba(113, 198, 255, 0.2) !important;
    }

    &:focus {
      border-color: #71C6FF !important;
      box-shadow: 0 0 0 3px rgba(113, 198, 255, 0.15) !important;
    }

    &::placeholder {
      color: #adb5bd !important;
      font-weight: 400 !important;
    }
  }

  :deep(.ant-input-prefix) {
    margin-right: 8px;
  }
}

.url-prefix {
  font-size: 16px;
  color: #71C6FF;
}

.iframe-container {
  flex: 1;
  position: relative;
  min-height: 0;
  background: #f8f9fa;
}

.iframe-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.placeholder-content {
  text-align: center;
  color: #6c757d;
  padding: 40px 20px;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.placeholder-text {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.8;
}

.iframe-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.iframe-content {
  width: 100%;
  height: 100%;
  border: none;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.95);
  backdrop-filter: blur(4px);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #71C6FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 2px 8px rgba(113, 198, 255, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 16px;
  color: #495057;
  font-size: 16px;
  font-weight: 500;
}

.iframe-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  z-index: 10;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.error-message {
  color: #e53e3e;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 24px;
  text-align: center;
}

.retry-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #71C6FF 0%, #5bb3ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(113, 198, 255, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(113, 198, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>

<!-- 全局样式，用于优化Ant Design Vue下拉框 -->
<style>
.ant-select-dropdown {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  overflow: hidden !important;
}

.ant-select-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
}

.ant-select-item-option-selected {
  background: linear-gradient(135deg, #71C6FF 0%, #5bb3ff 100%) !important;
  color: #fff !important;
  font-weight: 600 !important;
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background: rgba(113, 198, 255, 0.1) !important;
  color: #71C6FF !important;
}

.ant-select-item-option:hover {
  background: rgba(113, 198, 255, 0.08) !important;
}
</style>
