<template>
  <div class="iframe-card" :data-id="appId">
    <div v-if="!url" class="iframe-placeholder">
      <div class="iframe-message">
        <span>暂无网页链接</span>
      </div>
    </div>
    <div v-else class="iframe-wrapper">
      <div v-if="loading" class="iframe-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
      <iframe 
        :src="url" 
        frameborder="0" 
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        referrerpolicy="no-referrer"
        @load="onIframeLoad"
        loading="lazy"
        class="iframe-body"
      ></iframe>
    </div>
    <div class="iframe-controls" v-if="url">
      <button class="iframe-btn refresh" @click="refreshIframe" title="刷新">
        <span>🔄</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue';

const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  appId: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  headerColor: {
    type: String,
    default: ''
  }
});

const loading = ref(!!props.url);

const refreshIframe = () => {
  const iframe = document.querySelector(`.iframe-card[data-id="${props.appId}"] iframe`);
  if (iframe) {
    loading.value = true;
    iframe.src = iframe.src;
  }
};

const onIframeLoad = () => {
  loading.value = false;
};

// 处理iframe加载失败
onMounted(() => {
  const handleIframeError = () => {
    const iframe = document.querySelector(`.iframe-card[data-id="${props.appId}"] iframe`);
    if (iframe) {
      iframe.addEventListener('error', () => {
        loading.value = false;
        console.error('网页加载失败:', props.url);
      });
    }
  };
  
  setTimeout(handleIframeError, 1000);
});
</script>

<style lang="scss" scoped>
.iframe-card {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.iframe-body{
  width: 100%;
  height: 100%;
}
::v-deep(.iframe-body){
  img{
    width: 100%;
    height: 100%;
  }
}

.iframe-header {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  z-index: 5;
  color: white;
}

.iframe-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.iframe-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  flex: 1;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
  position: relative;
  z-index: 1;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  z-index: 2;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(var(--accent-color-rgb, 0, 120, 212), 0.2);
  border-top-color: rgba(var(--accent-color-rgb, 0, 120, 212), 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.iframe-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  flex: 1;
}

.iframe-message {
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.1);
  color: #555;
  border-radius: 20px;
  font-size: 14px;
}

.iframe-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 10;
}

.iframe-card:hover .iframe-controls {
  opacity: 1;
}

.iframe-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.iframe-btn:hover {
  background: white;
  transform: scale(1.1);
}
</style> 