<template>
  <div v-if="gridStyle == '2x1'" class="wooden-fish-container" :style="{backgroundImage: `url(https://tu.ltyuanfang.cn/api/fengjing.php)`, backgroundPosition:'center',backgroundSize:'cover'}">
      心诚则灵
  </div>
  <div v-else class="wooden-fish-container" @click="knock">
    <!-- 将计数显示移至右上角 -->
    <div class="count-display">功德数: {{ count }}</div>
    
    <div class="wooden-fish-wrapper">
      <!-- 木鱼棍 - 移到木鱼容器内，确保相对位置一致 -->
      <div class="wooden-stick" :class="{ 'knocking': isKnocking }">
        <img src="/src/assets/stick.svg" alt="木鱼棍" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iMjAwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNjAiIHg9IjE1IiB5PSIyMCIgZmlsbD0iIzhhNDgyNCIgcng9IjEwIiAvPjxlbGxpcHNlIGN4PSIyNSIgY3k9IjIwIiByeD0iMTUiIHJ5PSIxMCIgZmlsbD0iIzZkMzMxNCIgLz48L3N2Zz4='" />
      </div>
      
      <div class="wooden-fish">
        <!-- 木鱼图片，后续可替换为实际图片 -->
        <img src="/src/assets/muyu.svg" alt="木鱼" />
        
        <!-- 功德变化文字动画 -->
        <div 
          v-if="showMerit" 
          class="merit-text"
          :style="meritStyle"
        >
          功德{{ meritChange > 0 ? `+${meritChange}` : meritChange }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import muyu from '@/assets/audio/muyu.mp3'
// 计数器，记录功德总数
const count = ref(0);
// 本次功德变化
const meritChange = ref(0);
// 音效对象
const sound = ref(null);
// 是否显示功德文字
const showMerit = ref(false);
// 功德文字位置
const meritPosition = ref({ x: 0, y: 0 });
// 功德样式
const meritStyle = ref({});
// 控制木鱼棍的动画状态
const isKnocking = ref(false);

const props = defineProps({
  size: {
    type: Object,
    default: () => ({ w: 2, h: 2 })
  }
});

watch(() => props.size, (newSize, oldSize) => {
  gridStyle.value = newSize.w + 'x' + newSize.h
})

const gridStyle = ref()



/**
 * 初始化音效
 */
onMounted(() => {

  gridStyle.value = props.size.w + 'x' + props.size.h

  // 由于暂时没有实际音效文件，这里只创建音频对象但不加载
  sound.value = new Audio(muyu);
  
});

/**
 * 敲击木鱼
 * @param {MouseEvent} event - 鼠标点击事件
 */
const knock = (event) => {
  if (isKnocking.value) return; // 防止连击
  
  // 随机生成本次功德变化（-1000到+1000的整数）
  const change = Math.floor(Math.random() * 2001) - 1000;
  meritChange.value = change;
  count.value += change;
  isKnocking.value = true;
  
  // 播放音效
  if (sound.value) {
    sound.value.currentTime = 0;
    sound.value.play().catch(error => console.error('音频播放失败:', error));
  }
  
  // 显示功德变化的动画效果
  showMeritText();
  
  // 让木鱼图片有缩放动画
  const woodenFishImage = document.querySelector('.wooden-fish img');
  woodenFishImage.classList.add('scale');
  
  // 敲击动画结束后重置状态
  setTimeout(() => {
    woodenFishImage.classList.remove('scale');
    isKnocking.value = false;
  }, 500);
};

/**
 * 显示功德变化文字
 */
const showMeritText = () => {
  // 随机在木鱼上方显示功德文字
  const fishContainer = document.querySelector('.wooden-fish');
  const rect = fishContainer.getBoundingClientRect();
  
  // 随机生成在木鱼上方的位置
  const x = Math.random() * rect.width * 0.8 + rect.width * 0.1;
  const y = rect.height * 0.3;
  
  // 设置功德文字位置
  meritPosition.value = { x, y };
  
  // 设置随机旋转角度
  const rotation = Math.random() * 20 - 10;
  
  meritStyle.value = {
    left: `${meritPosition.value.x}px`,
    top: `${meritPosition.value.y}px`,
    transform: `rotate(${rotation}deg)`
  };
  
  // 显示功德文字
  showMerit.value = true;
  
  // 文字消失动画
  setTimeout(() => {
    showMerit.value = false;
  }, 1000);
};
</script>

<style scoped>
.wooden-fish-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  cursor: pointer; /* 添加手型光标表明整个区域可点击 */
}

.wooden-fish-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.count-display {
  position: absolute;
  font-size: 13px;
  font-weight: bold;
  color: #9a4708;
  top: 5px;
  left: 5px;
  padding: 2px 5px;
  border-radius: 20px;
  z-index: 10;
  pointer-events: none; /* 确保不会拦截点击事件 */
}

.wooden-fish {
  position: relative;
  background: transparent;
  z-index: 1;
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wooden-fish img {
  width: 100%;
  height: 100%;
  user-select: none;
  transition: transform 0.1s ease;
  background: transparent;
  max-width: 100%;
  pointer-events: none; /* 确保不会拦截点击事件 */
}

/* 只对图片应用缩放效果 */
.wooden-fish img.scale {
  transform: scale(0.95);
}

/* 木鱼棍样式 */
.wooden-stick {
  position: relative;
  width: 50%;
  height: max-content;
  transform-origin: 25% 90%;
  margin-bottom: -30px; /* 增加负边距，让重叠更明显 */
  margin-left: 50%; /* 向左偏移，让棍子更靠左 */
  z-index: 20;
  pointer-events: none; /* 确保不会拦截点击事件 */
}

.wooden-stick img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none; /* 确保不会拦截点击事件 */
}

/* 敲击动画 */
.wooden-stick.knocking {
  animation: knock 0.5s ease;
}

@keyframes knock {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: translateX(-5px) translateY(5px) rotate(-15deg);
  }
  50% {
    transform: translateX(-10px) translateY(10px) rotate(-20deg);
  }
  75% {
    transform: translateX(-5px) translateY(5px) rotate(-10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.merit-text {
  position: absolute;
  color: #FFA500;
  font-weight: bold;
  font-size: 1.2rem;
  pointer-events: none;
  animation: floatUp 1s ease-out forwards;
  opacity: 0;
  z-index: 25;
  background: transparent;
}

.instruction {
  margin-top: 20px;
  color: #666;
  font-size: 0.9rem;
  background: transparent;
}

@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateY(0);
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(-50px);
  }
}
</style> 