<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
      <div class="app-modal-header">
        <div class="app-modal-title">{{ title }}</div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <div class="icon-modal-container">
          <!-- 左侧图标类型导航 -->
          <div class="category-sidebar">
            <div class="sidebar-section">
              <div 
                v-for="type in iconTypes" 
                :key="type.value"
                class="category-item"
                :class="{ active: newIcon.type === type.value }"
                @click="newIcon.type = type.value"
              >
                <img :src=" newIcon.type === type.value ? type.activeIcon : type.icon" />
                <span class="category-label">{{ type.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- 右侧表单内容 -->
          <div class="icon-form-content">
            <!-- 组件卡片部分 -->
            <div v-if="newIcon.type === 'card'" class="add-icon-form">
              <!-- 组件卡片网格布局 - 使用循环渲染 -->
              <div class="component-grid">
                <!-- 循环渲染所有组件卡片 -->
                <div 
                  v-for="comp in availableComponents" 
                  :key="comp.type"
                  class="component-card" 
                  @click="selectComponent(comp.type, comp.name)"
                  :class="{'active': newIcon.websiteAddress === comp.type}"
                >
                  <div class="component-preview">
                    <div class="preview-component">
                      <component 
                        :url="comp.url"
                        :is="comp.component" 
                        class="component-inner" 
                        :appId="'preview'" 
                        :headerColor="newIcon.headerColor || '#4285F4'" 
                      />
                    </div>
                  </div>
                  <div class="component-name">{{ comp.name }}</div>
                  <div 
                    class="component-add" 
                    :class="{'selected': newIcon.websiteAddress === comp.type}"
                    @click.stop="addComponentToPage(comp.type, comp.name)"
                  >
                    {{ newIcon.websiteAddress === comp.type ? '已选择' : '添加到页面' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 自定义图标部分 -->
            <div v-if="newIcon.type === 'custom'" class="add-icon-form">
              <div class="form-item">
                <div class="form-label">图标名称</div>
                <Input v-model:value="newIcon.name" placeholder="请输入图标名称" />
              </div>
              
              <div class="form-item">
                <div class="form-label">网址</div>
                <Input v-model:value="newIcon.url" placeholder="请输入网站地址" addon-before="https://" />
                <div class="form-tip">输入不带https://的网址，如：www.example.com</div>
              </div>

              <div class="form-item">
               <div class="form-label">打开方式</div>
               <Radio.Group v-model:value="newIcon.iscanopen" button-style="solid">
                 <Radio.Button :value="1">内嵌打开</Radio.Button>
                 <Radio.Button :value="2">新窗口打开</Radio.Button>
               </Radio.Group>
             </div>

              <div class="form-item">
                <div class="form-label">图标类型</div>
                <Radio.Group v-model:value="iconDisplayType" button-style="solid">
                  <Radio.Button value="text">文字图标</Radio.Button>
                  <Radio.Button value="image">图片图标</Radio.Button>
                  <Radio.Button value="api">接口图标</Radio.Button>
                </Radio.Group>
              </div>
              
              <div class="form-item" v-if="iconDisplayType === 'text'">
                <!-- <Input v-model:value="iconText" placeholder="输入1-6个字符作为图标" maxlength="6" /> -->
                 <Alert message="默认使用名字作为文字图标" type="warning"></Alert>
                <div class="icon-preview-container">
                  <div class="icon-text-preview" :style="{ backgroundColor: newIcon.color || '#f5f5f5' }">
                    <!-- <span :class="{'text-small': iconText && iconText.length > 2, 'text-xsmall': iconText && iconText.length > 4}">
                      {{ iconText || '图' }}
                    </span> -->
                    <div  class="app-icon-div">
                      <span v-text-scale="newIcon.name.slice(0, 6)" class="app-icon-text">{{ newIcon.name.slice(0, 6) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="form-item" v-if="iconDisplayType === 'image'">
                <div class="form-label">上传图标</div>
                <div class="upload-container">
                  <div class="upload-box" @click="triggerFileUpload">
                    <div v-if="!uploadedImage" class="upload-placeholder">
                      <i class="upload-icon"></i>
                      <div>点击上传图标</div>
                    </div>
                    <img v-else :src="concatUrl(uploadedImage)" class="uploaded-preview" />
                  </div>
                  <input
                    type="file"
                    ref="fileInputRef"
                    style="display: none;"
                    accept="image/*"
                    @change="handleFileUpload"
                  />
                </div>
              </div>

              <div class="form-item" v-if="iconDisplayType === 'api'">
                <div class="form-label">接口图标</div>
                <Alert message="将根据网址自动获取网站图标" type="info" style="margin-bottom: 10px;"></Alert>
                <div class="api-icon-container">
                  <div class="api-icon-preview-box">
                    <div v-if="apiIconLoading" class="api-loading">
                      <Spin size="small" />
                      <div>获取图标中...</div>
                    </div>
                    <div v-else-if="apiIconError" class="api-error">
                      <div class="error-icon">⚠️</div>
                      <div class="error-text">{{ apiIconError }}</div>
                      <Button size="small" @click="fetchApiIcon" style="margin-top: 8px;">重新获取</Button>
                    </div>
                    <div v-else-if="apiIconUrl" class="api-success">
                      <img :src="apiIconUrl" class="api-icon-preview" @error="handleApiIconError" style="width: 100%;height: 100%;" />
                    </div>
                    <div v-else class="api-placeholder">
                      <div class="placeholder-icon">🌐</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-actions">
                <Button @click="handleClose">取消</Button>
                <Button type="primary" @click="addCustomIcon">添加到页面</Button>
              </div>
            </div>

            <!-- 应用列表部分 -->
            <div v-if="newIcon.type === 'app'" class="app-list-full">
              <!-- 搜索框 -->
              <div class="search-container">
                <Input
                  v-model:value="searchKeyword"
                  placeholder="搜索应用名称..."
                  class="search-input"
                  @pressEnter="handleSearch"
                  :loading="isSearching"
                  >
                  <template #suffix>
                    <img :src="search" @click="handleSearch" />
                  </template>
                </Input>
              </div>
              <div class="app-list-container" @scroll="handleScroll" ref="appListContainerRef">
                <Spin v-if="loading && appList.length === 0" class="loading-spinner"/>
                <Empty v-if="appList.length === 0 && !loading" description="暂无应用数据" />
                <div class="app-list">
                  <div 
                    v-for="app in appList" 
                    :key="app.id" 
                    class="app-card"
                  >
                    <div class="app-card-header">
                      <div class="app-icon-wrapper">
                        <img v-if="app.logo" :src="concatUrl(app.logo)" alt="app-icon" class="app-icon-img" />
                        <div v-else class="app-icon-div">
                          <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                        </div>
                      <div v-if="app.iscanopen == 2" class="newWindow">
                        <svg t="1748943554105" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5226" width="20" height="20"><path d="M914.285714 914.285714h-804.571428v-804.571428h248.685714V0H109.714286C51.2 0 0 51.2 0 109.714286v797.257143c0 65.828571 51.2 117.028571 109.714286 117.028571h797.257143c65.828571 0 109.714286-51.2 109.714285-109.714286V658.285714h-109.714285v256h7.314285zM629.028571 0v109.714286h204.8L277.942857 665.6l80.457143 80.457143 555.885714-555.885714v204.8H1024V0H629.028571z" fill="#999999" p-id="5227"></path></svg>
                      </div>
                      </div>
                      <div class="app-title-area">
                        <div class="app-name-text" :title="app.name">{{ app.name.length > 7 ? app.name.slice(0, 7) + '...' : app.name }}</div>
                        <div class="app-category">{{ app.classification || '未分类' }}</div>
                        <!-- <a class="app-direct-link" :href="'http://'+app.websiteAddress" target="_blank">
                          <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_1060_2068)">
                          <path d="M9.0957 5.29883C8.94043 5.29883 8.81348 5.42578 8.81348 5.58105V7.95019C8.81348 8.4209 8.43066 8.80371 7.95996 8.80371H2.05371C1.58301 8.80371 1.2002 8.4209 1.2002 7.95019V2.04395C1.2002 1.57324 1.58301 1.19043 2.05371 1.19043H5.00684C5.16211 1.19043 5.28906 1.06348 5.28906 0.908203C5.28906 0.75293 5.16211 0.625977 5.00684 0.625977H2.05371C1.27246 0.626953 0.636719 1.2627 0.635742 2.04395V7.95117C0.636719 8.73242 1.27246 9.36816 2.05371 9.36914H7.96094C8.74219 9.36816 9.37793 8.73242 9.37891 7.95117V5.58105C9.37891 5.42578 9.25195 5.29883 9.0957 5.29883Z" fill="#7DB1FF"/>
                          <path d="M8.49401 1.98817C6.74108 2.07704 5.41296 2.60926 4.54577 3.56922C3.3153 4.93153 3.48132 6.64637 3.48913 6.71864C3.50378 6.86219 3.62487 6.97059 3.76843 6.97059C3.77819 6.97059 3.78796 6.97059 3.7987 6.96961C3.953 6.95301 4.0653 6.81434 4.04968 6.66004C4.04968 6.65614 4.01257 6.26844 4.11022 5.73524C4.19812 5.25184 4.41882 4.54872 4.9696 3.94227C5.7323 3.10145 6.92761 2.63368 8.52136 2.55164L7.71374 3.62782C7.64929 3.71375 7.63855 3.82704 7.68737 3.92372C7.73523 4.01942 7.8319 4.07997 7.93933 4.07997H7.9403C8.02917 4.07997 8.11315 4.03797 8.16589 3.96668L9.32019 2.42664L9.32116 2.42469C9.32409 2.41981 9.328 2.4159 9.33093 2.41102C9.3319 2.41004 9.33288 2.40809 9.33386 2.40711L9.33483 2.40614C9.33679 2.40223 9.33874 2.3993 9.34167 2.39539L9.34265 2.39442C9.34362 2.39344 9.34362 2.39247 9.3446 2.39051C9.34753 2.38563 9.35046 2.37977 9.35339 2.37391L9.35437 2.37196L9.36022 2.35731L9.3612 2.35438L9.36706 2.3368V2.33485C9.36901 2.32997 9.36999 2.32411 9.37097 2.31922L9.37194 2.31532C9.37292 2.30946 9.3739 2.3036 9.37487 2.29872V2.29579C9.37585 2.2909 9.37585 2.287 9.37683 2.28211C9.37683 2.28016 9.37683 2.27723 9.3778 2.27528V2.26356V2.25379V2.24207C9.3778 2.23914 9.3778 2.23622 9.37683 2.23426C9.37683 2.23133 9.37585 2.2284 9.37585 2.22547C9.37585 2.22157 9.37487 2.21864 9.3739 2.21473C9.37292 2.21082 9.37292 2.20692 9.37194 2.20301C9.37097 2.20008 9.37097 2.19813 9.36999 2.1952C9.36901 2.19227 9.36804 2.18836 9.36804 2.18543V2.18348C9.36804 2.18153 9.36706 2.18055 9.36706 2.1786C9.36511 2.17372 9.36413 2.16883 9.36218 2.16395C9.3612 2.162 9.3612 2.16102 9.36022 2.15907V2.15809C9.35827 2.15418 9.3573 2.15028 9.35534 2.14637L9.35339 2.14247C9.35144 2.13758 9.34851 2.13172 9.34558 2.12684L9.3446 2.12391C9.34167 2.11903 9.33874 2.11414 9.33581 2.10829L9.33483 2.10731C9.328 2.09657 9.32019 2.08582 9.3114 2.07606L8.15515 0.724496C8.05358 0.606332 7.87487 0.59266 7.75671 0.693246C7.63855 0.794809 7.62487 0.97352 7.72546 1.09168L8.49401 1.98817Z" fill="#7DB1FF"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_1060_2068">
                          <rect width="10" height="10" fill="white"/>
                          </clipPath>
                          </defs>
                          </svg>
                          链接直达
                        </a> -->
                      </div>
                    </div>
                    <div class="app-card-content">
                      <div class="app-description-text">{{ app.descs }}</div>
                    </div>
                    <div class="app-card-footer">
                      <div @click="newOpenUrl(app.websiteAddress)">
                        <img :src="newlink" />
                      </div>
                      <div>
                        <svg v-if="!app.isCollect" @click.stop="addToCollect(app)" width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.67368 1.27441C8.61668 1.27441 7.63668 1.80641 6.98568 2.68141C6.34168 1.80641 5.35468 1.27441 4.29768 1.27441C2.39368 1.27441 0.84668 2.96841 0.84668 5.04741C0.84668 6.28641 1.39968 7.16141 1.84768 7.86141C3.14268 9.89841 6.40468 12.4254 6.54468 12.5304C6.67768 12.6354 6.83168 12.6844 6.98568 12.6844C7.13968 12.6844 7.29368 12.6354 7.42668 12.5304C7.56668 12.4254 10.8217 9.89141 12.1237 7.86141C12.5717 7.16141 13.1247 6.28641 13.1247 5.04741C13.1247 2.96841 11.5777 1.27441 9.67368 1.27441Z" fill="#C8C8C8"/>
                        </svg>
                        <svg v-else @click.stop="cancelCollect(app)" width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.67368 1.27441C8.61668 1.27441 7.63668 1.80641 6.98568 2.68141C6.34168 1.80641 5.35468 1.27441 4.29768 1.27441C2.39368 1.27441 0.84668 2.96841 0.84668 5.04741C0.84668 6.28641 1.39968 7.16141 1.84768 7.86141C3.14268 9.89841 6.40468 12.4254 6.54468 12.5304C6.67768 12.6354 6.83168 12.6844 6.98568 12.6844C7.13968 12.6844 7.29368 12.6354 7.42668 12.5304C7.56668 12.4254 10.8217 9.89141 12.1237 7.86141C12.5717 7.16141 13.1247 6.28641 13.1247 5.04741C13.1247 2.96841 11.5777 1.27441 9.67368 1.27441Z" fill="#FF452D"/>
                        </svg>
                      </div>
                      <button type="primary" @click.stop="selectApp(app)">添加</button>
                    </div>
                  </div>
                </div>
                <div v-if="loading && appList.length > 0" class="loading-more">
                  <Spin size="small" /> <span>加载更多...</span>
                </div>
                <div v-if="!hasMore && appList.length > 0" class="no-more">已加载全部应用</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, shallowRef, defineAsyncComponent, markRaw, onMounted } from 'vue';
import { Input, Radio, Button, message, Modal, Select, Spin, Empty, Pagination, Alert } from 'ant-design-vue';
import { getAllAppList, uploadFiles, queryAppName } from '@/api/navbar';
import { useUrlStore } from '@/stores/url';
import { useSettingStore } from '@/stores/setting.js';
import  closeSvg  from '@/assets/modal/close.svg'
import newlink from '@/assets/icons/newlink.svg'
import search from '@/assets/icons/search.svg'

import appIcon from '@/assets/modal/appIcon.svg'
import appIconActive from '@/assets/modal/appIconActive.svg'
import cardIcon from '@/assets/modal/cardIcon.svg'
import cardIconActive from '@/assets/modal/cardIconActive.svg'
import customIcon from '@/assets/modal/customIcon.svg'
import customIconActive from '@/assets/modal/customIconActive.svg'

// 异步导入需要的组件
const VideoCardAdapter = defineAsyncComponent(() => import('../video/VideoCardAdapter.vue'));
const Weather = defineAsyncComponent(() => import('../weather/index.vue'));
const AnimatedClock = defineAsyncComponent(() => import('../time/AnimatedClock.vue'));
const WoodenFish = defineAsyncComponent(() => import('../woodenFish/WoodenFish.vue'));
const RelaxCard = defineAsyncComponent(() => import('../relax/RelaxCard.vue'));
const IframeCard = defineAsyncComponent(() => import('../iframe/IframeCard.vue'));
const ImageCard = defineAsyncComponent(() => import('../iframe/ImageCard.vue'));
const LinkCard = defineAsyncComponent(() => import('../iframe/LinkCard.vue'));



const urlStore = useUrlStore();
const settingStore = useSettingStore();
// 新增：定义所有可用组件列表用于循环渲染
const availableComponents = ref([
  {
    type: 'LinkCard',
    name: '迷你浏览器',
    component: LinkCard,
    recommendedSize: '6x4'
  },
  {
    type: 'VideoCardAdapter',
    name: '摸鱼视频',
    component: VideoCardAdapter,
    recommendedSize: '3x2'
  },
  {
    type: 'Weather',
    name: '天气',
    component: Weather,
    recommendedSize: '3x2'
  },
  {
    type: 'AnimatedClock',
    name: '时钟',
    component: AnimatedClock,
    recommendedSize: '2x2'
  },
  {
    type: 'WoodenFish',
    name: '敲木鱼',
    component: WoodenFish,
    recommendedSize: '2x2'
  },
  {
    type: 'RelaxCard',
    name: '摸鱼日历',
    component: RelaxCard,
    recommendedSize: '4x3'
  },
  // {
  //   type: 'IframeCard',
  //   name: '内嵌',
  //   component: IframeCard,
  //   recommendedSize: '3x2'
  // },
  {
    type: 'ImageCard',
    name: '摸鱼日报',
    component: ImageCard,
    recommendedSize: '3x2',
    url:'https://www.yviii.com/moyu/moyu.php'
  }
]);


// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  // 如果path为null那么就随机a-z加上.png
  return urlStore.concatUrl(path)
}

// 根据组件名称字符串获取对应的组件引用
function getComponentByName(componentName) {
  // 从组件列表中找到对应的组件
  const foundComponent = availableComponents.value.find(item => item.type === componentName);
  return foundComponent ? foundComponent.component : null;
}

// 检查组件是否有效
const isComponentValid = (componentName) => {
  return !!componentName;
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加图标'
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  editingIcon: {
    type: Object,
    default: () => null
  },
  currentCategory: {
    type: String,
    default: 'all'
  }
});

const emit = defineEmits(['close', 'update:visible', 'add-icon']);

// 默认新图标数据
const newIcon = ref({
  name: '',
  type: 'app',
  url: '',
  iconUrl: '',
  color: '#f5f5f5',
  category: 'app',
  size: '1x1',
  headerColor: '#4285F4',
  websiteAddress: '',
  description: '',
  iscanopen: 1, // 默认内嵌打开
});

// 颜色选项
const predefinedColors = ref(['#f5f5f5', '#2d0301', '#00172d', '#000050', '#ffcb0e', '#50416a', '#4285F4', '#34A853', '#FBBC05', '#EA4335', '#5F6368', '#4285F4', '#34A853', '#FBBC05', '#EA4335']);
const predefinedHeaderColors = ref(['#FB7299', '#00BFFF', '#1E80FF', '#34A853', '#FBBC05', '#EA4335', '#4285F4', '#5F6368', '#FF4D4D', '#E4EB2F']);

// 分类选项
const categories = ref([
  { value: 'app', label: '应用', icon: '📱' },
  { value: 'widget', label: '小组件', icon: '🧩' }
]);

// 新增：图标类型选项
const iconTypes = ref([
  { value: 'app', label: '应用图标', icon: appIcon, activeIcon: appIconActive },
  { value: 'card', label: '组件卡片', icon: cardIcon, activeIcon: cardIconActive },
  { value: 'custom', label: '自定义图标', icon: customIcon, activeIcon: customIconActive }
]);

const isFullscreen = ref(false);
const isMinimized = ref(false);

// 应用列表相关状态
const appList = ref([]);
const loading = ref(false);
const total = ref(0);
const hasMore = ref(true);
const page = ref(1);
const pageSize = ref(30);
const appListContainerRef = ref(null);

// 搜索相关状态
const searchKeyword = ref('');
const isSearching = ref(false);
const searchResults = ref([]);
const hasSearched = ref(false);

// 新增自定义图标相关变量
const iconDisplayType = ref('text'); // 'text', 'image' 或 'api'
const iconText = ref(''); // 文字图标内容
const uploadedImage = ref(null); // 上传的图片
const fileInputRef = ref(null); // 文件输入引用
const apiIconUrl = ref(null); // API获取的图标URL
const apiIconLoading = ref(false); // API图标加载状态
const apiIconError = ref(null); // API图标错误信息

const addToCollect = (app) => {
  const token = localStorage.getItem('token')  
  if(token) {
    AddUseCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('添加成功')
      }
    })
  }
  app.isCollect = true
  emit('add-to-dock', app)

  // emit('close')
}

const cancelCollect = (app) => {
  const token = localStorage.getItem('token')  
  if(token) {
    deleteCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('删除成功')
      }
    })
  }
  app.isCollect = false
  emit('remove-from-dock', app)
}

// 触发文件上传
const triggerFileUpload = () => {
  fileInputRef.value.click();
};

// 获取API图标
const fetchApiIcon = async () => {
  if (!newIcon.value.url.trim()) {
    apiIconError.value = '请先输入网址';
    return;
  }

  apiIconLoading.value = true;
  apiIconError.value = null;
  apiIconUrl.value = null;

  try {
    // 清理URL，移除协议前缀
    let cleanUrl = newIcon.value.url.replace(/^https?:\/\//, '');

    // 构建favicon.im API链接
    const faviconUrl = `https://favicon.im/zh/${cleanUrl}?larger=true`;

    // 测试图标是否可以加载
    const img = new Image();
    img.onload = () => {
      apiIconUrl.value = faviconUrl;
      apiIconLoading.value = false;
    };
    img.onerror = () => {
      apiIconError.value = '无法获取该网站的图标';
      apiIconLoading.value = false;
    };
    img.src = faviconUrl;

  } catch (error) {
    console.error('获取API图标失败:', error);
    apiIconError.value = '获取图标失败，请重试';
    apiIconLoading.value = false;
  }
};

const newOpenUrl = (url) => {
  window.open('https://' + url, '_blank')
}

// 处理API图标加载错误
const handleApiIconError = () => {
  apiIconError.value = '图标加载失败';
  apiIconUrl.value = null;
};

// 处理文件上传
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请上传图片文件');
    return;
  }
  
  // 验证文件大小（限制为2MB）
  if (file.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB');
    return;
  }

  // 显示上传中状态
  message.loading({ content: '图标上传中...', key: 'uploadIcon' });
  
  const formData = new FormData();
  formData.append('file', file);
  
  // 调用上传接口
  uploadFiles(formData).then(res => {
    
    if (res.status === 200) {
      // 假设服务器返回的数据中包含文件URL
      const fileUrl = res.url;
      // 设置图标URL，这里使用服务器返回的URL
      uploadedImage.value = fileUrl;
      newIcon.value.iconUrl = fileUrl; // 保存图标URL（不带域名的相对路径）
      
      message.success({ content: '图标上传成功!', key: 'uploadIcon' });
    } else {
      throw new Error('上传响应格式不正确');
    }
  }).catch(error => {
    console.error('图标上传失败:', error);
    message.error({ content: '图标上传失败，请重试', key: 'uploadIcon' });
    
    // 清空文件输入，允许用户重新选择
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }
  });
};

// 添加自定义图标
const addCustomIcon = () => {
  // 表单验证
  if (!newIcon.value.name.trim()) {
    message.error('请输入图标名称');
    return;
  }
  
  if (!newIcon.value.url.trim()) {
    message.error('请输入网址');
    return;
  }
  
  // 检查图标
  // if (iconDisplayType.value === 'text' && !iconText.value.trim()) {
  //   message.error('请输入文字作为图标');
  //   return;
  // }
  
  if (iconDisplayType.value === 'image' && !uploadedImage.value) {
    message.error('请上传图标图片');
    return;
  }

  if (iconDisplayType.value === 'api' && !apiIconUrl.value) {
    message.error('请等待图标获取完成或重新获取');
    return;
  }
  
  
  
  // 生成唯一ID - 与selectApp保持一致
  const uniqueId = `app-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // 确定图标内容
  let iconContent = '';
  if (iconDisplayType.value === 'text') {
    iconContent = iconText.value;
  } else if (iconDisplayType.value === 'image') {
    iconContent = uploadedImage.value;
  } else if (iconDisplayType.value === 'api') {
    iconContent = apiIconUrl.value;
  }
  
  // 构建一个符合首页展示格式的应用对象 - 与selectApp中的homeApp结构保持一致
  const homeApp = {
    id: uniqueId,
    name: newIcon.value.name,
    type: 'app',  // 统一使用app类型
    icon: iconContent,  // 保持与selectApp一致，使用icon作为图标字段
    url: newIcon.value.url.startsWith('http') ? newIcon.value.url : `http://${newIcon.value.url}`,
    description: newIcon.value.description || '自定义图标',
    color: newIcon.value.color || '#f5f5f5',
    size: {
      w: 1,
      h: 1
    },
    iscanopen: newIcon.value.iscanopen,
    // 使用当前分类，如果没有传入则使用默认分类
    category: props.currentCategory || 'app',
    custom: true
  };
  
  // 根据图标类型设置图标
  if (iconDisplayType.value === 'text') {
    homeApp.isTextIcon = true;
  } else if (iconDisplayType.value === 'image') {
    // 如果是图片图标，使用上传后的URL或直接使用iconUrl
    homeApp.icon = newIcon.value.iconUrl || uploadedImage.value;
  } else if (iconDisplayType.value === 'api') {
    // 如果是API图标，直接使用API获取的URL
    homeApp.icon = apiIconUrl.value;
  }
  
  
  
  // 发出添加图标的事件
  emit('add-icon', homeApp);
  
  // 关闭弹窗
  // handleClose();
};

// 重置表单中添加自定义相关的内容
const resetForm = () => {
  newIcon.value = {
    name: '',
    type: 'app',
    url: '',
    iconUrl: '',
    color: '#f5f5f5',
    category: 'app',
    size: '1x1',
    headerColor: '#4285F4',
    websiteAddress: '',
    description: ''
  };
  
  // 重置自定义图标相关状态
  iconDisplayType.value = 'text';
  iconText.value = '';
  uploadedImage.value = null;
  apiIconUrl.value = null;
  apiIconLoading.value = false;
  apiIconError.value = null;
};

// 添加应用到首页
const selectApp = (app) => {
  // 构建一个符合首页展示格式的应用对象
  const homeApp = {
    id: app.id, // 始终使用新生成的唯一ID，不再使用app.id
    name: app.name,
    type: 'app',
    icon: app.logo, // Home.vue中使用icon作为图标
    url: app.websiteAddress || '',
    description: app.descs,
    color: app.color || '#f5f5f5',
    size: {
      w: app.w || 1,
      h: app.h || 1
    },
    iscanopen:app.iscanopen,
    // 使用当前分类，如果没有传入则使用app的分类或默认分类
    category: props.currentCategory || app.classification || 'app',
    iscanopen:app.iscanopen
  };
  
  
  
  // 发出添加图标的事件
  emit('add-icon', homeApp);
  // 关闭弹窗
  // handleClose();
}

// 分页变化
const handlePageChange = () => {
  getAllAppFunc();
}

// 监听visible变化，重置表单和状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 打开时重置表单
    resetForm();
    
    // 如果是编辑模式且有传入图标数据，则使用该数据
    if (props.isEditMode && props.editingIcon) {
      // 编辑模式下使用现有图标数据填充表单
      Object.assign(newIcon.value, props.editingIcon);
    }
  } else {
    // 关闭时确保退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
    }
    // 重置最小化状态
    if (isMinimized.value) {
      isMinimized.value = false;
    }
  }
});

// 监听类型变化
watch(() => newIcon.value.type, (newType) => {
  // 重置为默认大小
  newIcon.value.size = '1x1';
  if (newType === 'app') {
    getAllAppFunc();
  }
});

// 监听URL变化，当选择API图标类型时自动获取图标
watch(() => newIcon.value.url, (newUrl) => {
  if (iconDisplayType.value === 'api' && newUrl && newUrl.trim()) {
    // 延迟执行，避免频繁请求
    setTimeout(() => {
      if (newIcon.value.url === newUrl) {
        fetchApiIcon();
      }
    }, 500);
  }
});

// 监听图标显示类型变化
watch(() => iconDisplayType.value, (newType) => {
  if (newType === 'api' && newIcon.value.url && newIcon.value.url.trim()) {
    fetchApiIcon();
  }
});

// 监听搜索关键词变化，当输入框为空时自动重新加载所有应用
watch(() => searchKeyword.value, (newKeyword) => {
  // 只有在之前有搜索过且现在输入框为空时才自动重新加载
  if (newKeyword.trim() === '' && hasSearched.value) {
    // 重置搜索状态
    hasSearched.value = false;
    // 重置分页状态
    page.value = 1;
    hasMore.value = true;
    // 重新加载所有应用
    getAllAppFunc();
  }
});

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    handleClose();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    handleClose();
  }
};

// 最小化弹窗
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value;
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('icon-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
};

// 组件相关辅助函数
function getRecommendedSize(componentType) {
  const sizeMap = {
    'VideoCardAdapter': '3x2',
    'Weather': '3x2',
    'AnimatedClock': '2x2',
    'WoodenFish': '2x2',
    'RelaxCard': '3x2',
    'IframeCard': '6x4',
    'ImageCard': '3x2',
    'LinkCard': '2x2'
  };
  return sizeMap[componentType] || '2x2';
}

function getComponentIcon(componentType) {
  const iconMap = {
    'VideoCardAdapter': '🎬',
    'Weather': '🌤️',
    'AnimatedClock': '🕰️',
    'WoodenFish': '🪘',
    'RelaxCard': '⏳',
    'IframeCard': '🌐',
    'ImageCard': '🖼️',
    'LinkCard': '🔗'
  };
  return iconMap[componentType] || '🧩';
}

function getComponentName(componentType) {
  const nameMap = {
    'VideoCardAdapter': '视频卡片',
    'Weather': '天气卡片',
    'AnimatedClock': '时钟卡片',
    'WoodenFish': '木鱼卡片',
    'RelaxCard': '摸鱼卡片',
    'IframeCard': '迷你浏览器',
    'ImageCard': '图片卡片',
    'LinkCard': '链接卡片'
  };
  return nameMap[componentType] || '未知组件';
}

// 处理组件类型变更
function handleComponentChange(value) {
  // 根据组件类型自动设置推荐大小
  const recommendedSize = getRecommendedSize(value);
  newIcon.value.size = recommendedSize;
}

// 选择组件的方法
function selectComponent(componentType, componentName) {
  newIcon.value.websiteAddress = componentType;
  if (!newIcon.value.name) {
    newIcon.value.name = componentName;
  }
  handleComponentChange(componentType);
}

// 处理添加图标
const handleAddIcon = () => {
  // 表单验证
  if (!newIcon.value.name.trim()) {
    message.error('请输入图标名称');
    return;
  }

  if (newIcon.value.type === 'app' && !newIcon.value.iconUrl.trim()) {
    message.error('请输入图标URL');
    return;
  }
  
  if (newIcon.value.type === 'card' && !newIcon.value.websiteAddress) {
    message.error('请选择组件名称');
    return;
  }
  
  // 更新分类
  // 如果是组件卡片类型，则分类设为小组件
  if (newIcon.value.type === 'card') {
    newIcon.value.category = 'widget';
  }
  
  // 如果是组件类型，自动设置为推荐大小
  if (newIcon.value.type === 'card' && newIcon.value.websiteAddress) {
    newIcon.value.size = getRecommendedSize(newIcon.value.websiteAddress);
  }
  
  // 生成唯一ID
  const uniqueId = `icon-${newIcon.value.type}-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // 发送事件通知父组件添加图标，添加唯一ID
  emit('add-icon', { id: uniqueId, ...newIcon.value });
  
  // 关闭弹窗
  handleClose();
};

// 新增：直接添加组件到页面的方法
function addComponentToPage(componentType, componentName) {
  // 设置组件类型和名称
  newIcon.value.websiteAddress = componentType;
  if (!newIcon.value.name) {
    newIcon.value.name = componentName;
  }
  
  // 获取推荐大小
  const foundComponent = availableComponents.value.find(item => item.type === componentType);
  const recommendedSize = foundComponent?.recommendedSize || '2x2';
  
  // 生成唯一ID - 确保每个组件都有唯一ID
  const uniqueId = `card-${componentType}-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // 构建要添加的组件对象
  const componentToAdd = {
    ...newIcon.value,
    id: uniqueId, // 添加唯一ID
    type: 'card', // 确保类型是card
    category: props.currentCategory, // 设置分类为小组件
    size: recommendedSize, // 使用推荐大小
    headerColor: newIcon.value.headerColor || '#4285F4' // 使用已选颜色或默认颜色
  };
  
  // 发送添加事件
  emit('add-icon', componentToAdd);
  
  // 关闭弹窗
  handleClose();
}

const getAllAppFunc = async () => {
  if (!hasMore.value || loading.value) return;

  loading.value = true;
  try {
    const res = await getAllAppList(page.value, pageSize.value);

    // 当是第一页时替换数据，否则追加数据
    if (page.value === 1) {
      appList.value = res.data || [];
      // 重置搜索状态
      hasSearched.value = false;
    } else {
      appList.value = [...appList.value, ...(res.data || [])];
    }

    total.value = res.total || 0;

    // 判断是否还有更多数据
    hasMore.value = res.data && res.data.length === pageSize.value;

    // 增加页码，为下次加载准备
    if (res.data && res.data.length > 0) {
      page.value++;
    }


  } catch (error) {
    message.error('获取应用列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
}

// 搜索应用
const handleSearch = async () => {
  const keyword = searchKeyword.value.trim();
  if (!keyword) {
    message.warning('请输入搜索关键词');
    return;
  }

  isSearching.value = true;
  try {
    const res = await queryAppName({ name: keyword });

    if (res && res.data) {
      appList.value = res.data;
      hasSearched.value = true;
      // 搜索时禁用滚动加载更多
      hasMore.value = false;

      if (res.data.length === 0) {
        message.info('未找到相关应用');
      }
    } else {
      appList.value = [];
      hasSearched.value = true;
      hasMore.value = false;
      message.info('未找到相关应用');
    }
  } catch (error) {
    console.error('搜索失败:', error);
    message.error('搜索失败，请重试');
  } finally {
    isSearching.value = false;
  }
}

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = '';
  hasSearched.value = false;
  // 重置分页状态
  page.value = 1;
  hasMore.value = true;
  // 重新加载所有应用
  getAllAppFunc();
}



const handleScroll = (event) => {
  const container = event.target;
  if (container.scrollTop + container.clientHeight >= container.scrollHeight - 10) {
    getAllAppFunc();
  }
}

onMounted(() => {
  // 当类型为app时，加载应用列表
  if (newIcon.value.type === 'app') {
    getAllAppFunc();
  }
})

</script>

<style lang="scss" scoped>
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1190px;
  position: relative;
  z-index: 501;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 9999; /* 更高的z-index确保覆盖所有元素 */
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 9998; /* 提高overlay的z-index */
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag; /* 允许拖动窗口 */
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none; /* 防止选中文本 */
}

.app-modal-spacer {
  width: 60px; /* 与控制按钮区域保持对称 */
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag; /* 控制按钮区域不可拖动窗口 */
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  border: none;
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.minimize-btn {
  background-color: #ffbd2e;
}

.minimize-btn:hover {
  background-color: #ffbd2e;
  filter: brightness(0.9);
}

.maximize-btn {
  background-color: #28c940;
}

.maximize-btn:hover {
  background-color: #28c940;
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
}

.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 图标模态框左右布局容器 */
.icon-modal-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

/* 左侧分类导航样式 */
.category-sidebar {
  width: 164px;
  background-color: #ffffff;
  padding: 15px 0;
  overflow-y: auto;
  border-right: 1px solid #e0e0e0;
  flex-shrink: 0;
}

/* 侧边栏分区样式 */
.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  padding: 0 15px 5px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  width: 134px;
  margin: 0 auto;
  margin-bottom: 10px;
}

.category-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.category-item.active {
  background-color: #71C6FF;
  color: white;
  border-radius: 8px;
  .category-label{
    color: white;
  }
}

.category-icon {
  margin-right: 10px;
  font-size: 16px;
}

.category-label {
  font-size: 12px;
  color: #333;
}

/* 右侧表单内容样式 */
.icon-form-content {
  flex: 1;
  padding: 16px;
  position: relative;
  overflow-y: auto;
  height: 100%;
  background-color: #ffffff;
}

/* 应用列表满屏 */
.app-list-full {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* 表单样式 */
.add-icon-form {
  width: 100%;
  height: auto;
  overflow-y: visible;
  display: flex;
  flex-direction: column;
  text-align: left;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
}

.form-tip {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.danger-tip {
  color: #ff4d4f;
}

/* 颜色选择器样式 */
.color-picker-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #4285F4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.3);
  transform: scale(1.1);
}

/* 分类单选按钮样式 */
.category-radio-content,
.type-radio-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.category-icon,
.type-icon {
  margin-right: 4px;
  font-size: 16px;
}

// :deep(.ant-radio-button-wrapper) {
//   margin-bottom: 8px;
//   margin-right: 8px;
// }

:deep(.ant-radio-group-solid) {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.ant-radio-button-wrapper-checked) {
  background-color: #4285F4;
  border-color: #4285F4;
  color: white;
}

/* 表单操作按钮样式 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eaeaea;
}

/* 应用预览样式 */
.app-preview {
  display: flex;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-top: 10px;
  align-items: center;
}

.app-icon-preview {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.app-icon-preview img {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.placeholder-icon {
  font-size: 20px;
}

.app-info-preview {
  flex: 1;
}

.app-name-preview {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.app-desc-preview {
  font-size: 12px;
  color: #666;
}

/* 组件预览样式 */
.component-preview {
  width: 100%;
  position: relative;
  padding-bottom: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

.preview-component {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  pointer-events: none;
}

.preview-component > * {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  transform: scale(0.9);
}

.component-name {
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: black;
}

.component-add {
  padding: 8px;
  text-align: center;
  font-size: 12px;
  color: #1677ff;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
}

.selected {
  background-color: #e6f7ff;
  font-weight: bold;
}

.app-list-container {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.app-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, 225px);
  gap: 10px;
  padding: 10px;
  justify-content: center; /* 让网格在容器中居中 */
}

.app-card {
  width: 100%;
  max-height: 190px;
  background-color: #F3F3F3;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.app-card-header {
  display: flex;
  padding: 16px;
  position: relative;
}

.app-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  // overflow: hidden;
  margin-right: 12px;
  background-color: #f5f5f5;
  flex-shrink: 0;
  position: relative;
  img{
    border-radius: 8px;
  }
}

.app-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.app-title-area {
  flex: 1;
  text-align: left;
}

.app-name-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.app-category {
  font-size: 12px;
  color: #888;
}

.app-direct-link {
  /* position: absolute; */
  top: 12px;
  right: 12px;
  font-size: 12px;
  color: #7DB1FF;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.app-card-content {
  padding: 0 16px;
  flex: 1;
  // height: 50px;
}

.app-description-text {
  font-size: 10px;
  color: #939393;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  text-align: left;
}

.app-card-footer {
  padding: 12px 16px;
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
  gap: 10px;
  div{
    width: 50px;
    height: 23px;
    background: #E9E9E9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  button{
    background: #85c4fa;
    width: 50px;
    height: 23px;
    border-radius: 4px;
    border: none;
    color: #fff;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
  }
  button:hover{
    background: #0088ff;
  }
}

.app-add-btn {
  min-width: 120px;
  padding: 8px 16px;
  background-color: #1677ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.app-add-btn:hover {
  background-color: #4096ff;
}

.link-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231677ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='7' y1='17' x2='17' y2='7'%3E%3C/line%3E%3Cpolyline points='7 7 17 7 17 17'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: cover;
  margin-right: 4px;
}

.app-list-header {
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #ffffff;
}

.app-list-title {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.loading-more, .no-more {
  text-align: center;
  padding: 12px;
  color: #999;
  font-size: 13px;
  border-top: 1px dashed #eaeaea;
  margin-top: 10px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 组件卡片布局 */
.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin: 20px 0;
}

.component-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
}

/* 确保卡片是正方形 */
.component-preview {
  width: 100%;
  position: relative;
  padding-bottom: 100%; /* 使容器保持1:1比例（正方形） */
  background-color: #f5f5f5;
  overflow: hidden;
}

.preview-component {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.component-inner {
  width: 90%;
  height: 90%;
  transform: scale(0.85);
  transform-origin: center;
  pointer-events: none; /* 防止卡片内部组件接收点击事件 */
}

.component-name {
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.component-add {
  padding: 8px;
  text-align: center;
  font-size: 12px;
  color: #1677ff;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
}

.component-card.active {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 特定组件的样式调整 */
:deep(.weather-card),
:deep(.clock-wrapper),
:deep(.wooden-fish-container),
:deep(.relax-card),
:deep(.iframe-card),
:deep(.image-card),
:deep(.link-card),
:deep(.video-card) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  transform: scale(0.85) !important;
  transform-origin: center !important;
}

/* 自定义图标上传样式 */
.upload-container {
  margin-top: 10px;
}

.upload-box {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.upload-box:hover {
  border-color: #1677ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.upload-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23888' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
}

.uploaded-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-preview-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.icon-text-preview {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  overflow: hidden;
}

.icon-text-preview .text-small {
  font-size: 18px;
}

.icon-text-preview .text-xsmall {
  font-size: 14px;
}

/* API图标相关样式 */
.api-icon-container {
  margin-top: 10px;
}

.api-icon-preview-box {
  width: 100px;
  height: 100px;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  overflow: hidden;
}

.api-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #1677ff;
  font-size: 12px;
  gap: 8px;
}

.api-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 8px;
}

.error-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.error-text {
  margin-bottom: 4px;
  line-height: 1.2;
}

.api-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.api-icon-preview {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 4px;
}

.success-text {
  font-size: 10px;
  color: #52c41a;
  margin-top: 4px;
  text-align: center;
}

.api-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.placeholder-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  gap: 8px;
  padding: 12px;
  //background-color: #ffffff;
  align-items: center;
}

.search-input {
  flex: 1;
  border-radius: 70px;
}

.search-btn {
  min-width: 60px;
}

.clear-btn {
  min-width: 60px;
}
</style>
