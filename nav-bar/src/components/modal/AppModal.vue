<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
      <div class="app-modal-header">
        <div class="app-modal-title">{{ title }}</div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn maximize-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏'" style="margin-right: 3px;">
            <img :src="full" alt="fullscreen" />
          </button>
          <button class="control-btn minimize-btn" @click="openInNewWindow" title="在新窗口打开">
            <img :src="newWindow" alt="newwindow" />
          </button>
          <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
            <img :src="closeSvg" style="width: 16px;height: 16px;padding: 1px;" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <iframe
          v-if="url"
          ref="iframeRef"
          :src="url"
          frameborder="0"
          tabindex="0"
          class="app-iframe"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
        ></iframe>
        <div v-else class="empty-content">
          <i class="icon icon-warning"></i>
          <p>无效的URL</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useSettingStore } from '@/stores/setting.js';
import  closeSvg  from '@/assets/modal/close.svg'
import  fullscreen  from '@/assets/modal/fullscreen.svg'

import full from '@/assets/modal/full.svg'
import newWindow from '@/assets/modal/newWindow.svg'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  url: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '应用'
  }
});

const emit = defineEmits(['close', 'update:visible']);

const settingStore = useSettingStore();
const isFullscreen = ref(false);
const iframeRef = ref(null);

// 监听visible变化，重置全屏状态并处理焦点
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时确保退出全屏并恢复其他元素
    if (isFullscreen.value) {
      toggleFullscreen();
    }
  } else {
    // 弹窗打开时，自动聚焦到iframe
    nextTick(() => {
      if (iframeRef.value && props.url) {
        // 方法1：直接聚焦iframe元素
        try {
          iframeRef.value.focus();
        } catch (error) {
          console.warn('iframe.focus() 失败:', error);
        }

        // 方法2：监听iframe加载完成后聚焦contentWindow
        const handleIframeLoad = () => {
          try {
            if (iframeRef.value && iframeRef.value.contentWindow) {
              iframeRef.value.contentWindow.focus();
            }
          } catch (error) {
            console.warn('contentWindow.focus() 失败:', error);
          }
        };

        // 如果iframe已经加载完成，直接聚焦
        if (iframeRef.value.contentDocument && iframeRef.value.contentDocument.readyState === 'complete') {
          handleIframeLoad();
        } else {
          // 否则监听load事件
          iframeRef.value.addEventListener('load', handleIframeLoad, { once: true });
        }

        // 方法3：延时备用聚焦方案
        setTimeout(() => {
          try {
            if (iframeRef.value && iframeRef.value.contentWindow) {
              iframeRef.value.contentWindow.focus();
            }
          } catch (error) {
            // 静默处理延时聚焦失败
          }
        }, 500);
      }
    });
  }
});

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    handleClose();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    handleClose();
  }
};

// 在新窗口打开
const openInNewWindow = () => {
  if (props.url) {
    window.open(props.url, '_blank');
  }
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
};

// 组件卸载时清理
onUnmounted(() => {
  document.body.classList.remove('modal-fullscreen-active');
});
</script>

<style scoped lang="scss">
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; /* 提高z-index，确保高于CollectionModal的500 */
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1200px;
  position: relative;
  z-index: 1001; /* 提高z-index，确保高于overlay */
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000; /* 更高的z-index确保覆盖所有元素，包括其他弹窗 */
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 99999 !important; /* 提高overlay的z-index */
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag; /* 允许拖动窗口 */
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none; /* 防止选中文本 */
}

.app-modal-spacer {
  width: 60px; /* 与控制按钮区域保持对称 */
}

.app-modal-controls {
  display: flex;
  gap: 10px;
  -webkit-app-region: no-drag; /* 控制按钮区域不可拖动窗口 */
}

.control-btn {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  border: none;
  background: none;
  font-size: 20px;
  padding: 0px !important;
  img{
    width: 19px;
    height: 19px;
  }
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

.app-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.empty-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  gap: 12px;
}

.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.icon-external {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='9' y='9' width='13' height='13' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'%3E%3C/path%3E%3C/svg%3E");
}

.icon-warning {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
  width: 48px;
  height: 48px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
</style>

<style>
/* 全局样式，确保全屏模式下禁用页面滚动 */
body.modal-fullscreen-active {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 全屏模式下的弹窗样式 */
.app-modal-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  z-index: 9999 !important;
}
</style>
