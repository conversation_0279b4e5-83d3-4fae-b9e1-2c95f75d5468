<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal">
      <div class="app-modal-header">
        <div class="app-modal-title">编辑图标</div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn close-btn" @click="handleClose" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <div class="edit-icon-container">
          <!-- 编辑图标表单 -->
          <div class="add-icon-form">
            <div class="form-item">
              <div class="form-label">图标名称</div>
              <Input v-model:value="editIcon.name" placeholder="请输入图标名称" />
            </div>
            
            <div class="form-item">
              <div class="form-label">网址</div>
              <Input v-model:value="editIcon.url" placeholder="请输入网站地址" addon-before="https://" />
              <div class="form-tip">输入不带https://的网址，如：www.example.com</div>
            </div>

            <div class="form-item">
             <div class="form-label">打开方式</div>
             <Radio.Group v-model:value="editIcon.iscanopen" button-style="solid">
               <Radio.Button :value="1">内嵌打开</Radio.Button>
               <Radio.Button :value="2">新窗口打开</Radio.Button>
             </Radio.Group>
           </div>

            <div class="form-item">
              <div class="form-label">图标类型</div>
              <Radio.Group v-model:value="iconDisplayType" button-style="solid">
                <Radio.Button value="original">原图标</Radio.Button>
                <Radio.Button value="text">文字图标</Radio.Button>
                <Radio.Button value="image">图片图标</Radio.Button>
                <Radio.Button value="api">接口图标</Radio.Button>
              </Radio.Group>
            </div>

            <div class="form-item" v-if="iconDisplayType === 'original'">
              <Alert message="保持使用当前图标" type="info"></Alert>
              <div class="icon-preview-container">
                <div class="original-icon-preview">
                  <img v-if="originalIcon.icon" :src="concatUrl(originalIcon.icon)" class="original-icon-img" />
                  <div v-else class="app-icon-div" :style="{ backgroundColor: originalIcon.color || '#f5f5f5' }">
                    <span v-text-scale="originalIcon.name.slice(0, 6)" class="app-icon-text">{{ originalIcon.name.slice(0, 6) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-item" v-if="iconDisplayType === 'text'">
              <Alert message="默认使用名字作为文字图标" type="warning"></Alert>
              <div class="icon-preview-container">
                <div class="icon-text-preview" :style="{ backgroundColor: editIcon.color || '#f5f5f5' }">
                  <div class="app-icon-div">
                    <span v-text-scale="editIcon.name.slice(0, 6)" class="app-icon-text">{{ editIcon.name.slice(0, 6) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-item" v-if="iconDisplayType === 'image'">
              <div class="form-label">上传图标</div>
              <div class="upload-container">
                <div class="upload-box" @click="triggerFileUpload">
                  <div v-if="!uploadedImage" class="upload-placeholder">
                    <i class="upload-icon"></i>
                    <div>点击上传图标</div>
                  </div>
                  <img v-else :src="concatUrl(uploadedImage)" class="uploaded-preview" />
                </div>
                <input
                  type="file"
                  ref="fileInputRef"
                  style="display: none;"
                  accept="image/*"
                  @change="handleFileUpload"
                />
              </div>
            </div>

            <div class="form-item" v-if="iconDisplayType === 'api'">
              <div class="form-label">接口图标</div>
              <Alert message="将根据网址自动获取网站图标" type="info" style="margin-bottom: 10px;"></Alert>
              <div class="api-icon-container">
                <div class="api-icon-preview-box">
                  <div v-if="apiIconLoading" class="api-loading">
                    <Spin size="small" />
                    <div>获取图标中...</div>
                  </div>
                  <div v-else-if="apiIconError" class="api-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-text">{{ apiIconError }}</div>
                    <Button size="small" @click="fetchApiIcon" style="margin-top: 8px;">重新获取</Button>
                  </div>
                  <div v-else-if="apiIconUrl" class="api-success">
                    <img :src="apiIconUrl" class="api-icon-preview" @error="handleApiIconError" style="width: 100%;height: 100%;" />
                  </div>
                  <div v-else class="api-placeholder">
                    <div class="placeholder-icon">🌐</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-actions">
              <Button @click="handleClose">取消</Button>
              <Button type="primary" @click="saveEditIcon">保存修改</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { Input, Radio, Alert, Button, Spin, message } from 'ant-design-vue'
import { getAllAppList, uploadFiles, queryAppName } from '@/api/navbar';
import  closeSvg  from '@/assets/modal/close.svg'
import textScale from '@/directives/textScale'
import { useUrlStore } from '@/stores/url'

const urlStore = useUrlStore();
// 注册指令
const vTextScale = textScale

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editingIcon: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'save-icon'])

// 编辑的图标数据
const editIcon = ref({
  name: '',
  url: '',
  iscanopen: 1,
  color: '#f5f5f5',
  icon: ''
})

// 原始图标数据（用于"保持原图标"选项）
const originalIcon = ref({
  name: '',
  icon: '',
  color: '#f5f5f5'
})

// 图标显示类型
const iconDisplayType = ref('original')

// 文件上传相关
const fileInputRef = ref(null)
const uploadedImage = ref('')

// API图标相关
const apiIconLoading = ref(false)
const apiIconError = ref('')
const apiIconUrl = ref('')

// 监听编辑图标数据变化，初始化表单
watch(() => props.editingIcon, (newIcon) => {
  if (newIcon) {
    editIcon.value = { ...newIcon }

    // 保存原始图标信息
    originalIcon.value = {
      name: newIcon.name,
      icon: newIcon.icon,
      color: newIcon.color || '#f5f5f5'
    }
    console.log(originalIcon.value,'value')
    // 默认选择"保持原图标"
    iconDisplayType.value = 'original'

    // 如果需要，也可以根据现有图标设置其他类型的预览数据
    if (newIcon.icon) {
      if (newIcon.icon.startsWith('http') || newIcon.icon.startsWith('/')) {
        uploadedImage.value = newIcon.icon
      } else {
        apiIconUrl.value = newIcon.icon
      }
    }
  }
}, { immediate: true })

// 监听URL变化，自动获取API图标
watch(() => editIcon.value.url, (newUrl) => {
  if (newUrl && iconDisplayType.value === 'api') {
    // 延迟执行，避免频繁调用
    clearTimeout(apiIconTimeout)
    apiIconTimeout = setTimeout(() => {
      fetchApiIcon()
    }, 500)
  }
})


// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  // 如果path为null那么就随机a-z加上.png
  return urlStore.concatUrl(path)
}

let apiIconTimeout = null

// 关闭弹窗
function handleClose() {
  emit('close')
}

// 处理覆盖层点击
function handleOverlayClick() {
  handleClose()
}

// 触发文件上传
function triggerFileUpload() {
  fileInputRef.value?.click()
}

// 处理文件上传
async function handleFileUpload(event) {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件大小（限制为2MB）
  if (file.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await uploadFiles(formData)
    if (response.status === 200) {
      uploadedImage.value = response.url
      message.success('图片上传成功')
    } else {
      message.error('图片上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    message.error('图片上传失败')
  }
}

// 获取API图标
async function fetchApiIcon() {
  if (!editIcon.value.url) {
    message.warning('请先输入网址')
    return
  }

  apiIconLoading.value = true
  apiIconError.value = ''
  
  try {
    const cleanUrl = editIcon.value.url.replace(/^https?:\/\//, '')
    const iconUrl = `https://favicon.im/zh/${cleanUrl}?larger=true`
    
    // 测试图标是否可以加载
    const img = new Image()
    img.onload = () => {
      apiIconUrl.value = iconUrl
      apiIconLoading.value = false
    }
    img.onerror = () => {
      apiIconError.value = '无法获取网站图标'
      apiIconLoading.value = false
    }
    img.src = iconUrl
  } catch (error) {
    apiIconError.value = '获取图标失败'
    apiIconLoading.value = false
  }
}

// 处理API图标错误
function handleApiIconError() {
  apiIconError.value = '图标加载失败'
}

// 保存编辑的图标
function saveEditIcon() {
  if (!editIcon.value.name.trim()) {
    message.warning('请输入图标名称')
    return
  }

  if (!editIcon.value.url.trim()) {
    message.warning('请输入网址')
    return
  }

  // 根据图标类型设置图标
  let iconValue = ''
  if (iconDisplayType.value === 'original') {
    // 保持原图标
    iconValue = originalIcon.value.icon
  } else if (iconDisplayType.value === 'image' && uploadedImage.value) {
    iconValue = uploadedImage.value
  } else if (iconDisplayType.value === 'api' && apiIconUrl.value) {
    iconValue = apiIconUrl.value
  }
  // 如果是文字图标，iconValue保持为空字符串

  const updatedIcon = {
    ...editIcon.value,
    icon: iconValue,
    websiteAddress: editIcon.value.url
  }

  emit('save-icon', updatedIcon)
  handleClose()
}

</script>

<style lang="scss" scoped>
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 800px;
  position: relative;
  z-index: 501;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
}

.control-btn:hover {
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
}

.edit-icon-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  padding: 16px;
  background-color: #ffffff;
}

.add-icon-form {
  width: 100%;
  height: auto;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  text-align: left;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
}

.form-tip {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eaeaea;
}

:deep(.ant-radio-group-solid) {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.ant-radio-button-wrapper-checked) {
  background-color: #4285F4;
  border-color: #4285F4;
  color: white;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 图标预览样式 */
.icon-preview-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.icon-text-preview {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  overflow: hidden;
}

.app-icon-div {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

.app-icon-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 上传样式 */
.upload-container {
  margin-top: 10px;
}

.upload-box {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.upload-box:hover {
  border-color: #1677ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.upload-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23888' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
}

.uploaded-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* API图标样式 */
.api-icon-container {
  margin-top: 10px;
}

.api-icon-preview-box {
  width: 100px;
  height: 100px;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  overflow: hidden;
}

.api-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #1677ff;
  font-size: 12px;
  gap: 8px;
}

.api-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 8px;
}

.error-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.error-text {
  margin-bottom: 4px;
  line-height: 1.2;
}

.api-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.api-icon-preview {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 4px;
}

.api-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.placeholder-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 原图标预览样式 */
.original-icon-preview {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 2px solid #d9d9d9;
}

.original-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 10px;
}
</style>