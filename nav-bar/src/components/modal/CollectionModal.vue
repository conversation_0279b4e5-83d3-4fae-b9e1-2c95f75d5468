<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
      <div class="app-modal-header">
        <div class="app-modal-title">{{ title }} - {{ collectList.length || 0 }}个应用</div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <!-- 应用合集模态框内容 -->
        <div class="collection-modal-content">
          <!-- 显示合集内的应用 -->
          <div v-if="collectList.length > 0" class="collection-modal-grid">
            <div 
              v-for="(app, index) in collectList" 
              :key="app.id || `collection-app-${index}-${Date.now()}`"
              class="collection-app-item"
              @click="openApp(app)"
            >
              <div class="app-card-header">
                <div class="app-icon-wrapper">
                  <img v-if="app.logo" :src="concatUrl(app.logo)" alt="app-icon" class="app-icon-img" />
                  <div v-else class="app-icon-div">
                    <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                  </div>
                  <div v-if="app.iscanopen == 2" class="newWindow">
                    <svg t="1748943554105" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5226" width="20" height="20"><path d="M914.285714 914.285714h-804.571428v-804.571428h248.685714V0H109.714286C51.2 0 0 51.2 0 109.714286v797.257143c0 65.828571 51.2 117.028571 109.714286 117.028571h797.257143c65.828571 0 109.714286-51.2 109.714285-109.714286V658.285714h-109.714285v256h7.314285zM629.028571 0v109.714286h204.8L277.942857 665.6l80.457143 80.457143 555.885714-555.885714v204.8H1024V0H629.028571z" fill="#999999" p-id="5227"></path></svg>
                  </div>
                </div>
                <div class="app-title-area">
                  <div class="app-name-text" :title="app.name">{{ app.name.length > 7 ? app.name.slice(0, 7) + '...' : app.name }}</div>
                  <div class="app-category">{{ title || '未分类' }}</div>
                </div>
              </div>
              <div class="app-card-content">
                <div class="app-description-text">{{ app.descs }}</div>
              </div>
              <div class="app-card-footer">
                <div @click.stop="newOpenUrl(app.websiteAddress)">
                  <img :src="newlink" />
                </div>
                <div>
                  <svg v-if="!app.isCollect" @click.stop="addToCollect(app)" width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.67368 1.27441C8.61668 1.27441 7.63668 1.80641 6.98568 2.68141C6.34168 1.80641 5.35468 1.27441 4.29768 1.27441C2.39368 1.27441 0.84668 2.96841 0.84668 5.04741C0.84668 6.28641 1.39968 7.16141 1.84768 7.86141C3.14268 9.89841 6.40468 12.4254 6.54468 12.5304C6.67768 12.6354 6.83168 12.6844 6.98568 12.6844C7.13968 12.6844 7.29368 12.6354 7.42668 12.5304C7.56668 12.4254 10.8217 9.89141 12.1237 7.86141C12.5717 7.16141 13.1247 6.28641 13.1247 5.04741C13.1247 2.96841 11.5777 1.27441 9.67368 1.27441Z" fill="#C8C8C8"/>
                  </svg>
                  <svg v-else @click.stop="cancelCollect(app)" width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.67368 1.27441C8.61668 1.27441 7.63668 1.80641 6.98568 2.68141C6.34168 1.80641 5.35468 1.27441 4.29768 1.27441C2.39368 1.27441 0.84668 2.96841 0.84668 5.04741C0.84668 6.28641 1.39968 7.16141 1.84768 7.86141C3.14268 9.89841 6.40468 12.4254 6.54468 12.5304C6.67768 12.6354 6.83168 12.6844 6.98568 12.6844C7.13968 12.6844 7.29368 12.6354 7.42668 12.5304C7.56668 12.4254 10.8217 9.89141 12.1237 7.86141C12.5717 7.16141 13.1247 6.28641 13.1247 5.04741C13.1247 2.96841 11.5777 1.27441 9.67368 1.27441Z" fill="#FF452D"/>
                  </svg>
                </div>
                <button type="primary" @click.stop="selectApp(app)">添加</button>
              </div>
            </div>

            




          </div>
          

          <!-- 空合集提示 -->
          <div v-else class="empty-collection">
            <div class="empty-collection-icon">
              <div class="i-carbon-folder-off" style="font-size: 48px;"></div>
            </div>
            <div class="empty-collection-text">应用合集为空</div>
            <div class="empty-collection-desc">此应用合集暂无内容</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useUrlStore } from '@/stores/url';
import { useSettingStore } from '@/stores/setting.js';
import  closeSvg  from '@/assets/modal/close.svg'
import { getCollectListByid } from '@/api/navbar';
import { message, Button } from 'ant-design-vue';
import { AddUseCollect, deleteCollect} from '@/api/collect'
import newlink from '@/assets/icons/newlink.svg'
const urlStore = useUrlStore();
const settingStore = useSettingStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  collection: {
    type: Object,
    default: null
  },
  title: {
    type: String,
    default: '应用合集'
  },
  currentCategory: {
    type: String,
    default: 'all'
  }
});


const collectList = ref([])

// 添加应用到首页
const selectApp = (app) => {
  
  
  console.log(app,'app')

  // 构建一个符合首页展示格式的应用对象
  const homeApp = {
    id: app.id, // 始终使用新生成的唯一ID，不再使用app.id
    name: app.name,
    type: 'app',
    icon: app.logo, // Home.vue中使用icon作为图标
    url: app.websiteAddress || '',
    description: app.descs,
    color: app.color || '#f5f5f5',
    size: {
      w: app.w || 1,
      h: app.h || 1
    },
    iscanopen:app.iscanopen,
    // 使用当前分类，如果没有传入则使用app的分类或默认分类
    category: props.currentCategory || 'app',
    iscanopen: app.iscanopen
  };
  
  console.log(homeApp,'homeApps')
  
  // 发出添加图标的事件
  emit('add-icon', homeApp);
  // 关闭弹窗
  // handleClose();
}

const addToCollect = (app) => {
  const token = localStorage.getItem('token')  
  if(token) {
    AddUseCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('添加成功')
      }
    })
  }
  app.isCollect = true
  emit('add-to-dock', app)

  // emit('close')
}

const cancelCollect = (app) => {
  const token = localStorage.getItem('token')  
  if(token) {
    deleteCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('删除成功')
      }
    })
  }
  app.isCollect = false
  emit('remove-from-dock', app)
}


// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  return urlStore.concatUrl(path)
}

const emit = defineEmits(['close', 'update:visible', 'open-app']);

const isFullscreen = ref(false);
const isMinimized = ref(false);

// 处理并确保子项格式正确
const processedChildren = computed(() => {
  if (!props.collection || !props.collection.children) {
    return [];
  }
  
  return props.collection.children.map(child => {
    // 确保每个子项都有icon和url属性
    const processedChild = { ...child };
    
    if (!processedChild.icon && processedChild.logo) {
      processedChild.icon = processedChild.logo;
    }
    
    if (processedChild.websiteAddress && !processedChild.url) {
      processedChild.url = `https://${processedChild.websiteAddress}`;
    }
    
    return processedChild;
  });
});

// 监听visible变化，重置全屏状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时确保退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
    }
    // 重置最小化状态
    if (isMinimized.value) {
      isMinimized.value = false;
    }
  } else if (props.collection) {
    console.log(props.collection,'props.collection')
    // 弹窗打开时打印调试信息
    getCollectList(props.collection.originalId)
  }
});

const newOpenUrl = (url) => {
  window.open('http://' + url, '_blank')
}


const getCollectList = (id) => {
  getCollectListByid(id).then(res => {
    
    if(res.status == 200) {
      collectList.value = res.data
      const localDockApps = JSON.parse(localStorage.getItem('homeDockApps'))
      collectList.value.forEach(item => {
        localDockApps.forEach(item2 => {
          if (item.id === item2.id) { 
            item.isCollect = true
          }
        })
      })
    }else{
      message.error('获取合集失败')
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    handleClose();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    handleClose();
  }
};

// 最小化弹窗
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value;
  // 如果已经最小化，可以添加动画或其他效果
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('collection-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
};

// 打开应用
const openApp = (app) => {
  
  
  // 确保应用数据完整
  if (!app.icon && app.logo) {
    
    app.icon = app.logo;
  }
  
  // 确保URL格式正确
  if (app.websiteAddress && !app.url) {
    
    app.url = `https://${app.websiteAddress}`;
  }
  
  // 先关闭当前弹窗，解决层级问题
  // handleClose();
  
  // 触发事件，通知父组件打开应用
  emit('open-app', app);
};
</script>

<style lang="scss" scoped>
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  min-height: 200px;
  height: max-content;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1010px;
  position: relative;
  z-index: 1201;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 1299; /* 确保比AppModal的z-index低 */
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 1298; /* 确保比AppModal的overlay低 */
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag; /* 允许拖动窗口 */
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none; /* 防止选中文本 */
}

.app-modal-spacer {
  width: 60px; /* 与控制按钮区域保持对称 */
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag; /* 控制按钮区域不可拖动窗口 */
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
    border: none;
    box-shadow: none;
  &:focus{
    outline: none;
    border: none;
    box-shadow: none;
  }
  /* box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.2) inset; */
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.close-btn {
  /* background-color: #ff5f57; */
}

.minimize-btn {
  background-color: #febc2e;
}

.maximize-btn {
  background-color: #28c840;
}

.app-modal-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

/* 定义图标样式 */
.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath stroke='%23000' stroke-width='1.5' d='M4 4l8 8m0-8l-8 8'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath stroke='%23000' stroke-width='1.5' d='M4 8h8'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Crect stroke='%23000' stroke-width='1.5' x='4' y='4' width='8' height='8' fill='none'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 合集模态框内容样式 */
.collection-modal-content {
  padding: 5px;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  background-color: #fafafa;
}

.collection-modal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 225px);
  gap: 20px;
  padding: 10px;
  justify-content: center; /* 让网格在容器中居中 */
}

.collection-app-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 12px;
  border-radius: 12px;
  background-color: #f3f3f3;
  position: relative;
  // width: 225px;
}

.collection-app-item:hover {
  transform: translateY(-5px);
  background-color: rgba(0, 0, 0, 0.03);
}

.collection-app-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  background-color: #f7f7f7;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.collection-app-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.collection-app-name {
  font-size: 13px;
  text-align: center;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.empty-collection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.empty-collection-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #e0e0e0;
}

.empty-collection-text {
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.empty-collection-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
.app-card {
  width: 100%;
  height: 190px;
  background-color: #f1f1f1;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.app-card-header {
  display: flex;
  // padding: 16px;
  position: relative;
}

.app-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  // overflow: hidden;
  margin-right: 12px;
  background-color: #f5f5f5;
  flex-shrink: 0;
  position: relative;
  img{
    border-radius: 8px;
  }
}

.app-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.app-title-area {
  flex: 1;
  text-align: left;
}

.app-name-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.app-category {
  font-size: 12px;
  color: #888;
}
.app-card-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
  gap: 10px;
  div{
    width: 50px;
    height: 23px;
    background: #E9E9E9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  button{
    background: #85c4fa;
    width: 50px;
    height: 23px;
    border-radius: 4px;
    border: none;
    outline: none;
    color: #fff;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
  }
  button:hover{
    background: #0088ff;
  }
}
.app-card-content{
  padding: 10px 0px;
}

.app-description-text{
  font-size: 10px;
  color: #939393;
  //最多显示三行
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: left;
}

.app-direct-link {
  /* position: absolute; */
  top: 12px;
  right: 12px;
  font-size: 12px;
  color: #7DB1FF;
  text-decoration: none;
  display: flex;
  align-items: center;
}
.collect-icon{
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 12px;
  color: #7DB1FF;
  text-decoration: none;
  display: flex;
  align-items: center;
  z-index: 100;
}
.newWindow{
  position: absolute;
  background: rgba($color: #ffffff, $alpha: 0.5);
  padding: 5px;
  right: 0px;
  top: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
