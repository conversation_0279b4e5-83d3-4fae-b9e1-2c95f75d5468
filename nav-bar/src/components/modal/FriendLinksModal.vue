<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
      <div class="app-modal-header">
        <div class="app-modal-title">友情链接</div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <div class="friend-links-container">
          <Button type="primary" class="applyFor" @click="applyFor">申请友链</Button>
          <div class="friend-links-grid">
            <div 
              v-for="link in friendLinks" 
              :key="link.id"
              class="friend-link-item"
              @click="openLink(link.url)"
            >
              <div class="link-icon">
                <img :src="link.urllog" :alt="link.name" />
              </div>
              <div class="link-info">
                <div class="link-name">{{ link.urlname }}</div>
                <div class="link-description">{{ link.urldescs }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Modal
      v-model:visible="modalVisible"
      title="申请友链"
      :centered="true"
      class="todo-modal"
      :width="500"
      cancelText="取消"
      okText="确定"
    >
      <IframeCard :url="iframeUrl" class="kind-applyFor" />
    </Modal>

<!--       :maskClosable="false" -->
</template>

<script setup>
import { ref, watch, onUnmounted, onMounted } from 'vue';
import { useSettingStore } from '@/stores/setting.js';
import closeSvg from '@/assets/modal/close.svg'
import fullscreen from '@/assets/modal/fullscreen.svg'
import newwindow from '@/assets/modal/newWindows.svg'
import { getKindList } from '@/api/kind.js'
import { Button, Modal } from 'ant-design-vue'
import IframeCard from '@/components/iframe/IframeCard.vue'
import { getExchangeList } from '@/api/setting'


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const modalVisible = ref(false)
const iframeUrl = ref('')
const emit = defineEmits(['close', 'update:visible']);

const settingStore = useSettingStore();
const isFullscreen = ref(false);

const applyFor = () => {
  modalVisible.value = true
}


// 友情链接数据
const friendLinks = ref([
  {
    id: 1,
    name: 'GitHub',
    description: '全球最大的代码托管平台',
    url: 'https://github.com',
    icon: 'https://github.com/favicon.ico'
  },
  {
    id: 2,
    name: 'Stack Overflow',
    description: '程序员问答社区',
    url: 'https://stackoverflow.com',
    icon: 'https://stackoverflow.com/favicon.ico'
  },
  {
    id: 3,
    name: 'MDN Web Docs',
    description: 'Web开发者文档',
    url: 'https://developer.mozilla.org',
    icon: 'https://developer.mozilla.org/favicon-48x48.cbbd161b5b0b.png'
  },
  {
    id: 4,
    name: 'Vue.js',
    description: '渐进式JavaScript框架',
    url: 'https://vuejs.org',
    icon: 'https://vuejs.org/logo.svg'
  },
  {
    id: 5,
    name: 'Ant Design Vue',
    description: 'Vue UI组件库',
    url: 'https://antdv.com',
    icon: 'https://antdv.com/assets/logo.1ef800a8.svg'
  },
  {
    id: 6,
    name: 'Vite',
    description: '下一代前端构建工具',
    url: 'https://vitejs.dev',
    icon: 'https://vitejs.dev/logo.svg'
  }
]);

// 监听visible变化，重置全屏状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时确保退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
    }
  }
});

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    handleClose();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    handleClose();
  }
};

// 打开链接
const openLink = (url) => {
  window.open(url, '_blank');
};

// 在新窗口打开（暂时不实现具体功能）
const openInNewWindow = () => {
  // 可以实现在新窗口中打开友情链接页面的功能
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
};

const getFriendLinks = () => {
  getKindList().then(res => {
          console.log(res,'friendLinks')
    if(res.status == 200) {
      friendLinks.value = res.data
      console.log(friendLinks,'friendLinks')
    }
  })
}

// 组件卸载时清理
onUnmounted(() => {
  document.body.classList.remove('modal-fullscreen-active');
});

function getApplyForLink() {
    getExchangeList().then(res => {
      if (res.status === 200 && Array.isArray(res.data)) {
          iframeUrl.value = res.data.find(item => item.type === 2)?.questionUrl
      }
    })
}

onMounted(() => {
  getFriendLinks()
  getApplyForLink()
})

</script>

<style scoped>
/* 复用AppModal的基础样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1010px;
  position: relative;
  z-index: 1001;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 99999 !important;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 15px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

/* 友情链接特有样式 */
.friend-links-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}

.friend-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 225px);
  gap: 16px;
}

.friend-link-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.friend-link-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.link-icon {
  width: 48px;
  height: 48px;
  margin-right: 12px;
  flex-shrink: 0;
}

.link-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.link-info {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.link-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.link-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
</style>

<style>
/* 全局样式，确保全屏模式下禁用页面滚动 */
body.modal-fullscreen-active {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 全屏模式下的弹窗样式 */
.app-modal-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  z-index: 9999 !important;
}
.applyFor{
  position: absolute;
  right: 10px;
  bottom: 10px;
}
.kind-applyFor{
  height: 500px !important;
}
</style>
