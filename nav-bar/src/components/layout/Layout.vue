<template>
  <div class="layout-wrapper" :class="{ 'fullscreen-active': isModalFullscreen }">
    <SideNavigation
      :is-open="!isSideNavCollapsed"
      @toggle="toggleSideNav"
      class="side-navigation"
      :class="{ 'lower-z-index': isModalFullscreen }"
    />
    <div class="layout-container" :class="{ 'lower-z-index': isModalFullscreen }">
      <HeaderBar 
        @toggle="toggleSideNav" 
        @add-icon-modal="handleAddIconModal"
        :class="{ 'lower-z-index': isModalFullscreen }" 
      />
      <main class="main-content" :class="{ 'expanded': isSideNavCollapsed }">
        <router-view v-slot="{ Component }">
          <transition name="page" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, provide, computed, onMounted, onUnmounted, watch } from 'vue'
import SideNavigation from './SideNavigation.vue'
import HeaderBar from './HeaderBar.vue'

// 侧边栏折叠状态
const isSideNavCollapsed = ref(false)

// 是否使用透明背景
const useTransparentBg = ref(localStorage.getItem('bgTransparent') === 'true')

// 当前主题
const currentTheme = ref(localStorage.getItem('theme') || 'light')

// 控制布局元素的z-index状态
const isModalFullscreen = ref(false)

watch(() => isModalFullscreen.value, (newVal) => {
  console.log(newVal,'newVal')
})

// 监听主题和透明背景变化
watch(() => currentTheme.value, () => {
  document.documentElement.setAttribute('data-theme', currentTheme.value)
})

// 监听透明背景设置
watch(() => useTransparentBg.value, () => {
  if (useTransparentBg.value) {
    document.documentElement.classList.add('transparent-bg')
  } else {
    document.documentElement.classList.remove('transparent-bg')
  }
})

// 提供侧边栏状态给子组件
provide('isSideNavCollapsed', isSideNavCollapsed)

// 提供弹窗全屏状态给子组件
provide('isModalFullscreen', isModalFullscreen)

// 切换侧边栏状态
function toggleSideNav() {
  isSideNavCollapsed.value = !isSideNavCollapsed.value
}

// 处理AppModal全屏事件
function handleModalFullscreen(event) {
  isModalFullscreen.value = event.detail.isFullscreen
  document.body.classList.toggle('modal-fullscreen-mode', isModalFullscreen.value)
}

// 处理添加图标模态框事件
function handleAddIconModal() {
  // 触发全局事件，以便Home组件可以监听
  window.dispatchEvent(new CustomEvent('open-add-icon-modal'))
}

// 初始化设置
onMounted(() => {
  // 设置初始主题
  document.documentElement.setAttribute('data-theme', currentTheme.value)
  
  // 设置初始透明背景
  if (useTransparentBg.value) {
    document.documentElement.classList.add('transparent-bg')
  }
  
  // 监听透明背景变化
  window.addEventListener('storage', (event) => {
    if (event.key === 'bgTransparent') {
      useTransparentBg.value = event.newValue === 'true'
    }
  })
  
  // 监听AppModal全屏事件
  window.addEventListener('app-modal-fullscreen', handleModalFullscreen)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('app-modal-fullscreen', handleModalFullscreen)
  window.removeEventListener('storage', () => {})
})
</script>


<style scoped>
.layout-wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
  color: #fff;
}

.layout-container {
  flex: 1;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  display: flex;
  flex-direction: column;
  background-color: transparent !important;
  overflow: hidden;
  width: 100%;
  height: 100vh;
  color: #fff;
  transition: z-index 0.01s ease;
}

:root:not(.transparent-bg) .layout-container {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.main-content {
  flex: 1;
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-color: transparent !important;
  overflow: auto;
  /* z-index: 1; */
  color: #fff;
  transition: all 0.3s ease;
}

:root:not(.transparent-bg) .main-content {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.main-content.expanded {
  margin: 0;
  width: 100%;
}

/* 弹窗全屏时降低布局元素的z-index */
.lower-z-index {
  z-index: 0 !important;
}

/* 页面过渡效果 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .main-content {
    margin: 0;
    padding: 0;
    width: 100%;
  }
  
  .main-content.expanded {
    margin: 0;
    width: 100%;
  }
}

/* 为主内容添加阴影 */
.main-content::before {
  display: none;
}
</style>

<style>
/* 全屏模式下禁用滚动 */
body.modal-fullscreen-mode {
  overflow: hidden !important;
}

/* 确保模态框在全屏模式下总是显示在最顶层 */
body.modal-fullscreen-mode .app-modal-overlay,
body.modal-fullscreen-mode .app-modal-fullscreen {
  z-index: 9999 !important;
}
.side-navigation{
  z-index: 1;
}
</style> 