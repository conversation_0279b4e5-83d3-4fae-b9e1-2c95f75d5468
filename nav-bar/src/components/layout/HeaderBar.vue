<template>
  <header class="header-bar" :class="{
    'side-nav-collapsed': sideNavCollapsed,
    'lower-z-index': isModalFullscreen
  }">
    <div class="header-controls">

      <!-- 创建一个容器包裹三个按钮 -->
      <div class="buttons-container" style="justify-content: flex-end;">

        <!-- 添加应用快捷按钮 - 始终显示 -->
        <div class="icon-button refresh-wallpaper-button" @click="addApp">
          <Tooltip title="添加应用" placement="bottom">
            <img v-if="selectedIndex == 0" :src="addwebCat" alt="app" class="wallpaper-image" />
            <img v-else :src="addweb" alt="app" class="wallpaper-image" />
          </Tooltip>
        </div>
      </div>

      <!-- 切换壁纸快捷按钮 -->
      <transition name="fade">
        <div v-show="showButtons" class="wallpaper-button-container"
          @mouseenter="showAutoSwitchButtonsWithDelay"
          @mouseleave="hideAutoSwitchButtonsWithDelay">
          <div class="icon-button refresh-wallpaper-button" @click="refreshWallpaper"
            :class="{ 'rotate-animation': isRefreshing }">
            <Tooltip title="切换壁纸" placement="right">
              <img v-if="selectedIndex == 0" :src="cgwallpaperCat" alt="壁纸" class="wallpaper-image" />
              <img v-else :src="cgwallpaper" alt="壁纸" class="wallpaper-image" />
            </Tooltip>
          </div>

          <!-- 自动切换壁纸控制按钮 -->
          <div v-show="showAutoSwitchButtons" class="auto-switch-controls">
            <div class="auto-switch-button enable" @click.stop="enableAutoSwitch" :class="{ 'active': isAutoRefreshEnabled }">
              <Tooltip title="开启自动切换" placement="bottom">
                <div class="switch-icon enable-icon">
                  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="22" height="22" rx="11" fill="white" fill-opacity="0.28"/>
                  <path d="M8.35199 6.80985C8.44675 6.80985 8.50767 6.81133 8.54658 6.81428C8.55674 6.8187 8.5652 6.82313 8.57366 6.82756C8.59735 6.84526 8.64134 6.89394 8.67518 6.93377L8.67687 6.93524L8.69718 6.95737C8.93238 7.21994 9.29279 7.36598 9.39939 7.40434L9.40278 7.40581C9.64305 7.49285 9.8901 7.54005 10.1219 7.5784C10.1591 7.5843 10.1981 7.58873 10.2353 7.5902L10.4857 7.60348C10.506 7.60496 10.5246 7.60496 10.5449 7.60496C10.6532 7.60496 10.7784 7.59168 10.9155 7.56808C11.4248 7.47809 11.7345 7.38959 12.1152 7.12406L12.1186 7.12111C12.2336 7.04145 12.3081 6.95147 12.3639 6.88656L12.3656 6.88509L12.3673 6.88361C12.3758 6.87329 12.3876 6.86001 12.3978 6.84968L12.4451 6.82313H12.5078C12.655 6.82313 12.8445 6.82608 12.9697 6.82756C13.0475 6.82903 13.0864 6.82903 13.1118 6.82903C13.237 6.82903 13.3047 6.83346 13.3386 6.83788C13.3758 6.86296 13.4215 6.90279 13.4689 6.94409L13.4756 6.94999L15.4232 8.60953C15.5146 8.69361 15.5992 8.76294 15.6736 8.82343L15.6855 8.83228C15.7633 8.89571 15.8378 8.95619 15.897 9.01224C15.9004 9.01519 15.9021 9.01814 15.9055 9.01962C15.9359 9.04765 15.9664 9.0742 15.9935 9.0978L15.9951 9.09928C16.0188 9.11993 16.0442 9.14206 16.0628 9.15976L14.8496 10.1924C14.8327 10.2056 14.8175 10.2204 14.8022 10.2351C14.7498 10.2882 14.6398 10.3959 14.589 10.4151C14.5873 10.4151 14.5856 10.4151 14.5839 10.4166L14.5823 10.4151C14.4384 10.3399 14.2726 10.3 14.1051 10.3C13.5924 10.3 13.1761 10.6629 13.1761 11.1099V14.2416C13.1761 14.5647 13.1744 14.8877 13.1727 15.1724C13.1237 15.1813 13.0306 15.1887 12.8614 15.1887L12.4705 15.1857H12.4604L8.0508 15.1872C7.92728 15.1872 7.86298 15.1828 7.82914 15.1783C7.82575 15.137 7.82745 15.0692 7.82745 15.0205V11.1114C7.82745 10.896 7.72931 10.691 7.55502 10.539C7.38074 10.3871 7.14554 10.3015 6.89849 10.3015C6.73436 10.3015 6.57192 10.3399 6.42978 10.4122C6.42132 10.4107 6.41456 10.4077 6.40948 10.4048C6.38917 10.3915 6.35872 10.365 6.32826 10.3354L6.31303 10.3222L6.17766 10.2056L6.1692 10.1983L5.70219 9.80735C5.62097 9.73654 5.53975 9.66426 5.45853 9.59198C5.43822 9.57427 5.41792 9.55805 5.39761 9.54182C5.34854 9.50642 5.29778 9.46069 5.2301 9.40021L5.22671 9.39726L5.14718 9.32792L5.13872 9.32055C5.10996 9.29547 5.08119 9.27187 5.05919 9.25417L5.04735 9.24384L5.04397 9.24089C5.01182 9.21581 4.96275 9.17598 4.9509 9.15976L4.94921 9.15533V9.15386C4.9729 9.12288 5.02028 9.08453 5.11334 9.01077C5.19795 8.94586 5.25886 8.89128 5.30793 8.8485L5.3147 8.8426C5.357 8.80425 5.39761 8.76884 5.43145 8.74377C5.44161 8.73639 5.45176 8.72754 5.46191 8.72017L7.55164 6.93819L7.56517 6.92639C7.59225 6.90279 7.62778 6.87033 7.65316 6.85263L7.78176 6.82608C7.89513 6.82018 8.13541 6.80985 8.35199 6.80985ZM8.35199 6C8.01527 6 7.63455 6.02213 7.63455 6.02213L7.3249 6.08851C7.12523 6.15637 6.99325 6.27438 6.89511 6.36141L4.80707 8.14191C4.75124 8.18469 4.70047 8.23189 4.64802 8.27762C4.60233 8.31745 4.55834 8.35876 4.49404 8.40743C4.3096 8.55052 4.07948 8.72902 4.0101 9.05502C3.97118 9.23499 4.0541 9.39873 4.09978 9.48724C4.18269 9.65246 4.31299 9.75572 4.4162 9.8398C4.43989 9.85898 4.46527 9.87815 4.48727 9.89733L4.5668 9.96666C4.63956 10.0316 4.71401 10.0979 4.79523 10.1584C4.88153 10.2351 4.96782 10.3118 5.05412 10.3871L5.52621 10.7839L5.66158 10.9004C5.7208 10.9535 5.78848 11.014 5.87816 11.0701C6.04568 11.1719 6.2555 11.2264 6.45178 11.2264C6.55669 11.2264 6.65652 11.2117 6.7462 11.1792C6.79866 11.1601 6.84942 11.1379 6.89849 11.1128V15.0131C6.8968 15.1695 6.89511 15.3303 6.95771 15.4778C7.03555 15.6578 7.20645 15.8009 7.33674 15.8717C7.54487 15.9897 7.8156 15.9985 8.0508 15.9985L12.4604 15.997L12.8546 16C13.1575 16 13.501 15.9852 13.7599 15.8141C13.9206 15.7064 14.0476 15.5265 14.0915 15.3436L14.0983 15.2905L14.1034 14.2446V11.1114C14.2303 11.1778 14.386 11.2279 14.5755 11.2279C14.6347 11.2279 14.6956 11.2235 14.7616 11.2132C15.1085 11.1571 15.3335 10.9358 15.4977 10.7736L16.743 9.71441L16.7701 9.68786C16.8446 9.60525 17.0425 9.38841 16.9918 9.07567C16.9918 9.0506 16.9901 9.02404 16.9884 8.99897L16.9715 8.92669C16.8936 8.73639 16.7583 8.61986 16.6483 8.52545L16.5789 8.46497C16.496 8.38678 16.4063 8.31303 16.3166 8.24074C16.2405 8.17879 16.1644 8.11831 16.0848 8.04307L14.122 6.37174C14.0205 6.28323 13.9054 6.18292 13.7447 6.10769C13.6025 6.04278 13.413 6.01918 13.1068 6.01918C13.0509 6.01918 12.7345 6.01328 12.5044 6.01328C12.3893 6.01328 12.2946 6.01475 12.2658 6.01918C12.2218 6.02655 12.1169 6.05311 12.0678 6.06933L11.8309 6.20209C11.7328 6.26553 11.6685 6.34223 11.616 6.40419C11.5873 6.43812 11.5568 6.475 11.5348 6.48975C11.2979 6.65349 11.1338 6.70217 10.7277 6.77298C10.6532 6.78625 10.5889 6.79363 10.5432 6.79363L10.2945 6.78183C10.083 6.7479 9.9104 6.71249 9.75812 6.65791C9.62952 6.61071 9.48738 6.52515 9.42816 6.45877L9.40786 6.43664C9.33509 6.35403 9.25049 6.2611 9.13374 6.18144C9.0559 6.12834 8.86808 6.03835 8.76825 6.02065C8.67687 6.0059 8.51951 6 8.35199 6Z" fill="white"/>
                  </svg>
                </div>
              </Tooltip>
            </div>
            <div class="auto-switch-button disable" @click.stop="disableAutoSwitch" :class="{ 'active': !isAutoRefreshEnabled }">
              <Tooltip title="关闭自动切换" placement="bottom">
                <div class="switch-icon disable-icon">
                  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="22" height="22" rx="11" fill="white" fill-opacity="0.28"/>
                  <path d="M8.35199 6.80985C8.44675 6.80985 8.50767 6.81133 8.54658 6.81428C8.55674 6.8187 8.5652 6.82313 8.57366 6.82756C8.59735 6.84526 8.64134 6.89394 8.67518 6.93377L8.67687 6.93524L8.69718 6.95737C8.93238 7.21994 9.29279 7.36598 9.39939 7.40434L9.40278 7.40581C9.64305 7.49285 9.8901 7.54005 10.1219 7.5784C10.1591 7.5843 10.1981 7.58873 10.2353 7.5902L10.4857 7.60348C10.506 7.60496 10.5246 7.60496 10.5449 7.60496C10.6532 7.60496 10.7784 7.59168 10.9155 7.56808C11.4248 7.47809 11.7345 7.38959 12.1152 7.12406L12.1186 7.12111C12.2336 7.04145 12.3081 6.95147 12.3639 6.88656L12.3656 6.88509L12.3673 6.88361C12.3758 6.87329 12.3876 6.86001 12.3978 6.84968L12.4451 6.82313H12.5078C12.655 6.82313 12.8445 6.82608 12.9697 6.82756C13.0475 6.82903 13.0864 6.82903 13.1118 6.82903C13.237 6.82903 13.3047 6.83346 13.3386 6.83788C13.3758 6.86296 13.4215 6.90279 13.4689 6.94409L13.4756 6.94999L15.4232 8.60953C15.5146 8.69361 15.5992 8.76294 15.6736 8.82343L15.6855 8.83228C15.7633 8.89571 15.8378 8.95619 15.897 9.01224C15.9004 9.01519 15.9021 9.01814 15.9055 9.01962C15.9359 9.04765 15.9664 9.0742 15.9935 9.0978L15.9951 9.09928C16.0188 9.11993 16.0442 9.14206 16.0628 9.15976L14.8496 10.1924C14.8327 10.2056 14.8175 10.2204 14.8022 10.2351C14.7498 10.2882 14.6398 10.3959 14.589 10.4151C14.5873 10.4151 14.5856 10.4151 14.5839 10.4166L14.5823 10.4151C14.4384 10.3399 14.2726 10.3 14.1051 10.3C13.5924 10.3 13.1761 10.6629 13.1761 11.1099V14.2416C13.1761 14.5647 13.1744 14.8877 13.1727 15.1724C13.1237 15.1813 13.0306 15.1887 12.8614 15.1887L12.4705 15.1857H12.4604L8.0508 15.1872C7.92728 15.1872 7.86298 15.1828 7.82914 15.1783C7.82575 15.137 7.82745 15.0692 7.82745 15.0205V11.1114C7.82745 10.896 7.72931 10.691 7.55502 10.539C7.38074 10.3871 7.14554 10.3015 6.89849 10.3015C6.73436 10.3015 6.57192 10.3399 6.42978 10.4122C6.42132 10.4107 6.41456 10.4077 6.40948 10.4048C6.38917 10.3915 6.35872 10.365 6.32826 10.3354L6.31303 10.3222L6.17766 10.2056L6.1692 10.1983L5.70219 9.80735C5.62097 9.73654 5.53975 9.66426 5.45853 9.59198C5.43822 9.57427 5.41792 9.55805 5.39761 9.54182C5.34854 9.50642 5.29778 9.46069 5.2301 9.40021L5.22671 9.39726L5.14718 9.32792L5.13872 9.32055C5.10996 9.29547 5.08119 9.27187 5.05919 9.25417L5.04735 9.24384L5.04397 9.24089C5.01182 9.21581 4.96275 9.17598 4.9509 9.15976L4.94921 9.15533V9.15386C4.9729 9.12288 5.02028 9.08453 5.11334 9.01077C5.19795 8.94586 5.25886 8.89128 5.30793 8.8485L5.3147 8.8426C5.357 8.80425 5.39761 8.76884 5.43145 8.74377C5.44161 8.73639 5.45176 8.72754 5.46191 8.72017L7.55164 6.93819L7.56517 6.92639C7.59225 6.90279 7.62778 6.87033 7.65316 6.85263L7.78176 6.82608C7.89513 6.82018 8.13541 6.80985 8.35199 6.80985ZM8.35199 6C8.01527 6 7.63455 6.02213 7.63455 6.02213L7.3249 6.08851C7.12523 6.15637 6.99325 6.27438 6.89511 6.36141L4.80707 8.14191C4.75124 8.18469 4.70047 8.23189 4.64802 8.27762C4.60233 8.31745 4.55834 8.35876 4.49404 8.40743C4.3096 8.55052 4.07948 8.72902 4.0101 9.05502C3.97118 9.23499 4.0541 9.39873 4.09978 9.48724C4.18269 9.65246 4.31299 9.75572 4.4162 9.8398C4.43989 9.85898 4.46527 9.87815 4.48727 9.89733L4.5668 9.96666C4.63956 10.0316 4.71401 10.0979 4.79523 10.1584C4.88153 10.2351 4.96782 10.3118 5.05412 10.3871L5.52621 10.7839L5.66158 10.9004C5.7208 10.9535 5.78848 11.014 5.87816 11.0701C6.04568 11.1719 6.2555 11.2264 6.45178 11.2264C6.55669 11.2264 6.65652 11.2117 6.7462 11.1792C6.79866 11.1601 6.84942 11.1379 6.89849 11.1128V15.0131C6.8968 15.1695 6.89511 15.3303 6.95771 15.4778C7.03555 15.6578 7.20645 15.8009 7.33674 15.8717C7.54487 15.9897 7.8156 15.9985 8.0508 15.9985L12.4604 15.997L12.8546 16C13.1575 16 13.501 15.9852 13.7599 15.8141C13.9206 15.7064 14.0476 15.5265 14.0915 15.3436L14.0983 15.2905L14.1034 14.2446V11.1114C14.2303 11.1778 14.386 11.2279 14.5755 11.2279C14.6347 11.2279 14.6956 11.2235 14.7616 11.2132C15.1085 11.1571 15.3335 10.9358 15.4977 10.7736L16.743 9.71441L16.7701 9.68786C16.8446 9.60525 17.0425 9.38841 16.9918 9.07567C16.9918 9.0506 16.9901 9.02404 16.9884 8.99897L16.9715 8.92669C16.8936 8.73639 16.7583 8.61986 16.6483 8.52545L16.5789 8.46497C16.496 8.38678 16.4063 8.31303 16.3166 8.24074C16.2405 8.17879 16.1644 8.11831 16.0848 8.04307L14.122 6.37174C14.0205 6.28323 13.9054 6.18292 13.7447 6.10769C13.6025 6.04278 13.413 6.01918 13.1068 6.01918C13.0509 6.01918 12.7345 6.01328 12.5044 6.01328C12.3893 6.01328 12.2946 6.01475 12.2658 6.01918C12.2218 6.02655 12.1169 6.05311 12.0678 6.06933L11.8309 6.20209C11.7328 6.26553 11.6685 6.34223 11.616 6.40419C11.5873 6.43812 11.5568 6.475 11.5348 6.48975C11.2979 6.65349 11.1338 6.70217 10.7277 6.77298C10.6532 6.78625 10.5889 6.79363 10.5432 6.79363L10.2945 6.78183C10.083 6.7479 9.9104 6.71249 9.75812 6.65791C9.62952 6.61071 9.48738 6.52515 9.42816 6.45877L9.40786 6.43664C9.33509 6.35403 9.25049 6.2611 9.13374 6.18144C9.0559 6.12834 8.86808 6.03835 8.76825 6.02065C8.67687 6.0059 8.51951 6 8.35199 6Z" fill="white" fill-opacity="0.7"/>
                  <path d="M5 4L17 17" stroke="#FBFBFB"/>
                  </svg>
                </div>
              </Tooltip>
            </div>
          </div>
        </div>
      </transition>

      <!-- 壁纸库 -->
      <transition name="fade">
        <div v-show="showButtons" class="icon-button refresh-wallpaper-button" @click="paperModel">
          <Tooltip title="壁纸库" placement="bottom">
            <img v-if="selectedIndex == 0" :src="imageModelCat" alt="收藏" class="wallpaper-image" />
            <img v-else :src="imageModel" alt="收藏" class="wallpaper-image" />
          </Tooltip>
        </div>
      </transition>
      <!-- 搜藏本网站 -->
      <transition name="fade">
        <div v-show="showButtons" class="icon-button refresh-wallpaper-button" @click="selectWebsite">
          <Tooltip title="收藏网站" placement="bottom">
            <img v-if="selectedIndex == 0" :src="selectCat" alt="收藏" class="wallpaper-image" />
            <img v-else :src="select" alt="收藏" class="wallpaper-image" />
          </Tooltip>
        </div>
      </transition>




      <transition name="fade">
        <div v-show="showButtons" class="icon-button refresh-wallpaper-button" @click="openSettingsModal">
          <Tooltip title="设置" placement="bottom">
            <img v-if="selectedIndex == 0" :src="settingCat" alt="设置" class="wallpaper-image" />
            <img v-else :src="setting" alt="设置" class="wallpaper-image" />
          </Tooltip>
        </div>
      </transition>



      <div v-if="isLoggedIn" class="user-profile" @click="toggleUserMenu">
        <div class="avatar">
          <img :src="userInfo.avatar" alt="用户头像" />
        </div>
        <!-- <div class="user-info">
          <span class="user-name">{{ userInfo.name }}</span>
          <span class="user-role">{{ userInfo.role }}</span>
        </div> -->
        <!-- <div class="dropdown-icon" :class="{ 'rotated': isUserMenuOpen }">
          <div class="i-carbon-chevron-down"></div>
        </div> -->

        <div class="user-dropdown" v-if="isUserMenuOpen">
          <div class="dropdown-item" @click="handleLogout">
            <div class="i-carbon-logout"></div>
            <span>退出登录</span>
          </div>
        </div>
      </div>


      <div v-else class="login-button" @click="goToLogin">
        <Tooltip title="登录" placement="bottom">
          <div class="icon-button refresh-wallpaper-button login-icon ">
            <!-- <img :src="login" alt="登录" /> -->
            <img v-if="selectedIndex == 0" :src="loginCat" alt="app" class="wallpaper-image" />
            <img v-else :src="login" alt="app" class="wallpaper-image" />
          </div>

        </Tooltip>
      </div>

    </div>

    <!-- 右上角折叠效果和办公模式切换按钮 - 翻书设计 -->
    <div class="corner-fold" v-if="false" @mouseenter="showOfficeSwitcher = true"
      @mouseleave="showOfficeSwitcher = false">
      <!--  -->
      <!--  -->
      <!-- 折角效果 -->
      <div v-if="showOfficeSwitcher" class="page-corner" @click="switchToOfficeMode">
        <svg v-if="navigationStore.currentDataSource == 'entertainment'" width="25" height="25" viewBox="0 0 36 36"
          fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_610_966)">
            <path
              d="M31.5 9C32.7397 9 33.75 10.0103 33.75 11.25V31.5C33.75 32.7397 32.7397 33.75 31.5 33.75H4.5C3.26025 33.75 2.25 32.7397 2.25 31.5V11.25C2.25 10.0103 3.26025 9 4.5 9H31.5ZM31.5 6.75H4.5C3.30653 6.75 2.16193 7.22411 1.31802 8.06802C0.474106 8.91193 0 10.0565 0 11.25L0 31.5C0 32.6935 0.474106 33.8381 1.31802 34.682C2.16193 35.5259 3.30653 36 4.5 36H31.5C32.6935 36 33.8381 35.5259 34.682 34.682C35.5259 33.8381 36 32.6935 36 31.5V11.25C36 10.0565 35.5259 8.91193 34.682 8.06802C33.8381 7.22411 32.6935 6.75 31.5 6.75Z"
              fill="#585858" />
            <path
              d="M36 19.125C36 19.4234 35.8815 19.7095 35.6705 19.9205C35.4595 20.1315 35.1734 20.25 34.875 20.25H1.125C0.826631 20.25 0.540483 20.1315 0.329505 19.9205C0.118526 19.7095 0 19.4234 0 19.125C0 18.8266 0.118526 18.5405 0.329505 18.3295C0.540483 18.1185 0.826631 18 1.125 18H34.875C35.1734 18 35.4595 18.1185 35.6705 18.3295C35.8815 18.5405 36 18.8266 36 19.125Z"
              fill="#585858" />
            <path
              d="M18 22.5C17.7016 22.5 17.4155 22.3815 17.2045 22.1705C16.9935 21.9595 16.875 21.6734 16.875 21.375V16.875C16.875 16.5766 16.9935 16.2905 17.2045 16.0795C17.4155 15.8685 17.7016 15.75 18 15.75C18.2984 15.75 18.5845 15.8685 18.7955 16.0795C19.0065 16.2905 19.125 16.5766 19.125 16.875V21.375C19.125 21.6734 19.0065 21.9595 18.7955 22.1705C18.5845 22.3815 18.2984 22.5 18 22.5ZM29.25 7.875H27V5.625C27 3.76425 25.4858 2.25 23.625 2.25H12.375C10.5143 2.25 9 3.76425 9 5.625V7.875H6.75V5.625C6.75 2.5245 9.2745 0 12.375 0H23.625C26.7255 0 29.25 2.5245 29.25 5.625V7.875Z"
              fill="#585858" />
          </g>
          <defs>
            <clipPath id="clip0_610_966">
              <rect width="36" height="36" fill="white" />
            </clipPath>
          </defs>
        </svg>

        <svg v-else width="32" height="32" viewBox="0 0 51 51" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M20.8219 23.0876C14.8052 25.5289 11.0146 31.2778 9.33985 35.5567L1.72715 33.5721C1.4384 33.4986 1.16539 33.5249 0.918633 33.6246C0.267616 33.8924 -0.126145 34.6589 0.299117 35.2784L4.85624 41.8621L6.17402 49.7583C6.30002 50.4933 7.11379 50.7768 7.77006 50.5143C8.01157 50.4146 8.23207 50.2413 8.38433 49.9893L12.4637 43.2534C16.6428 45.1592 23.3682 46.6345 29.3849 44.1931C38.5831 40.4603 42.61 28.994 41.7542 26.8834C40.8984 24.7729 30.0149 19.3547 20.8219 23.0876ZM28.6079 42.2768C23.4312 44.3769 17.3201 43.1904 13.3352 41.3738L11.6499 40.6073L10.6891 42.1928L7.81731 46.9285L6.91429 41.5103L6.84079 41.0536L6.57828 40.6756L3.4492 36.1657L8.80959 37.5622L10.6051 38.0295L11.2772 36.3022C12.8732 32.2176 16.417 27.1092 21.5989 25.0039C29.1591 21.9326 38.2996 25.9962 39.7959 27.671C39.8904 29.918 36.1681 39.2055 28.6079 42.2768ZM29.6106 30.1385C28.8126 30.464 28.4294 31.3671 28.7496 32.1651C29.0699 32.9578 29.9834 33.3411 30.7762 33.0156C31.5742 32.6901 31.9574 31.7871 31.6372 30.989C31.3169 30.191 30.4087 29.813 29.6106 30.1385Z"
            fill="#585858" />
          <path
            d="M29.7687 23.9905C25.9991 23.9905 22.9277 20.9244 22.9277 17.1495C22.9277 13.3799 25.9938 10.3086 29.7687 10.3086C33.5435 10.3086 36.6096 13.3799 36.6096 17.1495C36.6096 20.9244 33.5383 23.9905 29.7687 23.9905ZM29.7687 12.4086C27.1541 12.4086 25.0278 14.535 25.0278 17.1495C25.0278 19.7641 27.1541 21.8904 29.7687 21.8904C32.3832 21.8904 34.5095 19.7641 34.5095 17.1495C34.5095 14.535 32.378 12.4086 29.7687 12.4086Z"
            fill="#585858" />
          <path d="M30.8018 10.7409L42.7898 -1.43848L44.2865 0.0347121L32.2985 12.2141L30.8018 10.7409Z"
            fill="#585858" />
          <path d="M34.916 15.4514L51.4361 2.67188L52.7211 4.33302L36.201 17.1126L34.916 15.4514Z" fill="#585858" />
        </svg>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, inject, defineEmits, getCurrentInstance } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useWallpaperStore } from '@/stores/wallpaper'
import { useRouter } from 'vue-router'
import { logout, getUserInfo as fetchUserInfo } from '@/api/auth'
import { Tooltip } from 'ant-design-vue'
import { getWxLoginCode, checkWxLoginStatus } from '@/api/navbar'
import { message } from 'ant-design-vue';
import emitter from '@/utils/mitt';
import { useNavigationStore } from '@/stores/navigation'

import cgwallpaper from '@/assets/header/wallpaper.svg'
import setting from '@/assets/header/setting.svg'
import addweb from '@/assets/header/addweb.svg'
import cgwallpaperCat from '@/assets/header/wallpaperCat.svg'
import settingCat from '@/assets/header/settingCat.svg'
import addwebCat from '@/assets/header/addwebCat.svg'
import login from '@/assets/header/login.svg'
import loginCat from '@/assets/header/loginCat.svg'
import select from '@/assets/header/select.svg'
import selectCat from '@/assets/header/selectCat.svg'
import imageModel from '@/assets/header/imageModel.svg'
import imageModelCat from '@/assets/header/imageModelCat.svg'

const navigationStore = useNavigationStore()

// 使用 computed 属性从 wallpaperStore 获取当前选中的壁纸索引，确保响应式更新
const selectedIndex = computed(() => wallpaperStore.selectedOfficialWallpaper)

// 组件属性
const props = defineProps({
  sideNavCollapsed: {
    type: Boolean,
    default: false
  }
})
const showOfficeSwitcher = ref(false)
const showButtons = ref(true)


function openSettingsModal() {
  emitter.emit('open-settings-modal', { message: 'Hello, mitt!' });
}

// 切换到办公模式 - 优化实现
function switchToOfficeMode() {
  // 关闭折叠区域
  showOfficeSwitcher.value = false

  // 添加过渡动画类
  document.body.classList.add('office-transition')

  // 显示加载状态
  // message.loading({ content: '正在切换到办公模式...', duration: 1 })

  // 延迟执行路由跳转，确保动画有时间显示
  setTimeout(() => {
    // 使用Vue Router导航而不是直接修改URL
    emitter.emit('changeModel', { message: 'noPure' })
    // window.dispatchEvent(new Event('office-mode-changed'));
    document.body.classList.remove('office-transition')
  }, 600)
}

// 从Layout组件注入弹窗全屏状态
const isModalFullscreen = inject('isModalFullscreen', ref(false))

// 获取主题存储和路由
const themeStore = useThemeStore()
const wallpaperStore = useWallpaperStore()
const router = useRouter()

// 登录状态
const isLoggedIn = ref(false)
// 用户信息
const userInfo = ref({
  name: '访客',
  role: '游客',
  avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
})

// 当前时间和日期
const currentTime = ref('')
const currentDate = ref('')
const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月']

// 搜索引擎相关
const isSearchOpen = ref(false)
const searchQuery = ref('')
const searchEngines = [
  { name: '百度', url: 'https://www.baidu.com/s?wd=' },
  { name: '必应', url: 'https://www.bing.com/search?q=' },
  { name: '谷歌', url: 'https://www.google.com/search?q=' },
  { name: '搜狗', url: 'https://www.sogou.com/web?query=' }
]
const selectedEngine = ref(searchEngines[0])

// 个人通知数量
const notificationCount = ref(3)

// 响应式设计
const isMobile = ref(false)
const isUserMenuOpen = ref(false)

// 微信登录相关
const isWxLoginOpen = ref(false)
const qrCodeUrl = ref('')
const isLoading = ref(false)
const loginStatus = ref('waiting') // waiting, scanning, success, error
const wxLoginSessionId = ref('') // 用于轮询的会话ID
const wxLoginWindow = ref(null) // 微信登录窗口引用
const wxWindowCheckInterval = ref(null) // 微信窗口检查定时器
const wxStatusPollInterval = ref(null) // 登录状态轮询定时器
const isPureMode = ref(false)

// 添加ref引用来跟踪壁纸刷新状态
const isRefreshing = ref(false)

// 更新响应式状态
function updateResponsiveState() {
  isMobile.value = window.innerWidth <= 768
}

// 切换用户菜单
function toggleUserMenu() {
  isUserMenuOpen.value = !isUserMenuOpen.value
}

// 打开/关闭搜索
function toggleSearch() {
  isSearchOpen.value = !isSearchOpen.value
  if (isSearchOpen.value) {
    // 下一个微任务队列中聚焦搜索框
    setTimeout(() => {
      document.getElementById('search-input')?.focus()
    }, 0)
  }
}

// 执行搜索
function executeSearch() {
  if (searchQuery.value.trim()) {
    window.open(selectedEngine.value.url + encodeURIComponent(searchQuery.value), '_blank')
    searchQuery.value = ''
    isSearchOpen.value = false
  }
}

function paperModel() {
  // 使用mitt发射打开壁纸弹窗事件
  emitter.emit('open-wallpaper-modal');
}

// 搜藏本网站
function selectWebsite() {
  const url = window.location.href;
  const title = document.title;

  try {
    // IE 浏览器
    window.external.addFavorite(url, title);
  } catch (e) {
    try {
      // Firefox <= v22
      window.sidebar.addPanel(title, url, "");
    } catch (e) {
      // 其他浏览器不支持，提示用户手动操作
      message.warning('您的浏览器不支持自动收藏，请按ctrl+d,cmd+d手动收藏本网站');
    }
  }
}

// 切换搜索引擎
function selectEngine(engine) {
  selectedEngine.value = engine
}

// 处理键盘事件
function handleSearchKeydown(e) {
  if (e.key === 'Enter') {
    executeSearch()
  } else if (e.key === 'Escape') {
    isSearchOpen.value = false
  }
}

// 登出功能
async function handleLogout() {
  try {
    // 调用登出接口
    await logout()

    // 清除本地存储的token和tokenType
    localStorage.removeItem('token')
    localStorage.removeItem('tokenType')
    localStorage.removeItem('userInfo')
    isLoggedIn.value = false
    userInfo.value = {
      name: '访客',
      role: '游客',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
    }

    // 跳转到登录页
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
    // 即使API调用失败，也执行本地登出
    localStorage.removeItem('token')
    localStorage.removeItem('tokenType')
    localStorage.removeItem('userInfo')
    isLoggedIn.value = false
    userInfo.value = {
      name: '访客',
      role: '游客',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
    }
    router.push('/')
  }
}

// 处理token失效
function handleTokenExpired() {
  // 清除本地存储的token和用户信息
  localStorage.removeItem('token')
  localStorage.removeItem('tokenType')
  localStorage.removeItem('userInfo')
  isLoggedIn.value = false
  userInfo.value = {
    name: '访客',
    role: '游客',
    avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
  }

  // 显示token失效提示
  message.warning('登录已过期，请重新登录')

  // 跳转到登录页
  router.push('/')
}

// 打开微信登录弹窗
function openWxLogin() {
  // 重置状态
  loginStatus.value = 'waiting'
  wxLoginSessionId.value = ''
  isWxLoginOpen.value = true

  // 清理可能存在的旧定时器
  if (wxStatusPollInterval.value) {
    clearInterval(wxStatusPollInterval.value)
    wxStatusPollInterval.value = null
  }

  if (wxWindowCheckInterval.value) {
    clearInterval(wxWindowCheckInterval.value)
    wxWindowCheckInterval.value = null
  }

  // 获取二维码
  getWxQrCode()
}

// 获取微信登录二维码
async function getWxQrCode() {
  try {
    isLoading.value = true
    loginStatus.value = 'waiting'
    const res = await getWxLoginCode()

    // 处理后端返回的二维码URL和会话ID
    isLoading.value = false

    // 判断返回格式，确保正确获取URL
    let url = '';

    if (res.data && res.data.qrCodeUrl) {
      url = res.data.qrCodeUrl;
      wxLoginSessionId.value = res.data.sessionId || res.data.qrCodeUrl;
    } else if (res.data && typeof res.data === 'string') {
      url = res.data;
      wxLoginSessionId.value = res.data;
    } else if (res.url) {
      url = res.url;
      wxLoginSessionId.value = res.sessionId || res.url;
    } else if (res.msg) {
      url = res.msg;
      wxLoginSessionId.value = res.sessionId || res.msg;
    } else {
      throw new Error('无效的二维码响应格式');
    }

    // 确保有会话ID才启动轮询
    if (wxLoginSessionId.value) {
      // 先启动轮询，再打开窗口，确保不漏掉状态变化
      startPollingStatus();

      // 在小窗口中打开微信登录
      const width = 600;
      const height = 500;
      wxLoginWindow.value = window.open(
        url,
        'WeChatLogin',
        `width=${width},height=${height},top=${(window.screen.height - height) / 2},left=${(window.screen.width - width) / 2}`
      );

      // 监听窗口关闭
      if (wxLoginWindow.value) {
        wxWindowCheckInterval.value = setInterval(() => {
          if (wxLoginWindow.value && wxLoginWindow.value.closed) {
            clearInterval(wxWindowCheckInterval.value);
            wxWindowCheckInterval.value = null;

            // 仅当状态不是success或error时才检查一次登录状态
            // 避免重复检查已知状态
            if (loginStatus.value !== 'success' && loginStatus.value !== 'error') {
              checkLoginStatusOnce();
            } else {
              // 如果已有明确状态，则停止轮询
              stopAllPolling();
            }
          }
        }, 1000);
      }
    } else {
      throw new Error('未获取到有效的会话ID，无法启动轮询');
    }
  } catch (error) {
    console.error('获取微信二维码失败:', error);
    isLoading.value = false;
    loginStatus.value = 'error';

    // 显示错误提示
    showErrorMessage('获取微信登录二维码失败，请稍后重试');
  }
}

// 停止所有轮询和定时器
function stopAllPolling() {
  if (wxStatusPollInterval.value) {
    clearInterval(wxStatusPollInterval.value);
    wxStatusPollInterval.value = null;
  }

  if (wxWindowCheckInterval.value) {
    clearInterval(wxWindowCheckInterval.value);
    wxWindowCheckInterval.value = null;
  }
}

// 关闭微信登录
function closeWxLogin() {
  isWxLoginOpen.value = false;

  // 关闭微信登录窗口
  if (wxLoginWindow.value && !wxLoginWindow.value.closed) {
    wxLoginWindow.value.close();
    wxLoginWindow.value = null;
  }

  // 重置状态
  // 只有在明确不是成功状态时才重置为waiting
  if (loginStatus.value !== 'success') {
    loginStatus.value = 'waiting';
  }
}

// 轮询检查登录状态
function startPollingStatus() {
  // 先清除可能存在的旧定时器
  if (wxStatusPollInterval.value) {
    clearInterval(wxStatusPollInterval.value);
    wxStatusPollInterval.value = null;
  }



  let pollCount = 0;
  const maxPollCount = 90; // 最多轮询90次(3分钟)

  wxStatusPollInterval.value = setInterval(async () => {
    try {
      // 如果登录窗口已被关闭且整个登录组件也被关闭，则停止轮询
      if (!isWxLoginOpen.value) {

        stopAllPolling();
        return;
      }

      // 如果没有会话ID，停止轮询
      if (!wxLoginSessionId.value) {

        stopAllPolling();
        return;
      }

      // 调用后端接口检查登录状态
      const response = await checkWxLoginStatus(wxLoginSessionId.value);


      // 检查登录是否成功 - 适配多种可能的返回格式
      const isSuccess =
        // 新格式: {"message":"登录成功","status":200, "loginStatus": true}
        (response.status === 200 && response.loginStatus === true) ||
        // 原格式: {code: 200, data: {status: 'success'}}
        (response.code === 200 && response.data && response.data.status === 'success');

      // 登录成功处理
      if (isSuccess) {
        loginStatus.value = 'success';


        // 提取用户数据和token
        // 处理新格式: {"message":"登录成功","status":200, "loginStatus": true, "user": [{...}]}
        if (response.user && Array.isArray(response.user) && response.user.length > 0) {
          const userData = response.user[0];


          // 用openId作为token
          if (userData.openId) {
            localStorage.setItem('token', response.token);
            localStorage.setItem('tokenType', 'token');

          }

          // 构建用户信息
          const userInfoData = {
            name: userData.nickname || '微信用户',
            role: userData.usertype || 'wx_user',
            avatar: userData.avatar || 'https://randomuser.me/api/portraits/men/44.jpg',
            id: userData.id,
            openId: userData.openId,
            userid: userData.userid
          };

          // 保存用户信息
          localStorage.setItem('userInfo', JSON.stringify(userInfoData));
          userInfo.value = userInfoData;

        }
        // 处理原有格式
        else if (response.data && response.data.token) {
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('tokenType', response.data.tokenType || 'Bearer');

          if (response.data.userInfo) {
            localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));
            userInfo.value = response.data.userInfo;
          }
        }

        // 更新登录状态
        isLoggedIn.value = true;


        // 关闭微信窗口
        if (wxLoginWindow.value && !wxLoginWindow.value.closed) {
          wxLoginWindow.value.close();
          wxLoginWindow.value = null;
        }

        // 提示登录成功
        showSuccessToast('登录成功');

        // 触发登录成功事件
        window.dispatchEvent(new Event('login-success'));

        // 停止所有轮询
        stopAllPolling();

        // 强制刷新用户信息
        getUserInfo();
        emitter.emit('login-success', { message: 'Hello, mitt!' });

        return;
      }

      // 检查是否为扫码中状态
      if (response.code === 200 && response.data && response.data.status === 'scanning') {
        loginStatus.value = 'scanning';
      }

      // 轮询次数限制
      pollCount++;
      if (pollCount >= maxPollCount) {
        loginStatus.value = 'error';

        // 关闭微信窗口
        if (wxLoginWindow.value && !wxLoginWindow.value.closed) {
          wxLoginWindow.value.close();
          wxLoginWindow.value = null;
        }

        // 提示登录超时
        showErrorMessage('登录超时，请重试');

        // 停止所有轮询
        stopAllPolling();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      pollCount++;

      // 如果连续失败多次，停止轮询
      if (pollCount >= 5) {
        loginStatus.value = 'error';
        stopAllPolling();
      }
    }
  }, 2000); // 每2秒轮询一次
}

// 显示错误提示
function showErrorMessage(messageText) {
  // 使用ant design vue的消息提示
  message.error(messageText);
}

// 显示成功提示
function showSuccessToast(messageText) {
  // 使用ant design vue的消息提示
  message.success(messageText);
}

// 获取用户信息
function getUserInfo() {
  const token = localStorage.getItem('token')
  isLoggedIn.value = !!token


  if (isLoggedIn.value) {
    // 如果没有存储的用户信息，使用默认信息
    userInfo.value = {
      name: '已登录用户',
      role: '用户',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
    }

    // 尝试从localStorage获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo)

      } catch (e) {
        console.error('解析用户信息失败', e)
      }
    }

    // 如果已登录，尝试从API获取最新的用户信息
    fetchUserInfoFromAPI()
  } else {
    userInfo.value = {
      name: '访客',
      role: '游客',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
    }

  }
}

// 从API获取用户信息
async function fetchUserInfoFromAPI() {
  if (!isLoggedIn.value) return

  try {
    const response = await fetchUserInfo()
    if (response.data) {
      // 更新用户信息
      userInfo.value = response.data
      // 保存到本地存储
      localStorage.setItem('userInfo', JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果是401错误，可能是token过期，执行登出
    if (error.response && error.response.status === 401) {
      handleLogout()
    }
  }
}

// 更新时间的定时器
let timer = null

// 格式化时间为 HH:MM 格式
function formatTime(date) {
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 格式化日期为 MM月DD日 星期x 农历x月x 格式
function formatDate(date) {
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekDay = weekDays[date.getDay()]

  // 这里简化农历计算，实际应用中可以使用专门的农历转换库
  const lunarMonth = lunarMonths[date.getMonth()]
  const lunarDay = day > 20 ? '下旬' : (day > 10 ? '中旬' : '上旬')

  return `${month}月${day}日 ${weekDay} ${lunarMonth}${lunarDay}`
}

// 更新时间
function updateTime() {
  const now = new Date()
  currentTime.value = formatTime(now)
  currentDate.value = formatDate(now)
}

// 跳转到登录页（现在改为打开微信登录窗口）
function goToLogin() {
  openWxLogin()
}

// 切换纯净模式
function togglePureMode() {
  isPureMode.value = !isPureMode.value

  // 保存到本地存储
  localStorage.setItem('isPureMode', isPureMode.value)

  // 触发全局事件，通知其他组件（如Home.vue）切换纯净模式
  window.dispatchEvent(new CustomEvent('toggle-pure-mode', {
    detail: { isPureMode: isPureMode.value }
  }))

  // 添加/移除body全局类，用于控制侧边栏显示
  if (isPureMode.value) {
    document.body.classList.add('pure-mode-active')
  } else {
    document.body.classList.remove('pure-mode-active')
  }

  // 调试信息

}

// 修改refreshWallpaper函数添加动画效果
function refreshWallpaper() {
  isRefreshing.value = true
  wallpaperStore.refreshWallpaperFromRecommended()

  // 2秒后重置动画状态
  setTimeout(() => {
    isRefreshing.value = false
  }, 2000)
}

// 组件挂载时开始计时
onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 60000) // 每分钟更新一次

  // 添加窗口大小调整监听
  updateResponsiveState()
  window.addEventListener('resize', updateResponsiveState)

  // 获取用户信息
  getUserInfo()

  // 监听登录状态变化
  window.addEventListener('login-success', () => {
    getUserInfo()
  })

  // 监听token失效事件
  emitter.on('token-expired', handleTokenExpired)

  // 获取纯净模式设置
  const savedPureMode = localStorage.getItem('isPureMode')
  if (savedPureMode !== null) {
    isPureMode.value = savedPureMode === 'true'

    // 初始化时同步body类
    if (isPureMode.value) {
      document.body.classList.add('pure-mode-active')
    } else {
      document.body.classList.remove('pure-mode-active')
    }
  }

})

// 组件卸载前清除资源
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 清除自动切换按钮隐藏定时器
  if (hideTimeout) {
    clearTimeout(hideTimeout)
    hideTimeout = null
  }

  // 关闭微信登录窗口
  if (wxLoginWindow.value && !wxLoginWindow.value.closed) {
    wxLoginWindow.value.close()
  }

  // 清除所有定时器
  stopAllPolling()

  window.removeEventListener('resize', updateResponsiveState)

  // 移除token失效事件监听器
  emitter.off('token-expired', handleTokenExpired)

  // 正确移除纯净模式变更事件监听器
  if (window._pureModeChangedHandler) {
    window.removeEventListener('pure-mode-changed', window._pureModeChangedHandler)
    delete window._pureModeChangedHandler
  }
})

// 窗口关闭后的单次状态检查
async function checkLoginStatusOnce() {
  try {
    // 如果没有会话ID，不执行检查
    if (!wxLoginSessionId.value) {
      return;
    }


    const response = await checkWxLoginStatus(wxLoginSessionId.value);


    // 检查登录是否成功 - 适配多种返回格式
    const isSuccess =
      // 新格式: {"message":"登录成功","status":200, "loginStatus": true}
      (response.status === 200 && response.loginStatus === true) ||
      // 原格式: {code: 200, data: {status: 'success'}}
      (response.code === 200 && response.data && response.data.status === 'success');

    if (isSuccess) {
      // 登录成功
      loginStatus.value = 'success';


      // 处理新格式: {"message":"登录成功","status":200, "loginStatus": true, "user": [{...}]}
      if (response.user && Array.isArray(response.user) && response.user.length > 0) {
        const userData = response.user[0];


        // 用openId作为token
        if (userData.openId) {
          localStorage.setItem('token', userData.openId);
          localStorage.setItem('tokenType', 'Bearer');

        }

        // 构建用户信息
        const userInfoData = {
          name: userData.nickname || '微信用户',
          role: userData.usertype || 'wx_user',
          avatar: userData.avatar || 'https://randomuser.me/api/portraits/men/44.jpg',
          id: userData.id,
          openId: userData.openId,
          userid: userData.userid
        };

        // 保存用户信息
        localStorage.setItem('userInfo', JSON.stringify(userInfoData));
        userInfo.value = userInfoData;

      }
      // 处理原有格式
      else if (response.data && response.data.token) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('tokenType', response.data.tokenType || 'Bearer');

        if (response.data.userInfo) {
          localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));
          userInfo.value = response.data.userInfo;
        }
      }

      // 更新登录状态
      isLoggedIn.value = true;


      // 提示登录成功
      showSuccessToast('登录成功');

      // 触发登录成功事件
      window.dispatchEvent(new Event('login-success'));

      // 确保停止所有轮询
      stopAllPolling();

      // 强制刷新用户信息
      getUserInfo();
    } else {
      // 关闭窗口且登录未成功，清理所有轮询
      stopAllPolling();
    }
  } catch (error) {
    console.error('最终检查登录状态失败:', error);
    // 出错时也要停止轮询
    stopAllPolling();
  }
}
const emit = defineEmits([
  'close',
  'toggle-edit-mode',
  'add-icon-modal',
  'create-folder',
  'set-app-size',
  'set-open-mode',
  'move-app-to-category',
  'delete-app',
  'add-to-dock',
  'remove-from-dock',
  'edit-icon',
  'change-wallpaper',
  'toggle-pure-mode'
])

function addApp() {
  // 发出事件给父组件
  emit('add-icon-modal')
  emit('close')

  // 移除重复的全局事件触发，避免IconModal重复显示
  // window.dispatchEvent(new CustomEvent('open-add-icon-modal'))
}

// 自动切换壁纸控制按钮
const showAutoSwitchButtons = ref(false)
const isAutoRefreshEnabled = computed(() => wallpaperStore.autoRefreshActive)
console.log(isAutoRefreshEnabled,'isAutoRefreshEnabled')
let hideTimeout = null

// 延时显示自动切换按钮
function showAutoSwitchButtonsWithDelay() {
  if (hideTimeout) {
    clearTimeout(hideTimeout)
    hideTimeout = null
  }
  showAutoSwitchButtons.value = true
}

// 延时隐藏自动切换按钮
function hideAutoSwitchButtonsWithDelay() {
  hideTimeout = setTimeout(() => {
    showAutoSwitchButtons.value = false
    hideTimeout = null
  }, 500) // 500ms延时，提供更宽松的时间窗口
}

function enableAutoSwitch(e) {
  e.stopPropagation()
  wallpaperStore.startAutoRefresh()
  message.success('已开启自动切换壁纸')
}

function disableAutoSwitch(e) {
  e.stopPropagation()
  wallpaperStore.stopAutoRefresh()
  message.success('已关闭自动切换壁纸')
}
</script>


<style lang="scss" scoped>
.header-bar {
  width: max-content;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  //padding: 0 24px;
  padding-right: 20px;
  background-color: transparent !important;
  border-bottom: none;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 100;
  transition: all 0.3s ease;
  box-shadow: none;
  color: #fff;
}

.header-bar.side-nav-collapsed {
  left: 0;
}

.header-bar.lower-z-index {
  z-index: 0 !important;
}

.time-display {
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInCentered {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}


@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 59, 130, 246), 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(var(--accent-color-rgb, 59, 130, 246), 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 59, 130, 246), 0);
  }
}

.current-date {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.3s ease;
  overflow: visible;
  background: rgba(255, 255, 255, 0.14);

}

.icon-button::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  // background-color: var(--hover-bg);
  border-radius: 8px;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.3s ease;
  z-index: -1;
}

.icon-button:hover::before {
  opacity: 1;
  transform: scale(1);
}

.icon-button:hover {
  color: var(--accent-color);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.38);

}

.icon-button:active {
  transform: translateY(0);
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: var(--accent-color);
  color: white;
  font-size: 0.75rem;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 8px;
  padding: 0 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  animation: scaleIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }

  to {
    transform: scale(1);
  }
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 30px;
  transition: all 0.3s ease;
  position: relative;
  // background-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
  // border: 1px solid rgba(var(--accent-color-rgb, 59, 130, 246), 0.2);
}

.user-profile:hover {
  // background-color: rgba(var(--accent-color-rgb, 59, 130, 246), 0.15);
  transform: translateY(-2px);
}

.user-profile:active {
  transform: translateY(0);
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--accent-color);
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(var(--accent-color-rgb, 59, 130, 246), 0.3);
}

.user-profile:hover .avatar {
  transform: scale(1.05);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.user-profile:hover .avatar img {
  filter: brightness(1.1);
}

.user-info {
  display: flex;
  flex-direction: column;
  min-width: 80px;
}

.user-name {
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

.user-role {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.dropdown-icon {
  transition: transform 0.3s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 100;
  overflow: hidden;
  border: 1px solid var(--border-color);
  animation: slideDown 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: top center;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.dropdown-item:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
  padding-left: 20px;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 4px 0;
}

/* 使用蓝色主题时的头部颜色 */
:global(.theme-blue) .header-bar {
  color: var(--text-primary);
  background-color: rgba(224, 231, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-bar {
    right: 0;
    padding: 0 16px;
  }

  .current-date {
    display: none;
  }

  .user-info,
  .dropdown-icon {
    display: none;
  }

  .user-dropdown {
    right: -12px;
  }

  .header-bar.side-nav-collapsed {
    left: 0;
  }

  .user-profile {
    border-radius: 50%;
    padding: 0;
    width: 40px;
    height: 40px;
    justify-content: center;
  }
}

.search-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
  z-index: 100;
  animation: fadeIn 0.2s ease-out;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.search-engines {
  display: flex;
  gap: 8px;
}

.engine-option {
  padding: 6px 12px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.engine-option.active {
  background-color: var(--accent-color);
  color: white;
}

.engine-option:hover:not(.active) {
  background-color: var(--hover-bg);
}

.close-search {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-search:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: 16px;
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 12px;
}

.search-input {
  flex: 1;
  padding: 12px 0;
  background: none;
  border: none;
  outline: none;
  font-size: 16px;
  color: var(--text-primary);
}

.search-button {
  padding: 8px 16px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.search-button:hover {
  background-color: var(--accent-color-hover);
  transform: translateY(-1px);
}

.search-button:active {
  transform: translateY(0);
}

.notification-toggle {
  position: relative;
}

.login-button {
  display: flex;
  align-items: center;
  gap: 8px;
  // padding: 8px 16px;
  color: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.login-button:hover {
  transform: translateY(-2px);
}

.login-button:active {
  transform: translateY(0);
}

.login-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 微信登录弹窗样式 */
.wx-login-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.2s ease-out;
}

.wx-login-container {
  width: 360px;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.wx-login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.wx-login-header h3 {
  margin: 0;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
}

.close-wx-login {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-wx-login:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
}

.wx-login-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-container {
  width: 160px;
  height: 160px;
  margin: 0 auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.qrcode-img {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.qrcode-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 16px;
}

.qrcode-mask p {
  margin: 4px 0;
  color: var(--text-primary);
}

.scanning-icon,
.success-icon,
.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.scanning-icon {
  color: #1aad19;
  /* 微信绿 */
  animation: pulse 1.5s infinite;
}

.success-icon {
  color: #1aad19;
  animation: scaleIn 0.5s;
}

.error-icon {
  color: #ff4d4f;
}

.retry-button {
  margin-top: 8px;
  padding: 6px 12px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: var(--accent-color-hover);
}

.wx-login-tips {
  margin-top: 16px;
  text-align: center;
}

.wx-login-tips p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--text-secondary);
}

/* 微信登录状态样式 */
.qrcode-container.scanning {
  border-color: #1aad19;
}

.qrcode-container.success {
  border-color: #1aad19;
}

.qrcode-container.error {
  border-color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .wx-login-container {
    width: 320px;
  }

  .qrcode-container {
    width: 250px;
    height: 250px;
  }
}

/* 纯净模式按钮样式 */
.pure-mode-button {
  margin-right: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.pure-mode-button:active {
  transform: translateY(0);
}

/* 壁纸刷新按钮样式 */
.refresh-wallpaper-button {
  overflow: visible;
  transition: all 0.3s ease;
  user-select: none;
}

.refresh-wallpaper-button:hover {
  // transform: translateY(-2px) rotate(30deg);
  background: rgba(255, 255, 255, 0.38);
}

.refresh-wallpaper-button:active {
  transform: rotate(180deg);
}

/* 办公模式切换按钮 - 右上角折角设计 */
.corner-fold {
  position: fixed;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  z-index: 1001;
  cursor: pointer;
  overflow: hidden;
}

.page-corner {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: #ffffff;
  filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;

  svg {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.page-corner.show-fold {
  width: 120px;
  height: 80px;
}

.page-corner p {
  color: #333;
  text-align: right;
  padding-right: 10px;
  padding-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: opacity 0.3s ease;
}

.page-corner.show-fold p {
  opacity: 1;
}

.page-corner i {
  font-size: 18px;
  color: #555;
  margin-bottom: 2px;
}

.page-corner span {
  font-size: 12px;
  font-weight: 500;
}

/* 角落提示区域样式 */
.corner-hint-area {
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  /* 增大触发区域 */
  height: 120px;
  /* 增大触发区域 */
  overflow: hidden;
  pointer-events: auto;
  z-index: 1002;
}

/* 小角标样式 */
.corner-angle {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  /* 增大角标大小 */
  height: 80px;
  /* 增大角标大小 */
  background-image: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  transform: rotate(45deg);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9;
}

/* 角落文字提示 */
.corner-text {
  position: absolute;
  top: 15px;
  right: 20px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 10;
  transform: rotate(-10deg);
  background-color: rgba(0, 0, 0, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  animation: blink 2s infinite;
}

@keyframes blink {

  0%,
  100% {
    opacity: 0.8;
  }

  50% {
    opacity: 1;
  }
}

.corner-angle {
  background-image: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  animation: wiggle 2s ease-in-out infinite;
}

/* 为角标添加摆动动画以吸引注意 */
@keyframes wiggle {

  0%,
  100% {
    transform: rotate(45deg);
  }

  85%,
  95% {
    transform: rotate(50deg);
  }

  90% {
    transform: rotate(40deg);
  }
}

.corner-fold:hover .corner-angle {
  animation: pulse-corner 1.5s infinite;
  background-image: linear-gradient(135deg, #1565c0 0%, #64b5f6 100%);
}

@keyframes pulse-corner {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.8);
  }

  70% {
    box-shadow: 0 0 0 15px rgba(25, 118, 210, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
}

.corner-fold-inner {
  position: absolute;
  top: -120px;
  right: -120px;
  width: 240px;
  height: 240px;
  background-image: linear-gradient(135deg, #1976d2 0%, #bbdefb 70%);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  transform: rotate(45deg) scale(0.8);
  opacity: 0;
  transform-origin: bottom right;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 9995;

  [data-theme='dark'] & {
    background-image: linear-gradient(135deg, #1976d2 0%, #263238 70%);
  }
}

.corner-fold-inner.show-fold {
  top: -80px;
  right: -80px;
  transform: rotate(45deg) scale(1);
  opacity: 1;
  pointer-events: auto;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
}

.corner-content {
  position: absolute;
  bottom: 30px;
  right: 30px;
  transform: rotate(-45deg);
  text-align: center;
  white-space: nowrap;
}

.office-switch-btn {
  background: #1565c0;
  border: none;
  padding: 8px 14px;
  color: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.office-switch-btn i {
  font-size: 16px;
}

.office-switch-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  background-color: #0d47a1;
}

/* 添加页面过渡效果 */
:global(body.office-transition) {
  animation: fadeToOffice 0.5s forwards;
}

@keyframes fadeToOffice {
  from {
    opacity: 1;
  }

  to {
    opacity: 1;
  }
}

/* 右上角折角设计 */
.corner-fold {
  position: fixed;
  top: 0;
  right: 0;
  width: 200px;
  /* 增大整体区域 */
  height: 200px;
  /* 增大整体区域 */
  z-index: 1001;
  /* 确保高于HeaderBar */
  pointer-events: auto;
  overflow: hidden;
}

/* 角落提示区域 */
.corner-hint-area {
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  /* 增大触发区域 */
  height: 120px;
  /* 增大触发区域 */
  overflow: hidden;
  pointer-events: auto;
  z-index: 1002;
}

/* 小角标 */
.corner-angle {
  position: absolute;
  top: -40px;
  /* 调整位置 */
  right: -40px;
  /* 调整位置 */
  width: 120px;
  /* 增大角标尺寸 */
  height: 120px;
  /* 增大角标尺寸 */
  background-image: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  transform: rotate(45deg);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9;
  animation: wiggle 3s ease-in-out infinite;
}

@keyframes wiggle {

  0%,
  100% {
    transform: rotate(45deg);
  }

  88%,
  94% {
    transform: rotate(50deg);
  }

  91% {
    transform: rotate(40deg);
  }
}

.corner-fold:hover .corner-angle {
  background-image: linear-gradient(135deg, #1565c0 0%, #64b5f6 100%);
  box-shadow: 0 0 15px rgba(25, 118, 210, 0.8);
}

/* 折叠区域 */
.corner-fold-inner {
  position: absolute;
  top: -140px;
  right: -140px;
  width: 300px;
  /* 增大折叠区域 */
  height: 300px;
  /* 增大折叠区域 */
  background-image: linear-gradient(135deg, #1976d2 0%, #bbdefb 70%);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  transform: rotate(45deg) scale(0.8);
  opacity: 0;
  transform-origin: bottom right;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 9995;
}

.corner-fold-inner.show-fold {
  top: -80px;
  right: -80px;
  transform: rotate(45deg) scale(1);
  opacity: 1;
  pointer-events: auto;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
}

.corner-content {
  position: absolute;
  bottom: 40px;
  /* 调整按钮位置 */
  right: 40px;
  /* 调整按钮位置 */
  transform: rotate(-45deg);
  text-align: center;
  white-space: nowrap;
}

.office-switch-btn {
  background: #1565c0;
  border: none;
  padding: 12px 24px;
  /* 增大内边距 */
  color: white;
  border-radius: 24px;
  /* 增大圆角 */
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 10px;
  /* 增大间距 */
  font-size: 16px;
  /* 增大字体 */
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.4);
  /* 添加边框 */
}

.office-switch-btn i {
  font-size: 20px;
  /* 增大图标 */
}

.office-switch-btn:hover {
  transform: translateY(-3px) scale(1.05);
  /* 增强悬停效果 */
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  background-color: #0d47a1;
}

/* 右上角折角设计 - 简洁版 */
.corner-fold {
  position: fixed;
  top: 0;
  right: 0;
  width: 80px;
  /* 增大鼠标感应区域 */
  height: 80px;
  /* 增大鼠标感应区域 */
  z-index: 1001;
  cursor: pointer;
}

.page-corner {
  position: absolute;
  top: 0;
  right: 0;
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, #EEE 40%, #999 50%, #fff 100%);
  filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
  animation: fold-appear 0.3s ease forwards;
}

@keyframes fold-appear {
  from {
    width: 0;
    height: 0;
  }

  to {
    width: 70px;
    height: 70px;
  }
}

.page-corner p {
  color: #333;
  text-align: right;
  padding-right: 10px;
  padding-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.page-corner i {
  font-size: 18px;
  color: #555;
  margin-bottom: 2px;
}

.page-corner span {
  font-size: 12px;
  font-weight: 500;
}

/* 按钮容器样式 */
.buttons-container {
  display: flex;
  align-items: center;
  gap: 16px;
  /* 恢复按钮之间的间距 */
}

/* fade过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 添加旋转动画样式 */
.rotate-animation {
  animation: rotate-refresh 2s ease-in-out;
}

@keyframes rotate-refresh {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.auto-switch-controls {
  width: 66px;
  // height: 30px;
  position: absolute;
  display: flex;
  gap: 10px;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0px;
  // background: rgba(60, 60, 60, 0.8);
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 10;
  animation: fadeInCentered 0.2s ease;
  background: #00000054;
}


.auto-switch-button {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.auto-switch-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.auto-switch-button.active {
  background: var(--accent-color, #1976d2);
  box-shadow: 0 0 12px rgba(25, 118, 210, 0.5);
}

.switch-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-wallpaper-button {
  position: relative;
}

.wallpaper-button-container {
  position: relative;
  display: inline-block;
}
</style>
<!-- 
有一个新的需求，当鼠标移动到右上角，有一个小小折叠的效果，像翻书一样，然后显示一个切换按钮，当点击切换按钮，就显示办公模式，办公模式的页面暂时弄一个空的，先把这个逻辑做好， -->