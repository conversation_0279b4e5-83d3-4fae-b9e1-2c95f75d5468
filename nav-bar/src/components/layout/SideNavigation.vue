<template>
  <!-- 分离出的圆形固定按钮 -->
  <div class="fixed-logo-container" v-if="!isMobile" :class="{ 'lower-z-index': isModalFullscreen }">
    <Tooltip :title="topInfo" placement="right" color="white" :overlayInnerStyle="{ color: 'black' }" style="color: black;">
      <div class="logo-icon" @click="jumpAndToggleCollapse" @mouseenter="handleMouseEnter" ref="logoIconRef">
        <img v-if="navigationStore.currentDataSource === 'entertainment'" src="../../assets/officialWallpaper/mian-logo.png" alt="logo" style="width: 100%; height: 100%;">
        <img v-else src="../../assets/office/sideBar.png" style="width: 100%; height: 100%;" />
      </div>
    </Tooltip>
  </div>
  
  <!-- 桌面端侧边栏 - 只在非移动端显示 -->
  <aside v-if="!isMobile" :class="['side-navigation', { 'collapsed': isCollapsedLocal, 'lower-z-index': isModalFullscreen }]" style="background-color: unset;" v-bind="$attrs"
         @mouseenter="handleMouseEnter"
         @mouseleave="handleMouseLeave"
         ref="sideNavRef">
    <!-- 导航菜单 -->

    <div class="nav-item" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
        <Tooltip title="首页" placement="right">
          <div class="i-carbon:home nav-icon" @click="navigateTo(0, '首页')"></div>
        </Tooltip>
      </div>
    <div class="nav-menu">
      <div class="nav-indicator-container">
        <div class="nav-indicator" ref="mainNavIndicator"></div>
      </div>
      <ul>

        <li v-for="(item, index) in navItems" 
            :class="{ 'active': isActive(item.path) || item.active }"
            :key="item.path" 
            @click="navigateTo(index, item.type)"
            :ref="el => { if (el) navItemRefs[index] = el }"
            style="color: black;"
            >
          <Tooltip 
            :title="item.name" 
            placement="right"
            :mouseEnterDelay="0.3"
          >
          <!-- {{ item }} -->
            <img :src="item.icon" alt="icon" style="width: 20px; height: 20px">
          </Tooltip>
          <div v-if="item.badge" class="badge-dot"></div>
        </li>
      </ul>
    </div>
    
    <!-- 底部导航和按钮 -->
    <div class="bottom-area" style=" margin-top: auto; padding-bottom: 10px;">
      <!-- 底部导航项 -->
      <div class="bottom-nav">
        <ul>
          <li v-for="(item, bottomIndex) in bottomNavItems" 
              :key="item.path" 
              :class="{ 'active': isActive(item.path) || item.active || item.type === navigationStore.currentCategory }"
              @click="navigateTo(navItems.length + bottomIndex, item.type)"
              :ref="el => { if (el) navItemRefs[navItems.length + bottomIndex] = el }"
              style="cursor: pointer;">
            <Tooltip :title="item.name" placement="right">
              <img :src="item.icon" alt="icon" style="width: 20px; height: 20px">
            </Tooltip>
            <div v-if="item.badge" class="badge-dot"></div>
          </li>
        </ul>
      </div>
    </div>
  </aside>
  
  <!-- 移动端菜单按钮 -->
  <div v-if="isMobile" class="mobile-menu-btn" @click="toggleMobileMenu">
    <div class="i-carbon:menu nav-icon"></div>
  </div>

  
  <!-- 移动端抽屉菜单背景遮罩 -->
  <Transition name="fade">
    <div 
      v-if="isMobile && showMobileDrawer" 
      class="mobile-drawer-backdrop"
      @click="closeMobileMenu"
    ></div>
  </Transition>
  
  <!-- 移动端抽屉菜单 -->
  <Transition name="drawer">
    <aside 
      v-if="isMobile && showMobileDrawer"
      class="mobile-drawer"
    >
      <div class="mobile-drawer-header">
        <div class="drawer-title">导航菜单</div>
        <div class="drawer-close" @click="closeMobileMenu">
          <div class="i-carbon:close nav-icon"></div>
        </div>
      </div>
      
      <div class="mobile-nav-list">
        <div
          v-for="(item, index) in navItems"
          :key="item.path"
          :class="['mobile-nav-item', { 'active': item.type == navigationStore.currentCategory }]"
          @click="navigateToMobile(index, item.type)"
        >
          <div class="nav-icon">
            <img :src="item.icon" alt="icon" style="width: 20px; height: 20px; object-fit: contain;" />
          </div>
          <span class="nav-item-name">{{ item.name }}</span>
        </div>
      </div>
    </aside>
  </Transition>
  
  <!-- 设置弹窗 -->
  <SettingModal 
    v-model="showSettingsModal" 
    @fullscreen-changed="handleFullscreenChanged"
  >
    <!-- 壁纸设置 -->
    <template #wallpaper-settings>
      <div class="setting-group">
        <!-- 壁纸模式选择 -->
        <div class="wallpaper-mode-selector">
          <label>壁纸模式</label>
          <div class="wallpaper-mode-options">
            <div 
              class="wallpaper-mode-option" 
              :class="{ active: wallpaperStore.wallpaperMode === 1 }"
              @click="wallpaperStore.setWallpaperMode(1)"
            >
              <div class="i-carbon:upload"></div>
              <span>自定义上传</span>
            </div>
            <div 
              class="wallpaper-mode-option" 
              :class="{ active: wallpaperStore.wallpaperMode === 2 }"
              @click="wallpaperStore.setWallpaperMode(2)"
            >
              <div class="i-carbon:image"></div>
              <span>推荐壁纸</span>
            </div>
            <div 
              class="wallpaper-mode-option" 
              :class="{ active: wallpaperStore.wallpaperMode === 3 }"
              @click="wallpaperStore.setWallpaperMode(3)"
            >
              <div class="i-carbon:shuffle"></div>
              <span>随机API</span>
            </div>
          </div>
        </div>
        
        <!-- 模式1: 自定义上传 -->
        <div v-if="wallpaperStore.wallpaperMode === 1" class="setting-group">
          <label>上传图片</label>
          <div class="custom-wallpaper-uploader">
            <input
              type="file"
              id="wallpaper-uploader"
              accept="image/*"
              @change="handleWallpaperUpload"
              style="display: none;"
            />
            <button class="upload-button" @click="triggerUpload">
              <div class="i-carbon:cloud-upload"></div>
              <span>选择图片</span>
            </button>

            <div class="upload-tip">
              支持 JPG、PNG 等格式，文件大小不超过 2MB
            </div>

            <div v-if="wallpaperStore.userCustomWallpaper" class="custom-wallpaper-preview">
              <img :src="wallpaperStore.userCustomWallpaper" alt="自定义壁纸预览" />
            </div>
          </div>
        </div>
        
        <!-- 模式2: 官方壁纸 -->
        <div v-if="wallpaperStore.wallpaperMode === 2" class="setting-group">
          <label>选择壁纸</label>
          <div class="official-wallpaper-grid">
            <div
              v-for="(wallpaper, index) in wallpaperStore.officialWallpapers"
              :key="wallpaper.id"
              class="official-wallpaper-item"
              :class="{ active: wallpaperStore.selectedOfficialWallpaper === index }"
              @click="wallpaperStore.selectOfficialWallpaper(index)"
            >
              <div class="wallpaper-thumbnail">
                <img
                  :src="wallpaper.url"
                  :alt="wallpaper.name"
                  class="thumbnail-image"
                  loading="lazy"
                  decoding="async"
                />
              </div>
              <div class="wallpaper-name">{{ wallpaper.name }}</div>
              <div v-if="wallpaperStore.selectedOfficialWallpaper === index" class="selected-indicator">
                <div class="i-carbon:checkmark-outline"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 模式3: 随机API -->
        <div v-if="wallpaperStore.wallpaperMode === 3" class="setting-group">
          <label>壁纸API地址</label>
          <input 
            type="text" 
            v-model="wallpaperStore.wallpaperUrl" 
            placeholder="输入壁纸API地址"
            class="settings-input"
          />
          <div class="setting-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="wallpaperStore.useRandomBg" /> 
              <span>使用随机背景</span>
            </label>
          </div>
          
          <button class="action-button refresh" @click="wallpaperStore.refreshWallpaper">刷新壁纸</button>
        </div>
        
        <!-- 公共设置 - 所有模式下都显示 -->
        <div class="setting-group">
          <label>背景模糊度 ({{ wallpaperStore.blurAmount }}px)</label>
          <input 
            type="range" 
            v-model.number="wallpaperStore.blurAmount" 
            min="0" 
            max="20"
            class="slider"
          />
          <div class="blur-preview-container">
            <img class="blur-preview-bg" 
              :src="getBlurPreviewImage()" 
              alt="背景预览"
            >
            <div class="blur-preview-effect" :style="{
              backdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
              WebkitBackdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
              backgroundColor: wallpaperStore.bgTransparent ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.3)'
            }">
              <div class="blur-text">模糊效果预览</div>
            </div>
          </div>
        </div>
        
        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" v-model="wallpaperStore.bgTransparent" /> 
            <span>移除背景蒙层</span>
          </label>
        </div>
      </div>
    </template>




    <!-- 时间设置 -->
    <template #time-settings>
      <div class="time-settings">
        <!-- 字体设置组 -->
        <div class="setting-group">
          <label>字体选择</label>
          <select v-model="fontFamily" @change="setFontFamily(fontFamily)" class="font-select">
            <option v-for="font in fontOptions" :key="font.value" :value="font.value">
              {{ font.name }}
            </option>
          </select>
        </div>

        <div class="setting-group">
          <label>字体大小 ({{ fontSize }}rem)</label>
          <input
            type="range"
            v-model.number="fontSize"
            min="2"
            max="8"
            step="0.1"
            class="slider"
          />
        </div>

        <div class="setting-group">
          <label>字体厚度 ({{ fontWeight }})</label>
          <input
            type="range"
            v-model.number="fontWeight"
            min="100"
            max="900"
            step="100"
            class="slider"
          />
        </div>

        <div class="setting-group">
          <div class="font-preview-container">
            <div class="font-preview" :style="{
              fontSize: fontSize + 'rem',
              fontWeight: fontWeight,
              fontFamily: fontOptions.find(f => f.value === fontFamily)?.family || 'Orbitron, monospace',
              color: clockColors.find(c => c.value === selectedClockColor)?.hex || '#ffffff'
            }">
              13:45:30
            </div>
          </div>
        </div>

        <div class="setting-group">
          <label>时间格式</label>
          <div class="time-format-options">
            <div class="format-option" :class="{ active: timeFormat === '24h' }" @click="setTimeFormat('24h')">
              <div class="option-preview">13:45</div>
              <div class="option-label">24小时制</div>
            </div>
            <div class="format-option" :class="{ active: timeFormat === '12h' }" @click="setTimeFormat('12h')">
              <div class="option-preview">1:45 PM</div>
              <div class="option-label">12小时制</div>
            </div>
          </div>
        </div>
        
        <div class="setting-group">
          <label>日期格式</label>
          <div class="date-format-options">
            <div class="format-option" :class="{ active: dateFormat === 'full' }" @click="setDateFormat('full')">
              <div class="option-preview">6月15日 星期四 农历六月初六</div>
              <div class="option-label">完整格式</div>
            </div>
            <div class="format-option" :class="{ active: dateFormat === 'short' }" @click="setDateFormat('short')">
              <div class="option-preview">2023/6/15</div>
              <div class="option-label">简洁格式</div>
            </div>
          </div>
        </div>
        
        <div class="setting-group" style="display: flex;flex-direction: row;justify-content: flex-start;">
          <label class="checkbox-label">
            <input type="checkbox" v-model="showSeconds" />
            <span>显示秒数</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="showWeekday" />
            <span>显示星期</span>
          </label>
        </div>

        
        <div class="setting-group">
          <label>时钟样式</label>
          <div class="time-style-options">
            <div class="style-option" :class="{ active: clockStyle === 'digital' }" @click="setClockStyle('digital')">
              <div class="style-preview digital-preview">13:45</div>
              <div class="style-label">数字时钟</div>
            </div>
          </div>
        </div>
        
        <div class="setting-actions">
          <button class="action-button apply" @click="saveTimeSettings">应用</button>
        </div>
      </div>
    </template>

    <!-- 关于我们 -->
    <template #about-settings>
      <div class="about-content">
        <div class="app-info">
          <div class="app-logo">
            <img src="../../assets/officialWallpaper/mian-logo.png" alt="Logo" />
          </div>
          <h3 class="app-name">应用导航系统</h3>
          <div class="app-version">版本：1.0.0</div>
        </div>
        
        <div class="about-section">
          <h4>关于我们</h4>
          <p>这是一个现代化的应用导航系统，用于组织和快速访问您喜爱的应用和网站。提供优雅的界面设计和丰富的自定义选项。</p>
        </div>
        
        <div class="about-section">
          <h4>联系我们</h4>
          <p>如有任何问题或建议，请联系：</p>
          <div class="contact-info">
            <div class="contact-item">
              <div class="i-carbon:email"></div>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <div class="i-carbon:logo-github"></div>
              <span>github.com/example/nav-system</span>
            </div>
          </div>
        </div>
        
        <div class="about-actions">
          <button class="action-button">检查更新</button>
          <button class="action-button">查看文档</button>
        </div>
      </div>
    </template>
  </SettingModal>

  <!-- 友情链接弹窗 -->
  <FriendLinksModal
    v-model:visible="showFriendLinksModal"
    @close="closeFriendLinksModal"
  />

</template>
<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, inject, useAttrs,getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useWallpaperStore } from '@/stores/wallpaper'
import { useNavigationStore } from '@/stores/navigation'
import { useLayoutStore } from '@/stores/layout'
import { Tooltip, message } from 'ant-design-vue'
import SettingModal from '../modal/settingModal.vue'
import FriendLinksModal from '../modal/FriendLinksModal.vue'
import emitter from '@/utils/mitt';
import kindLink from '@/assets/icons/links.svg'


// 获取路由和主题存储
const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()
const wallpaperStore = useWallpaperStore()
const navigationStore = useNavigationStore()
const layoutStore = useLayoutStore()

// 明确定义组件接受的props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  // 明确接受class属性作为prop
  class: {
    type: String,
    default: ''
  }
})




// 组件事件
const emit = defineEmits(['toggle'])

// 从Layout组件注入弹窗全屏状态
const isModalFullscreen = inject('isModalFullscreen', ref(false))

// 响应式的数据源状态
const currentDataSource = ref(localStorage.getItem('currentDataSource') || 'entertainment')

// 将任何额外的attr（包括class）传递到根元素
const attrs = useAttrs()

// 导航栏折叠状态 - 反向使用isOpen
const isCollapsedLocal = ref(!props.isOpen)
// 当前主题
const themes = ref(['light', 'dark', 'blue',])
const currentTheme = ref(localStorage.getItem('theme') || 'light')
const showThemePopup = ref(false)
const topInfo = ref("LinkFun妙趣导航")
// 壁纸设置相关
const showWallpaperPopup = ref(false)

const jumpAndToggleCollapse = () => {
  emitter.emit('changeModel',{ message: '' })
}


// 导航项动画效果
const animationStyles = ref([
  { id: 'bounce', name: '弹跳', value: 'bounce' },
  { id: 'rotate', name: '旋转', value: 'rotate' },
  { id: 'pulse', name: '脉冲', value: 'pulse' },
  { id: 'shake', name: '抖动', value: 'shake' },
  { id: 'swing', name: '摇摆', value: 'swing' }
])

// 应用卡片动画效果
const cardAnimations = ref([
  { id: 'fade-up', name: '淡入上升', value: 'fade-up' },
  { id: 'fade-down', name: '淡入下降', value: 'fade-down' },
  { id: 'fade-left', name: '左侧淡入', value: 'fade-left' },
  { id: 'fade-right', name: '右侧淡入', value: 'fade-right' },
  { id: 'slide-in', name: '滑入', value: 'slide-in' },
  { id: 'zoom-in', name: '缩放', value: 'zoom-in' },
  { id: 'flip', name: '翻转', value: 'flip' },
  { id: 'rotate', name: '旋转', value: 'rotate' },
  { id: 'reveal', name: '揭示', value: 'reveal' },
  { id: 'glitch', name: '故障', value: 'glitch' },
  { id: 'pulse', name: '脉冲', value: 'pulse' }
])

// 当前选中的应用卡片动画效果
const currentCardAnimation = ref(localStorage.getItem('cardAnimation') || 'fade-up')

// 当前选中的动画效果
const currentAnimation = ref(localStorage.getItem('navAnimation') || 'bounce')

// 动画设置弹窗状态
const showAnimationPopup = ref(false)
// 当前选中的动画设置标签页
const activeAnimationTab = ref('icon') // 'icon' 或 'card'

// 新增移动端检测
const isMobile = ref(false)
// 抽屉菜单状态
const showMobileDrawer = ref(false)

// 自动隐藏计时器
const autoHideTimer = ref(null);
// 侧边栏引用
const sideNavRef = ref(null);
// 按钮引用
const logoIconRef = ref(null);
// 是否在侧边栏区域内
const isMouseInNavArea = ref(false);
// 左边缘触发区域宽度（像素）
const LEFT_EDGE_TRIGGER_WIDTH = 75;

const isSidebarFixed = computed(() => layoutStore.isSidebarFixed);

// 实时应用模糊度设置（不关闭弹窗）
function applyWallpaperSettingsPreview() {
  // 只设置模糊度，避免不必要的壁纸刷新
  wallpaperStore.setBlurAmount(wallpaperStore.blurAmount)
}

// 应用壁纸设置并加载新壁纸
function refreshWallpaper() {
  // 只调用store的方法，避免多处实现相同逻辑
  wallpaperStore.refreshWallpaper()
  // message.success('壁纸刷新成功!')
}

// 监听模糊度变化实时应用
watch(() => wallpaperStore.blurAmount, () => {
  applyWallpaperSettingsPreview()
})

// 切换随机背景
function toggleRandomBg() {
  wallpaperStore.toggleRandomBg()
}

// 切换背景透明度
function toggleBgTransparent() {
  wallpaperStore.toggleBgTransparent()

  // 触发一个自定义事件，通知透明背景状态变化
  window.dispatchEvent(new CustomEvent('transparentBgChanged', { 
    detail: { transparent: wallpaperStore.bgTransparent } 
  }))
}

// 初始化读取动画设置
function initAnimationSettings() {
  const savedAnimation = localStorage.getItem('navAnimation')
  if (savedAnimation) {
    currentAnimation.value = savedAnimation
  }
  
  const savedCardAnimation = localStorage.getItem('cardAnimation')
  if (savedCardAnimation) {
    currentCardAnimation.value = savedCardAnimation
  }
}

// 保存动画效果设置
function saveAnimationSettings() {
  localStorage.setItem('navAnimation', currentAnimation.value)
  applyAnimation(currentAnimation.value)
  // closeAnimationPopup()
}

// 保存应用卡片动画效果设置
function saveCardAnimationSettings() {
  localStorage.setItem('cardAnimation', currentCardAnimation.value)
  applyCardAnimation(currentCardAnimation.value)
  // closeAnimationPopup()
}

// 侧边栏动画预览函数
function previewAnimation(animationId) {
  const demoIcon = document.getElementById('animation-demo-icon')
  if (demoIcon) {
    // 先移除所有动画类
    animationStyles.value.forEach(style => {
      demoIcon.classList.remove(`animate-${style.value}`)
    })
    // 应用新的动画类
    demoIcon.classList.add(`animate-${animationId}`)
    
    // 重置动画
    void demoIcon.offsetWidth
    
    // 当前选择的动画
    currentAnimation.value = animationId
  }
}

// 应用卡片动画预览函数
function previewCardAnimation(animationId) {
  const demoCard = document.getElementById('card-animation-demo')
  if (demoCard) {
    // 先移除所有动画类
    cardAnimations.value.forEach(style => {
      demoCard.classList.remove(`card-animate-${style.value}`)
    })
    // 应用新的动画类
    demoCard.classList.add(`card-animate-${animationId}`)
    
    // 重置动画
    void demoCard.offsetWidth
    
    // 当前选择的动画
    currentCardAnimation.value = animationId
  }
}

// 监听外部传入的折叠状态
watch(() => props.isOpen, (newValue) => {
  isCollapsedLocal.value = !newValue
})

watch(isSidebarFixed, (isFixed) => {
  if (isFixed) {
    // 如果设置为固定，则展开侧边栏
    if (isCollapsedLocal.value) {
      toggleCollapse();
    }
  } else {
    // 如果取消固定，则启动自动隐藏计时器
    startAutoHideTimer();
  }
});

// 创建一个ref跟踪首页是否已选中
const homeSelected = ref(false)

// 导航项 - 使用 navigation store 的数据
const navItems = computed(() => {
  const categories = navigationStore.categories
  return categories
    .filter(category => category.type !== 'collectNav') // 排除收藏分类
    .map(category => ({
      name: category.name,
      path: category.type === '首页' ? '/' : `/${category.type}`,
      icon: category.icon,
      type: category.type,
      active: false
    }))
})

  // 底部导航项 - 包含友情链接和收藏分类
const bottomNavItems = computed(() => {
  const categories = navigationStore.categories
  const items = []

  // 添加友情链接项（在收藏上边）
  items.push({
    name: '友情链接',
    path: '/friendLinks',
    icon: kindLink,
    type: 'friendLinks',
    active: false
  })

  // 添加收藏分类
  const collectCategories = categories
    .filter(category => category.type === 'collectNav')
    .map(category => ({
      name: category.name,
      path: `/${category.type}`,
      icon: category.icon,
      type: category.type,
      active: false
    }))

  items.push(...collectCategories)

  return items
})

// 计算当前激活的导航项
const activeItem = computed(() => {
  return [...navItems.value, ...bottomNavItems.value].find(item => 
    route.path === item.path || 
    (item.path !== '/' && route.path.startsWith(item.path))
  )
})

  // 切换导航栏折叠状态
function toggleCollapse() {
  // 检查是否在纯净模式下
  if (document.body.classList.contains('pure-mode-active')) {
    // 在纯净模式下不进行切换
    return
  }
  
  // 在折叠前记录当前激活的索引和状态
  const wasCollapsed = isCollapsedLocal.value
  const activeItemIndex = currentActiveIndex.value
  
  // 备份当前激活状态
  const activeItemBackup = navItems.value.findIndex(item => item.active)
  const activeItemType = activeItemBackup >= 0 ? navItems.value[activeItemBackup].type : null
  
  // 检查当前是否是收藏分类
  const isCollectActive = navigationStore.currentCategory === 'collectNav'
  
  // 修改状态
  isCollapsedLocal.value = !isCollapsedLocal.value
  
  // 通知父组件折叠状态变化，使用相反的值（因为父组件用isOpen，我们用isCollapsed）
  emit('toggle', !isCollapsedLocal.value)
  
  // 保存用户偏好
  localStorage.setItem('navCollapsed', isCollapsedLocal.value.toString())
  
  // 使用双重nextTick确保DOM完全更新后再更新指示器位置和激活状态
  nextTick(() => {
    // 确保只有正确的导航项处于激活状态
    navItems.value.forEach((item, i) => {
      if (activeItemType) {
        // 如果有记录激活类型，使用类型匹配
        item.active = (item.type === activeItemType)
      } else if (activeItemIndex >= 0 && activeItemIndex < navItems.value.length) {
        // 否则使用索引匹配（确保不是底部导航项）
        item.active = (i === activeItemIndex)
      }
    })
    
    // 如果当前是收藏分类，处理底部导航项
    if (isCollectActive) {
      bottomNavItems.value.forEach(item => {
        item.active = (item.type === 'collectNav')
      })
    }
    
    // 强制等待一个较长的延迟，确保DOM完全更新并且CSS过渡效果完成
    setTimeout(() => {
      // 如果是收藏分类，重新移动指示器到底部收藏项
      if (isCollectActive) {
        const collectIndex = navItems.value.length
        // 触发一次强制布局更新，确保能获取到正确的位置信息
        document.body.offsetHeight; // 强制重绘
        // 使用精确的位置定位底部收藏项
        moveIndicator(collectIndex)
      } else {
        // 否则更新普通指示器位置
        updateIndicatorPosition()
      }
    }, 50) // 增加延迟时间，确保DOM已经完全更新
  })
}

// 切换主题
function switchTheme() {
  showThemePopup.value = !showThemePopup.value
}

// 导航到指定路径 - 使用 navigation store
function navigateTo(index, type) {

  // 如果是底部导航项（包括收藏分类）
  if (index >= navItems.value.length) {
    // 重置所有导航项的active状态
    navItems.value.forEach(item => item.active = false)
    bottomNavItems.value.forEach(item => item.active = false)

    // 设置底部导航项为active
    const bottomIndex = index - navItems.value.length
    if (bottomIndex >= 0 && bottomIndex < bottomNavItems.value.length) {
      bottomNavItems.value[bottomIndex].active = true
    }

    // 移动指示器
    if (index !== undefined) {
      moveIndicator(index)
    }
    
    // 获取底部导航项的类型（如'collectNav'或'friendLinks'）
    if (bottomIndex >= 0 && bottomIndex < bottomNavItems.value.length) {
      const categoryType = bottomNavItems.value[bottomIndex].type

      // 如果是友情链接，打开友情链接弹窗
      if (categoryType === 'friendLinks') {
        openFriendLinksModal()
      } else {
        // 切换到相应的分类
        navigationStore.switchCategory(categoryType)
      }
    }

    // 关闭移动端菜单
    closeMobileMenu()
    return
  }

  // 对于普通分类导航，使用 navigation store
  // 先重置所有导航项的active状态
  navItems.value.forEach((item, idx) => {
    // 使用Vue的响应式方法修改active属性
    if (idx === index) {
      // 设置当前点击的为激活状态
      item.active = true
    } else {
      // 其他的设为非激活
      item.active = false
    }
  })
  
  // 同时清除底部导航项的激活状态
  bottomNavItems.value.forEach(item => item.active = false)
  
  navigationStore.switchCategory(type)

  // 移动指示器到该菜单项
  if (index !== undefined) {
    moveIndicator(index)
  }


  // 关闭移动端菜单
  closeMobileMenu()
}

// 检查导航项是否为当前活动项
function isActive(path) {
  // 如果是首页路径，需要做精确匹配
  if (path === '/') {
    return route.path === '/'
  }
  // 其他路径做前缀匹配
  return route.path === path || (path !== '/' && route.path.startsWith(path))
}

// 初始化导航栏状态和主题（从本地存储读取）
function initializeState() {
  // 读取导航栏状态
  const savedCollapsed = localStorage.getItem('navCollapsed')
  if (savedCollapsed !== null) {
    isCollapsedLocal.value = savedCollapsed === 'true'
    // 同步父组件状态
    nextTick(() => {
      emit('toggle', !isCollapsedLocal.value)
    })
  }
  
  // 读取主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    currentTheme.value = savedTheme
    document.documentElement.setAttribute('data-theme', savedTheme)
  }
  
  // 初始化壁纸
  wallpaperStore.initWallpaper()
}

// 计算滑块位置
const getSliderPosition = computed(() => {
  switch(currentTheme.value) {
    case 'light': return '0%';
    case 'dark': return '33.3%';
    case 'blue': return '66.6%';
    default: return '0%';
  }
})

// 导航项引用和指示器引用
const navItemRefs = ref([])
const mainNavIndicator = ref(null)
const bottomNavIndicator = ref(null)
// 添加当前激活的导航索引
const currentActiveIndex = ref(0)

  // 移动指示器到指定索引的菜单项
function moveIndicator(index) {
  if (navItemRefs.value.length === 0) return
  
  // 获取对应的菜单项DOM元素
  const activeItem = navItemRefs.value[index]
  if (!activeItem) return
  
  // 更新当前激活索引
  currentActiveIndex.value = index
  
  // 确定是主导航项还是底部导航项
  const isBottomNavItem = index >= navItems.value.length
  
  // 选择正确的指示器元素
  const indicatorEl = isBottomNavItem ? bottomNavIndicator.value : mainNavIndicator.value
  if (!indicatorEl) return
  
  // 获取菜单项位置信息
  const { offsetTop, offsetHeight } = activeItem
  
  // 切换指示器显示状态
  if (isBottomNavItem) {
    // 显示底部指示器，隐藏主导航指示器
    if (mainNavIndicator.value) mainNavIndicator.value.style.opacity = '0'
    indicatorEl.style.opacity = '1'
  } else {
    // 显示主导航指示器，隐藏底部指示器
    if (bottomNavIndicator.value) bottomNavIndicator.value.style.opacity = '0'
    indicatorEl.style.opacity = '1'
  }
  
  // 根据侧边栏的收缩状态设置指示器位置和大小
  if (isCollapsedLocal.value) {
    if (isBottomNavItem) {
      // 收缩状态下的底部导航指示器使用CSS变量设置位置
      // 修复：使用更精确的位置计算
      indicatorEl.style.setProperty('--translate-y', `${offsetTop}px`)
      indicatorEl.style.height = `${offsetHeight}px`
      
      // 确保位置正确并避免transform导致的问题
      indicatorEl.style.transform = 'translate(-50%, 0)' // 只保留水平居中
      indicatorEl.style.top = `${offsetTop}px` // 直接设置top属性
      indicatorEl.style.left = '50%' // 保持水平居中
    } else {
      // 收缩状态下的主导航指示器
      indicatorEl.style.transform = `translateY(${offsetTop}px)`
      indicatorEl.style.height = `${offsetHeight}px`
    }
  } else {
    // 展开状态下的指示器（所有类型）
    indicatorEl.style.transform = `translateY(${offsetTop}px)`
    indicatorEl.style.height = `${offsetHeight}px`
    
    // 确保收缩状态特有的样式被清除
    if (isBottomNavItem) {
      indicatorEl.style.top = 'auto'
      indicatorEl.style.left = '6px'
    }
  }
  
  // 添加动画类
  indicatorEl.classList.add('animate-indicator')
  
  // 300ms后移除动画类
  setTimeout(() => {
    indicatorEl.classList.remove('animate-indicator')
  }, 300)
  
}

  // 更新指示器位置
function updateIndicatorPosition() {
  
  // 检查收藏分类是否应该激活
  const isCollectActive = navigationStore.currentCategory === 'collectNav'
  
  // 如果收藏分类应该激活
  if (isCollectActive && bottomNavItems.value.length > 0) {
    
    // 清除所有普通导航项的激活状态
    navItems.value.forEach(item => item.active = false)
    
    // 设置收藏分类的激活状态
    bottomNavItems.value.forEach(item => {
      if (item.type === 'collectNav') {
        item.active = true
      }
    })
    
    // 计算正确的索引（普通导航项数量）
    const collectIndex = navItems.value.length
    
    // 移动指示器
    moveIndicator(collectIndex)
    return
  }
  
  // 对于非收藏分类，清除底部导航项的激活状态
  bottomNavItems.value.forEach(item => item.active = false)
  
  // 确保底部指示器隐藏
  if (bottomNavIndicator.value) {
    bottomNavIndicator.value.style.opacity = '0'
  }
  
  // 检查是否有多个active项，如果有则修正
  const activeItems = navItems.value.filter(item => item.active)
  if (activeItems.length > 1) {
    // 保留当前activeIndex对应的项，重置其他项
    navItems.value.forEach((item, i) => {
      item.active = (i === currentActiveIndex.value)
    })
  }
  
  // 首先检查是否有标记为active的导航项
  const activeByFlag = navItems.value.findIndex(item => item.active)
  if (activeByFlag >= 0) {
    moveIndicator(activeByFlag)
    return
  }
  
  // 然后检查当前路径对应的索引
  const activeIndex = navItems.value.findIndex(item => isActive(item.path))
  if (activeIndex >= 0) {
    moveIndicator(activeIndex)
    return
  }
  
  // 查找底部导航项中的活动项（非收藏分类情况）
  if (!isCollectActive) {
    const bottomActiveIndex = bottomNavItems.value.findIndex(item => isActive(item.path))
    if (bottomActiveIndex >= 0) {
      moveIndicator(navItems.value.length + bottomActiveIndex)
      return
    }
  }
  
  // 如果没有找到活动项，但已有记录的激活索引，则使用该索引
  if (currentActiveIndex.value >= 0 && currentActiveIndex.value < navItems.value.length) {
    // 保持当前激活的索引不变，只更新指示器位置
    const activeItem = navItemRefs.value[currentActiveIndex.value]
    if (activeItem) {
      // 根据激活项是否在主导航区域选择正确的指示器
      const isBottomNavItem = currentActiveIndex.value >= navItems.value.length
      const indicatorEl = isBottomNavItem ? bottomNavIndicator.value : mainNavIndicator.value
      
      if (indicatorEl) {
        const { offsetTop, offsetHeight } = activeItem
        indicatorEl.style.transform = `translateY(${offsetTop}px)`
        indicatorEl.style.height = `${offsetHeight}px`
        indicatorEl.style.opacity = '1'
        
        // 隐藏另一个指示器
        if (isBottomNavItem) {
          if (mainNavIndicator.value) mainNavIndicator.value.style.opacity = '0'
        } else {
          if (bottomNavIndicator.value) bottomNavIndicator.value.style.opacity = '0'
        }
        
        // 确保该项被标记为active
        if (isBottomNavItem) {
          const bottomIndex = currentActiveIndex.value - navItems.value.length
          bottomNavItems.value.forEach((item, i) => {
            item.active = (i === bottomIndex)
          })
        } else {
          navItems.value.forEach((item, i) => {
            item.active = (i === currentActiveIndex.value)
          })
        }
      }
    }
    return
  }
  
  // 如果没有活动项，隐藏两个指示器
  if (mainNavIndicator.value) {
    mainNavIndicator.value.style.opacity = '0'
  }
  if (bottomNavIndicator.value) {
    bottomNavIndicator.value.style.opacity = '0'
  }
}

// 监听路由变化，更新指示器位置
watch(() => route.path, () => {
  nextTick(() => {
    updateIndicatorPosition()
  })
})

// 监听折叠状态变化，更新指示器位置
watch(isCollapsedLocal, () => {
  // 增加更长的延迟，确保DOM完全更新后再计算位置
  setTimeout(() => {
    // 检查当前是否是收藏分类
    const isCollectActive = navigationStore.currentCategory === 'collectNav'
    
    if (isCollectActive) {
      // 如果是收藏分类，专门处理底部导航项
      const collectIndex = navItems.value.length
      moveIndicator(collectIndex)
    } else {
      // 否则使用通用的指示器位置更新
      updateIndicatorPosition()
    }
  }, 100) // 增加延迟时间，确保DOM已经完全更新
})

// 监听 navigation store 的分类变化，更新指示器位置和active状态
watch(() => navigationStore.currentCategory, (newCategory) => {
  console.log(`SideNavigation: 监听到分类变化 -> ${newCategory}`)
  
  // 检查是否是收藏分类
  if (newCategory === 'collectNav') {
    // 收藏分类需要特殊处理
    // 清除所有普通导航项的active状态
    navItems.value.forEach(item => {
      item.active = false
    })
    
    // 设置收藏分类的active状态
    bottomNavItems.value.forEach(item => {
      item.active = item.type === 'collectNav'
    })
    
    // 隐藏主导航指示器
    if (mainNavIndicator.value) {
      mainNavIndicator.value.style.opacity = '0'
    }
    
    nextTick(() => {
      // 计算收藏分类的索引
      const collectIndex = navItems.value.length
      // 更新当前激活索引
      currentActiveIndex.value = collectIndex
      // 移动指示器
      moveIndicator(collectIndex)
    })
    return
  }
  
  // 普通分类的处理
  // 首先重置所有导航项的active状态
  navItems.value.forEach(item => {
    item.active = item.type === newCategory
  })
  
  // 重置底部导航项的active状态
  bottomNavItems.value.forEach(item => {
    item.active = false
  })
  
  // 隐藏底部导航指示器
  if (bottomNavIndicator.value) {
    bottomNavIndicator.value.style.opacity = '0'
  }
  
  nextTick(() => {
    // 找到对应的导航项索引
    const activeIndex = navItems.value.findIndex(item => item.type === newCategory)
    if (activeIndex >= 0) {
      // 更新当前激活索引
      currentActiveIndex.value = activeIndex
      moveIndicator(activeIndex)
    }
  })
})

// 监听 navigation store 的分类数据变化，确保导航项更新
watch(() => navigationStore.categories, () => {
  nextTick(() => {
    updateIndicatorPosition()
  })
})

// 应用动画设置并预览效果
function applyAnimation(animationId) {
  currentAnimation.value = animationId
  
  // 保存设置到本地存储
  localStorage.setItem('navAnimation', animationId)
  
  // 触发自定义事件通知Home组件更新动画
  window.dispatchEvent(new CustomEvent('navAnimationChanged', {
    detail: { animation: animationId }
  }))
}

// 应用卡片动画设置并预览效果
function applyCardAnimation(animationId) {
  currentCardAnimation.value = animationId
  
  // 保存设置到本地存储
  localStorage.setItem('cardAnimation', animationId)
  
  // 触发自定义事件通知Home组件更新卡片动画
  window.dispatchEvent(new CustomEvent('cardAnimationChanged', {
    detail: { animation: animationId }
  }))
}

// 设置主题
function setTheme(theme) {
  currentTheme.value = theme
  document.documentElement.setAttribute('data-theme', theme)
  localStorage.setItem('theme', theme)
  
  // 延迟关闭弹窗，让用户看到主题切换效果
  setTimeout(() => {
    closeThemePopup()
  }, 300)
  
  // 发送主题变更事件
  themeStore.setTheme(theme)
}

// 检测设备类型并监听窗口大小变化
function checkDevice() {
  isMobile.value = window.innerWidth <= 768
}

// 切换移动端菜单显示
function toggleMobileMenu() {
  showMobileDrawer.value = !showMobileDrawer.value
  
  // 禁止/恢复body滚动
  if (showMobileDrawer.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 关闭移动端菜单
function closeMobileMenu() {
  if (showMobileDrawer.value) {
    showMobileDrawer.value = false
    document.body.style.overflow = ''
  }
}

// 移动端点击导航项后关闭抽屉
function navigateToMobile(index, type) {
  // 传递类型参数到navigateTo函数
  navigateTo(index, type)
  closeMobileMenu()
}

// 显示主题设置弹窗
function showThemeSettings() {
  showThemePopup.value = true
}

// 关闭主题设置弹窗
function closeThemePopup() {
  showThemePopup.value = false
  }
  
// 主题设置弹窗
const themePopupRef = ref(null)
  
// 点击界面其他地方关闭主题弹窗
function handleClickOutside(event) {
  // 关闭主题设置弹窗
  if (showThemePopup.value && themePopupRef.value) {
    if (!themePopupRef.value.contains(event.target) && !event.target.closest('.theme-button')) {
      showThemePopup.value = false
        }
      }
    }

// 刷新随机背景
function refreshBackground() {
  // 只打开设置面板，不执行刷新
  showWallpaperPopup.value = true
}

// 打开壁纸设置抽屉
function openWallpaperPopup() {
  showAnimationPopup.value = false
  showThemePopup.value = false
  // 使用setTimeout确保CSS转换完全应用
  setTimeout(() => {
    showWallpaperPopup.value = true
  }, 50)
}

// 打开动画设置抽屉
function openAnimationPopup() {
  showWallpaperPopup.value = false
  showThemePopup.value = false
  // 使用setTimeout确保CSS转换完全应用
  setTimeout(() => {
    showAnimationPopup.value = true
  }, 50)
}

// 打开主题设置抽屉
function openThemePopup() {
  showWallpaperPopup.value = false
  showAnimationPopup.value = false
  // 使用setTimeout确保CSS转换完全应用
  setTimeout(() => {
    showThemePopup.value = true
  }, 50)
}

// 关闭壁纸设置抽屉
function closeWallpaperPopup() {
  showWallpaperPopup.value = false
}

// 关闭动画设置抽屉
function closeAnimationPopup() {
  showAnimationPopup.value = false
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 添加鼠标移动监听器
  document.addEventListener('mousemove', checkMouseInNavArea);
  
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    document.documentElement.setAttribute('data-theme', savedTheme)
    currentTheme.value = savedTheme
  }
  
  // 初始化导航
  navigationStore.initNavigation().then(() => {
    
    // 在导航初始化完成后检查当前分类
    if (navigationStore.currentCategory === 'collectNav') {
      // 如果当前是收藏分类，更新指示器位置
      setTimeout(() => {
        updateIndicatorPosition()
      }, 200)
    }
  })
  
  // 初始化状态
  initializeState()
  
  // 初始化动画设置
  initAnimationSettings()
  
  // 初始化导航项激活状态
  // 首先检查当前路径是否匹配某个导航项
  const currentPathIndex = navItems.value.findIndex(item => isActive(item.path))
  
  if (currentPathIndex >= 0) {
    // 当前路径匹配到某个导航项，将其设置为激活状态
    navItems.value.forEach((item, i) => {
      item.active = (i === currentPathIndex)
    })
    currentActiveIndex.value = currentPathIndex
  } else {
    // 如果没有匹配项，则激活首页
    if (navItems.value.length > 0 && navItems.value[0].path === '/') {
      navItems.value.forEach((item, i) => {
        item.active = (i === 0)
      })
      homeSelected.value = true
      currentActiveIndex.value = 0
    }
  }
  
  // 初始更新指示器位置 - 使用较长的延迟确保DOM完全渲染
  setTimeout(() => {
    // 检查当前分类是否为收藏
    if (navigationStore.currentCategory === 'collectNav') {
      // 如果是收藏分类，移动指示器到收藏项
      const collectIndex = navItems.value.length
      moveIndicator(collectIndex)
    } else {
      // 根据当前激活索引更新指示器
      if (currentActiveIndex.value >= 0 && currentActiveIndex.value < navItemRefs.value.length) {
        moveIndicator(currentActiveIndex.value)
      } else if (navItems.value.length > 0) {
        // 如果没有激活索引，默认选择第一项
        moveIndicator(0)
      }
    }
  }, 200)
  
  // 监听Home组件分类变化事件
  window.addEventListener('categoryChanged', (event) => {
    if (event.detail && event.detail.path) {
      const path = event.detail.path
      const navIndex = navItems.value.findIndex(item => item.path === path)
      if (navIndex !== -1) {
        moveIndicator(navIndex)
        // 如果不是在路由系统控制下，手动更新路由路径
        if (route.path !== path) {
          router.push(path)
        }
      }
    }
  })
  
  // 检测设备类型
  checkDevice()
  window.addEventListener('resize', checkDevice)
  
  // 不再需要监听菜单分类变化事件，直接使用 navigation store
  
  // 监听壁纸面板打开事件
  window.addEventListener('open-wallpaper-panel', handleOpenWallpaperPanel)

  // 监听滚动切换分类事件
  emitter.on('scroll-category-switch', handleScrollCategorySwitch)



  nextTick(() => {
    moveIndicator(0)
  })
  // moveIndicator(0)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  // 移除鼠标移动监听器
  document.removeEventListener('mousemove', checkMouseInNavArea);
  // 移除窗口大小变化监听
  window.removeEventListener('resize', checkDevice)
  // 不再需要移除 menuCategoriesChanged 监听器
  window.removeEventListener('open-wallpaper-panel', handleOpenWallpaperPanel)
  // 移除滚动切换分类事件监听器
  emitter.off('scroll-category-switch', handleScrollCategorySwitch)

  document.body.classList.remove('modal-fullscreen-active')
  if (autoHideTimer.value) {
    clearTimeout(autoHideTimer.value);
  }
})

// 不再需要处理菜单分类变化事件，直接使用 navigation store 的响应式数据

// 获取分类的图标
function getCategoryIcon(category) {
  // 根据分类名称返回相应的图标
  const iconMap = {
    '应用': 'i-carbon:application',
    '小组件': 'i-carbon:widgets',
  }
  
  return iconMap[category] || 'i-carbon:application'
}

// 获取模糊效果预览图片
function getBlurPreviewImage() {
  switch(wallpaperStore.wallpaperMode) {
    case 1: // 自定义上传
      return wallpaperStore.userCustomWallpaper || 'https://bing.img.run/rand_uhd.php';
    case 2: // 官方壁纸
      if (wallpaperStore.officialWallpapers.length > 0 && 
          wallpaperStore.selectedOfficialWallpaper < wallpaperStore.officialWallpapers.length) {
        return wallpaperStore.officialWallpapers[wallpaperStore.selectedOfficialWallpaper].url;
      }
      return 'https://bing.img.run/rand_uhd.php';
    case 3: // 随机API
    default:
      return 'https://bing.img.run/rand_uhd.php';
  }
}

// 处理壁纸上传
function handleWallpaperUpload(event) {
  const file = event.target.files[0]
  if (file) {
    // 检查文件大小限制（2MB）
    const maxSize = 2 * 1024 * 1024 // 2MB in bytes
    if (file.size > maxSize) {
      message.error('图片文件过大，请选择小于 2MB 的图片文件')
      // 清空文件输入
      event.target.value = ''
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      // 更新自定义壁纸URL
      wallpaperStore.setCustomWallpaper(e.target.result)

      // 确保切换到自定义壁纸模式
      if (wallpaperStore.wallpaperMode !== 1) {
        wallpaperStore.setWallpaperMode(1)
      }

      // 清空文件输入，确保可以重复上传同一文件
      event.target.value = ''
    }
    reader.readAsDataURL(file)
  }
}

// 触发上传按钮
function triggerUpload() {
  document.getElementById('wallpaper-uploader').click()
}

// 处理打开壁纸面板事件
function handleOpenWallpaperPanel() {
  openWallpaperPopup()
}



// 设置弹窗状态
const showSettingsModal = ref(false);
const isSettingsFullscreen = ref(false);

// 友情链接弹窗状态
const showFriendLinksModal = ref(false);

emitter.on('open-friend-links-modal', () => {
  openFriendLinksModal()
  closeSettingsModal()
});


// 打开友情链接弹窗
function openFriendLinksModal() {
  showFriendLinksModal.value = true;
}

// 关闭友情链接弹窗
function closeFriendLinksModal() {
  showFriendLinksModal.value = false;
}

// 处理设置弹窗全屏状态变化
function handleFullscreenChanged(isFullscreen) {
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
    detail: { isFullscreen }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
}

// 打开设置弹窗
function openSettingsModal() {
  showSettingsModal.value = true;
}

emitter.on('open-settings-modal', (data) => {
  openSettingsModal()
});


// 关闭设置弹窗
function closeSettingsModal() {
  showSettingsModal.value = false;
  
  // 如果处于全屏状态，通知退出全屏
  if (isSettingsFullscreen.value) {
    isSettingsFullscreen.value = false;
    window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
      detail: { isFullscreen: false }
    }));
  }
}

// 切换设置弹窗全屏状态
function toggleSettingsFullscreen() {
  isSettingsFullscreen.value = !isSettingsFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('app-modal-fullscreen', {
    detail: { isFullscreen: isSettingsFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isSettingsFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
}

// 组件卸载时确保不残留全屏类
onUnmounted(() => {
  document.body.classList.remove('modal-fullscreen-active');
});

// 时间设置相关状态
const timeFormat = ref(localStorage.getItem('timeFormat') || '24h')
const dateFormat = ref(localStorage.getItem('dateFormat') || 'full')
const showSeconds = ref(localStorage.getItem('showSeconds') === 'true')
const showWeekday = ref(localStorage.getItem('showWeekday') !== 'false')
const clockStyle = ref(localStorage.getItem('clockStyle') || 'digital')
const selectedClockColor = ref(localStorage.getItem('clockColor') || 'white')
const fontSize = ref(parseFloat(localStorage.getItem('fontSize')) || 4.3)
const fontWeight = ref(parseInt(localStorage.getItem('fontWeight')) || 700)
const fontFamily = ref(localStorage.getItem('fontFamily') || 'OrbitrDINon')

// 时钟颜色选项
const clockColors = [
  { name: '白色', value: 'white', hex: '#ffffff' },
  { name: '蓝色', value: 'blue', hex: '#3b82f6' },
  { name: '绿色', value: 'green', hex: '#10b981' },
  { name: '紫色', value: 'purple', hex: '#8b5cf6' },
  { name: '粉色', value: 'pink', hex: '#ec4899' },
  { name: '橙色', value: 'orange', hex: '#f97316' }
]

// 字体选项
const fontOptions = [
  { name: '推荐字体', value: 'DIN', family: 'DIN'},
  { name: '数字字体', value: 'Orbitron', family: "'Orbitron', monospace" },
  { name: '等宽字体', value: 'Roboto Mono', family: "'Roboto Mono', monospace" },
  { name: '系统字体', value: 'Arial', family: "Arial, sans-serif" },
  { name: '优雅字体', value: 'Georgia', family: "Georgia, serif" },
  { name: '现代字体', value: 'Helvetica', family: "Helvetica, Arial, sans-serif" },
  { name: '代码字体', value: 'Fira Code', family: "'Fira Code', monospace" }
]

// 设置时间格式
function setTimeFormat(format) {
  timeFormat.value = format
}

// 设置日期格式
function setDateFormat(format) {
  dateFormat.value = format
}

// 设置时钟样式
function setClockStyle(style) {
  clockStyle.value = style
}

// 设置时钟颜色
function setClockColor(color) {
  selectedClockColor.value = color
}

// 设置字体大小
function setFontSize(size) {
  fontSize.value = size
}

// 设置字体厚度
function setFontWeight(weight) {
  fontWeight.value = weight
}

// 设置字体
function setFontFamily(font) {
  fontFamily.value = font
}

// 监听字体设置变化，实现实时预览
watch([fontFamily, fontSize, fontWeight], () => {
  saveTimeSettings()
}, { immediate: false })

// 保存时间设置
function saveTimeSettings() {
  // 保存到本地存储
  localStorage.setItem('timeFormat', timeFormat.value)
  localStorage.setItem('dateFormat', dateFormat.value)
  localStorage.setItem('showSeconds', showSeconds.value.toString())
  localStorage.setItem('showWeekday', showWeekday.value.toString())
  localStorage.setItem('clockStyle', clockStyle.value)
  localStorage.setItem('clockColor', selectedClockColor.value)
  localStorage.setItem('fontSize', fontSize.value.toString())
  localStorage.setItem('fontWeight', fontWeight.value.toString())
  localStorage.setItem('fontFamily', fontFamily.value)

  // 触发自定义事件通知时间组件更新
  window.dispatchEvent(new CustomEvent('timeSettingsChanged', {
    detail: {
      timeFormat: timeFormat.value,
      dateFormat: dateFormat.value,
      showSeconds: showSeconds.value,
      showWeekday: showWeekday.value,
      clockStyle: clockStyle.value,
      clockColor: selectedClockColor.value,
      fontSize: fontSize.value,
      fontWeight: fontWeight.value,
      fontFamily: fontFamily.value
    }
  }))
  

  
  // 关闭设置弹窗
  // showSettingsModal.value = false
}

// 将任何额外的attr（包括class）传递到根元素

// 处理鼠标进入侧边栏
function handleMouseEnter() {
  if (isSidebarFixed.value) return;
  // 清除自动隐藏计时器
  if (autoHideTimer.value) {
    clearTimeout(autoHideTimer.value);
    autoHideTimer.value = null;
  }
  
  isMouseInNavArea.value = true;
  
  // 如果侧边栏是折叠状态，展开它
  if (isCollapsedLocal.value) {
    toggleCollapse();
  }
}

// 处理鼠标离开侧边栏
function handleMouseLeave() {
  if (isSidebarFixed.value) return;
  isMouseInNavArea.value = false;

  // 如果侧边栏是展开状态，设置计时器5秒后自动折叠
  if (!isCollapsedLocal.value) {
    startAutoHideTimer();
  }
}

// 处理滚动切换分类事件
function handleScrollCategorySwitch() {
  // 调用 handleMouseEnter 方法
  handleMouseEnter();
}

// 启动自动隐藏计时器
function startAutoHideTimer() {
  if (isSidebarFixed.value) return;
  // 先清除之前的计时器
  if (autoHideTimer.value) {
    clearTimeout(autoHideTimer.value);
  }
  
  autoHideTimer.value = setTimeout(() => {
    // 再次检查此时鼠标是否确实在区域外
    if (!isMouseInNavArea.value && !isCollapsedLocal.value) {
      // 检查是否在纯净模式下
      if (!document.body.classList.contains('pure-mode-active')) {
        // 折叠侧边栏
        isCollapsedLocal.value = true;
        emit('toggle', !isCollapsedLocal.value);
        localStorage.setItem('navCollapsed', 'true');
        
        // 更新指示器位置
        nextTick(() => {
          updateIndicatorPosition();
        });
        
        
      }
    }
  }, 5000); // 5秒后自动折叠
}

// 检查鼠标是否在侧边栏区域
function checkMouseInNavArea(e) {
  if (isSidebarFixed.value) return;
  if (!sideNavRef.value && !logoIconRef.value) return;

  // 获取侧边栏和Logo按钮的位置信息
  const navRect = sideNavRef.value?.getBoundingClientRect();
  const logoRect = logoIconRef.value?.getBoundingClientRect();

  // 检查鼠标是否在侧边栏或Logo区域内
  const inNavArea = navRect &&
    e.clientX >= navRect.left &&
    e.clientX <= navRect.right &&
    e.clientY >= navRect.top &&
    e.clientY <= navRect.bottom;

  const inLogoArea = logoRect &&
    e.clientX >= logoRect.left &&
    e.clientX <= logoRect.right &&
    e.clientY >= logoRect.top &&
    e.clientY <= logoRect.bottom;

  // 检查鼠标是否在屏幕左边缘触发区域内
  const inLeftEdge = e.clientX <= LEFT_EDGE_TRIGGER_WIDTH;

  const wasInArea = isMouseInNavArea.value;
  isMouseInNavArea.value = inNavArea || inLogoArea || inLeftEdge;
  
  // 如果鼠标状态发生变化
  if (wasInArea !== isMouseInNavArea.value) {
    if (isMouseInNavArea.value) {
      // 鼠标进入区域
      if (autoHideTimer.value) {
        clearTimeout(autoHideTimer.value);
        autoHideTimer.value = null;
      }
      
      // 如果侧边栏是折叠状态，展开它
      if (isCollapsedLocal.value) {
        toggleCollapse();
      }
    } else {
      // 鼠标离开区域，启动自动隐藏
      if (!isCollapsedLocal.value) {
        startAutoHideTimer();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.side-navigation {
  position: fixed;
  top: 80px;
  left: 25px;
  bottom: 0;
  // z-index: 100; /* 高于主内容区域 */
  transition: z-index 0.01s ease;
  height: auto;
  max-height: calc(100vh - 140px); /* 留出顶部和底部的空间 */
  width: 50px;
  color: #fff;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  // z-index: 100;
  overflow: hidden; /* 超出部分隐藏 */
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡动画 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 10px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 1;
  // transform: translateY(0);
  background: rgba(255, 255, 255, 0.6) !important;
  background: #ffffff24;
  border-radius: 30px;

}

.side-navigation.collapsed {
  max-height: 0; /* 完全收起 */
  padding: 0;
  border: none;
  box-shadow: none;
  margin-top: 10px;
  opacity: 0;
  transform: translateY(-20px); /* 向上收起 */
  pointer-events: none; /* 收起时不响应交互 */
}

.fixed-logo-container {
  position: fixed;
  top: 20px;
  left: 25px;
  z-index: 1; /* 高于侧边栏 */
  transition: z-index 0.01s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px; 
  width: 50px;
  height: 50px;
  border-radius: 50%; /* 圆形按钮 */
  //background: linear-gradient(135deg, rgba(70, 130, 220, 0.9) 0%, rgba(100, 160, 250, 0.8) 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  // box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.logo-icon .nav-icon {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  width: 24px;
  height: 24px;
}

.logo-icon:hover {
  // box-shadow: 0 6px 20px rgba(70, 130, 220, 0.5);
  transform: translateY(-2px);
}

.logo-icon:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.nav-menu {
  flex: 0 0 auto;
  padding: 8px 0;
  overflow-y: visible;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.nav-indicator-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  display: block; /* 显示指示器容器 */
  padding: 0 5px; /* 添加内边距使指示器不贴边 */
}

.nav-indicator {
  position: absolute;
  top: 11px; /* 顶部导航中的默认位置 */
  left: 6px;
  right: 6px;
  width: calc(100% - 12px); /* 考虑左右间距 */
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), height 0.3s ease, opacity 0.3s ease;
  opacity: 0; /* 默认隐藏，根据需要显示 */
  z-index: 0;
  height: 30px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: block; /* 显示指示器 */
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
}

/* 底部导航的指示器容器样式 */
.bottom-nav .nav-indicator-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}

/* 底部导航的指示器样式 */
.bottom-nav .nav-indicator {
  top: 8px; /* 调整底部指示器的默认位置 */
}

/* 侧边栏收缩状态下调整底部指示器的位置 */
.side-navigation.collapsed .bottom-nav .nav-indicator {
  position: absolute;
  left: 50%;
  top: var(--translate-y, 0) !important; /* 使用CSS变量控制垂直位置 */
  transform: translateX(-50%) !important; /* 只做水平居中 */
  width: 40px !important; /* 与收缩状态下的菜单项宽度相同 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: top 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), height 0.3s ease, opacity 0.3s ease;
  /* 确保折叠状态下指示器在z轴上叠放正确 */
  z-index: 0;
}

.animate-indicator {
  animation: pulse-indicator 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulse-indicator {
  0% { background-color: rgba(255, 255, 255, 0.15); border-color: rgba(255, 255, 255, 0.2); }
  50% { background-color: rgba(255, 255, 255, 0.25); border-color: rgba(255, 255, 255, 0.4); }
  100% { background-color: rgba(255, 255, 255, 0.15); border-color: rgba(255, 255, 255, 0.2); }
}

.nav-menu ul,
.bottom-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-menu li,
.bottom-nav li {
  // display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  margin: 2px 0;
  color: rgba(255, 255, 255, 0.7);
  width: 100%;
  /* height: 40px; */
  z-index: 1;
}

.nav-menu li:hover,
.bottom-nav li:hover {
  color: white;
}

.nav-menu li.active,
.bottom-nav li.active {
  color: white;
  font-weight: 500;
  background-color: transparent;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.nav-icon {
  width: 24px;
  height: 24px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

.nav-menu li:hover .nav-icon,
.bottom-nav li:hover .nav-icon {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 5px rgba(0, 0, 0, 0.15));
}

.nav-menu li.active .nav-icon,
.bottom-nav li.active .nav-icon {
  filter: drop-shadow(0 2px 5px rgba(255, 255, 255, 0.5));
  transform: scale(1.1);
  animation: glow 2s infinite alternate;
}

@keyframes glow {
  from { filter: drop-shadow(0 2px 5px rgba(255, 255, 255, 0.3)); transform: scale(1.05); }
  to { filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.7)); transform: scale(1.15); }
}

/* 为激活状态的菜单项添加特效 */
.nav-menu li.active::after,
.bottom-nav li.active::after {
  // content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  left: 50%;
  bottom: 2px;
  transform: translateX(-50%);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

.bottom-area {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  padding-top: 10px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: black;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  padding: 10px 0;
  width: 100%;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  span{
    color: black;
  }
}

.nav-item::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.6s ease, opacity 0.3s ease;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
  // color: white;
  transform: translateY(-2px);
}

.nav-item:hover::before {
  transform: translateX(100%);
  opacity: 1;
}

.nav-item:active {
  transform: translateY(0);
}

.nav-item .nav-icon {
  margin-bottom: 6px;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-item span {
  font-size: 12px;
  color: black;
  display: block;
  text-align: center;
  line-height: 1.2;
}

.bottom-nav {
  padding: 5px 0;
  margin-top: 5px;
  width: 100%;
  
  ul {
    width: 100%;
  }
  
  li {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    cursor: pointer;
    position: relative;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    margin: 2px 0;
    color: rgba(255, 255, 255, 0.7);
    z-index: 1;
  }
}

.badge-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f44336;
  box-shadow: 0 0 0 2px rgba(25, 55, 120, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

/* 抽屉动画效果 - 修复版 */
.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  z-index: 9999;
}

.drawer-container {
  position: fixed;
  height: 100%;
  width: 380px;
  background: rgba(30, 30, 40, 0.95);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  right: 0;
  top: 0;
  transform: translateX(100%);
  transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform;
  z-index: 10000;
}

.drawer-container.drawer-open {
  transform: translateX(0) !important;
}

.drawer-fade-enter-active {
  transition: opacity 0.3s ease;
}

.drawer-fade-leave-active {
  transition: opacity 0.3s ease;
}

.drawer-fade-enter-from,
.drawer-fade-leave-to {
  opacity: 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drawer-header h4 {
  margin: 0;
  font-size: 18px;
  color: #fff;
  font-weight: 500;
}

.close-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 抽屉内容进入动画 */
.drawer-content > * {
  animation: contentFadeIn 0.5s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes contentFadeIn {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标签页按钮进入动画 */
.animation-tabs .tab-button:nth-child(1) {
  animation: slideInUp 0.4s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: translateY(20px);
}

.animation-tabs .tab-button:nth-child(2) {
  animation: slideInUp 0.4s ease forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 动画选项进入动画 */
.animation-option {
  animation: optionFadeIn 0.4s ease forwards;
  opacity: 0;
  transform: scale(0.9);
}

.animation-option:nth-child(1) { animation-delay: 0.3s; }
.animation-option:nth-child(2) { animation-delay: 0.35s; }
.animation-option:nth-child(3) { animation-delay: 0.4s; }
.animation-option:nth-child(4) { animation-delay: 0.45s; }
.animation-option:nth-child(5) { animation-delay: 0.5s; }

@keyframes optionFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 滑入动画 */
@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.drawer-fade-enter-active {
  animation: none;
}

.drawer-fade-leave-active {
  animation: none;
}

.drawer-fade-enter-active .drawer-container {
  animation: none;
}

.drawer-fade-leave-active .drawer-container {
  animation: none;
}

/* 壁纸设置样式改进 */
.wallpaper-settings {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group label {
  color: #fff;
  font-size: 14px;
}

.settings-input {
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  outline: none;
  transition: all 0.2s;
  color: black; 
}

.settings-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border: none;
}

/* 改进后的模糊效果预览 */
.blur-preview-container {
  position: relative;
  margin-top: 10px;
  height: 80px;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.blur-preview-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blur-preview-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blur-text {
  position: relative;
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 为背景添加更强的对比度 */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px 0;
}

.checkbox-label input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: var(--accent-color);
}

.checkbox-label span {
  font-size: 15px;
  color: #fff;
  font-weight: 500;
}

.setting-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: flex-end;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.action-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-button:hover::after {
  transform: translateX(100%);
}

.action-button:active {
  transform: translateY(-1px);
}

.action-button.cancel {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.action-button.refresh,
.action-button.apply {
  background: linear-gradient(135deg, rgba(60, 180, 120, 0.8), rgba(40, 160, 100, 0.8));
  color: #fff;
}

/* 主题选择样式优化 */
.theme-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 20px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  position: relative;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.2s;
}

.theme-option:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.theme-option.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.theme-icon {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.active-check {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-preview {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  margin-top: 12px;
}

/* 主题UI预览 */
.theme-ui-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 20px 1fr;
  grid-template-rows: 20px 1fr;
  gap: 4px;
  padding: 4px;
  box-sizing: border-box;
}

.preview-header {
  grid-column: 1 / 3;
  border-radius: 2px;
}

.preview-sidebar {
  grid-row: 2 / 3;
  border-radius: 2px;
}

.preview-content {
  border-radius: 2px;
}

/* 各主题的演示样式 */
.theme-preview.light .preview-header {
  background-color: #f8f9fa;
}
.theme-preview.light .preview-sidebar {
  background-color: #e9ecef;
}
.theme-preview.light .preview-content {
  background-color: #ffffff;
}

.theme-preview.dark .preview-header {
  background-color: #1f2937;
}
.theme-preview.dark .preview-sidebar {
  background-color: #111827;
}
.theme-preview.dark .preview-content {
  background-color: #374151;
}

.theme-preview.blue .preview-header {
  background-color: #93c5fd;
}
.theme-preview.blue .preview-sidebar {
  background-color: #3b82f6;
}
.theme-preview.blue .preview-content {
  background-color: #dbeafe;
}

.theme-preview.green .preview-header {
  background-color: #6ee7b7;
}
.theme-preview.green .preview-sidebar {
  background-color: #10b981;
}
.theme-preview.green .preview-content {
  background-color: #d1fae5;
}

.theme-preview.purple .preview-header {
  background-color: #c4b5fd;
}
.theme-preview.purple .preview-sidebar {
  background-color: #8b5cf6;
}
.theme-preview.purple .preview-content {
  background-color: #ede9fe;
}

.theme-option span {
  font-size: 14px;
  color: #fff;
  text-transform: capitalize;
}

/* 使用深度选择器确保弹窗正确显示 */
:deep(.drawer-backdrop) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

/* 动画设置抽屉样式 */
.animation-settings {
  padding: 15px;
}

.animation-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.animation-preview {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

#animation-demo-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.preview-label {
  font-size: 14px;
  color: #333;
}

.animation-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.animation-option {
  padding: 12px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(var(--accent-color-rgb), 0.3);
    box-shadow: 0 4px 10px rgba(var(--accent-color-rgb), 0.2);
  }
  
  .option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .option-icon {
    margin-bottom: 8px;
    font-size: 1.5rem;
  }
  
  .option-name {
    font-size: 12px;
  }
  
  .check-icon {
    color: var(--accent-color);
    font-size: 1.2rem;
  }
}

/* 动画效果定义 */
.animate-bounce {
  animation: bounce 1s ease infinite;
}

.animate-rotate {
  animation: rotate 1.5s linear infinite;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) infinite;
}

.animate-swing {
  transform-origin: center top;
  animation: swing 1.5s ease infinite;
}

/* 动画关键帧 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

@keyframes swing {
  0%, 100% { transform: rotate(0deg); }
  20% { transform: rotate(15deg); }
  40% { transform: rotate(-10deg); }
  60% { transform: rotate(5deg); }
  80% { transform: rotate(-5deg); }
}

.card-animation-preview {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.demo-card {
  width: 100px;
  height: 100px;
  background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.demo-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.demo-text {
  font-size: 12px;
  color: #333;
}

/* 卡片动画效果 */
.card-animate-fade-up {
  animation: card-fade-up 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-down {
  animation: card-fade-down 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-left {
  animation: card-fade-left 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-right {
  animation: card-fade-right 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-slide-in {
  animation: card-slide-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-zoom-in {
  animation: card-zoom-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-bounce {
  animation: card-bounce 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-flip {
  animation: card-flip 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  backface-visibility: visible;
}

.card-animate-rotate {
  animation: card-rotate 0.7s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-reveal {
  animation: card-reveal 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: bottom;
}

.card-animate-glitch {
  animation: card-glitch 0.8s linear;
}

.card-animate-pulse {
  animation: card-pulse 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes card-fade-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-fade-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-zoom-in {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes card-bounce {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(40px);
  }
  40% {
    opacity: 1;
    transform: scale(1.1) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(5px);
  }
  100% {
    transform: scale(1) translateY(0);
  }
}

@keyframes card-flip {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

@keyframes card-rotate {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

@keyframes card-reveal {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes card-glitch {
  0% {
    opacity: 0;
    transform: translate(0);
    clip-path: inset(100% 0 0 0);
  }
  20% {
    opacity: 1;
    transform: translate(-5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }
  40% {
    transform: translate(5px, -5px);
    clip-path: inset(40% 0 30% 0);
  }
  60% {
    transform: translate(5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }
  80% {
    transform: translate(-5px, -5px);
    clip-path: inset(20% 0 20% 0);
  }
  100% {
    transform: translate(0);
    opacity: 1;
    clip-path: inset(0);
  }
}

@keyframes card-pulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 0 15px rgba(var(--accent-color-rgb), 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb), 0);
  }
}

/* 标签页样式 */
.animation-tabs {
  display: flex;
  padding: 15px;
  gap: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  border: none;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.tab-button.active {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  color: var(--accent-color, #4285F4);
  box-shadow: 0 4px 8px rgba(var(--accent-color-rgb), 0.2);
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 添加移动端菜单按钮样式
.mobile-menu-btn {
  position: fixed;
  top: 15px;
  left: 15px;
  width: 42px;
  height: 42px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 900;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

  &:hover {
    transform: scale(1.05) translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }

  .nav-icon {
    color: white;
    font-size: 1.4rem;
  }
}

.mobile-menu-btn-toggle{
  left: 60px;
  width: 40px;
  height: 40px;
  overflow: hidden;
  background-color: unset !important;
  box-shadow: unset !important;
  background: unset !important;
  border: unset;
  border-radius: 50%;
}



// 添加移动端抽屉菜单背景
.mobile-drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 950;
  transition: all 0.3s ease;
}

// 添加移动端抽屉菜单
.mobile-drawer {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 0 16px 16px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.mobile-drawer-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .drawer-title {
    display: none; // 隐藏标题以节省空间
  }

  .drawer-close {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.15);
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: scale(1.05);
    }

    .nav-icon {
      color: white;
      font-size: 1.1rem;
    }
  }
}

.mobile-nav-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin: 4px 8px;
  border-radius: 12px;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-2px);
  }

  &.active {
    background: rgba(var(--accent-color-rgb), 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(var(--accent-color-rgb), 0.3);

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 2px;
      transform: translateX(-50%);
      height: 3px;
      width: 20px;
      border-radius: 2px;
      background: var(--accent-color);
    }
  }

  .nav-icon {
    font-size: 1.4rem;
    margin-bottom: 4px;
  }

  .nav-item-name {
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.mobile-setting-list {
  padding: 15px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-setting-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .nav-icon {
    margin-right: 15px;
    font-size: 1.2rem;
  }
  
  span {
    font-size: 14px;
  }
}

// 抽屉动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.drawer-enter-active,
.drawer-leave-active {
  transition: transform 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.drawer-enter-from,
.drawer-leave-to {
  transform: translateX(-100%);
}

/* 当弹窗全屏时降低z-index */
.lower-z-index {
  z-index: 0 !important;
}

// 壁纸模式选择
.wallpaper-mode-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.wallpaper-mode-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.wallpaper-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px;
  width: 90px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.02);
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
    box-shadow: 0 0 10px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
  
  [class^="i-carbon"] {
    font-size: 24px;
    color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 12px;
    font-weight: 500;
    color: #333;
  }
}

.custom-wallpaper-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.upload-button {
  background: var(--accent-color, #4285F4);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: var(--accent-color-dark, #2b5797);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .i-carbon\:cloud-upload {
    font-size: 18px;
  }
}

.upload-tip {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: -5px;
}

.custom-wallpaper-preview {
  width: 100%;
  max-width: 300px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  
  &::after {
    content: '当前自定义壁纸';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px;
    text-align: center;
    font-size: 12px;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.official-wallpaper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.official-wallpaper-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  
  &.active {
    box-shadow: 0 0 0 3px var(--accent-color, #4285F4);
  }
  
  img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
  }
}

.wallpaper-name {
  font-size: 12px;
  padding: 5px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(2px);
}

.selected-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: var(--accent-color, #4285F4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 设置弹窗相关样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 85%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 900px;
  position: relative;
  z-index: 1001;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 32px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.2) inset;
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.close-btn {
  background-color: #ff5f57;
}

.close-btn:hover {
  background-color: #ff5f57;
  filter: brightness(0.9);
}

.minimize-btn {
  background-color: #ffbd2e;
}

.minimize-btn:hover {
  background-color: #ffbd2e;
  filter: brightness(0.9);
}

.maximize-btn {
  background-color: #28c940;
}

.maximize-btn:hover {
  background-color: #28c940;
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: auto;
  background-color: #fff;
}

.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.icon-external {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='9' y='9' width='13' height='13' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'%3E%3C/path%3E%3C/svg%3E");
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 设置内容样式 */
.settings-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

.settings-sidebar {
  width: 220px;
  background-color: rgba(0, 0, 0, 0.02);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: auto;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
  
  &.active {
    background: rgba(0, 0, 0, 0.08);
    border-left: 3px solid var(--accent-color, #4285F4);
    
    .settings-icon {
      color: var(--accent-color, #4285F4);
    }
    
    span {
      color: var(--accent-color, #4285F4);
      font-weight: 500;
    }
  }
}

.settings-content-area {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #fff;
}

.settings-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  color: #333;
}

.settings-nav-item span {
  color: #333;
  font-size: 14px;
}

.setting-panel {
  padding: 20px 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: fadeIn 0.3s ease;
  background-color: #fff;
}

.setting-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.setting-group {
  margin-bottom: 24px;
  
  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }
}

.settings-input {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: var(--accent-color, #4285F4);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  margin: 10px 0;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 14px;
    color: #333;
  }
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.apply {
    background: var(--accent-color, #4285F4);
    color: white;
  }
}

/* 壁纸设置相关样式 */
.wallpaper-mode-options {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.wallpaper-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  width: 110px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.02);
    transform: translateY(-3px);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.05);
  }
  
  [class^="i-carbon"] {
    font-size: 24px;
    color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 12px;
    font-weight: 500;
  }
}

.custom-wallpaper-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.upload-button {
  background: var(--accent-color, #4285F4);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: var(--accent-color-dark, #2b5797);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.upload-tip {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: -5px;
}

.custom-wallpaper-preview {
  width: 100%;
  max-width: 300px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.blur-preview-container {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
}

.blur-preview-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blur-preview-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blur-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.official-wallpaper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 10px;
  max-height: 250px;
  overflow-y: auto;
  padding: 5px;
}

.official-wallpaper-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
  }
  
  &.active {
    box-shadow: 0 0 0 2px var(--accent-color, #4285F4), 0 5px 10px rgba(0, 0, 0, 0.1);
  }
  
  img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
  }
}

.wallpaper-name {
  font-size: 11px;
  padding: 5px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(2px);
}

.selected-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: var(--accent-color, #4285F4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 设置弹窗相关样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 85%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 900px;
  position: relative;
  z-index: 1001;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 32px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.2) inset;
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.close-btn {
  background-color: #ff5f57;
}

.close-btn:hover {
  background-color: #ff5f57;
  filter: brightness(0.9);
}

.minimize-btn {
  background-color: #ffbd2e;
}

.minimize-btn:hover {
  background-color: #ffbd2e;
  filter: brightness(0.9);
}

.maximize-btn {
  background-color: #28c940;
}

.maximize-btn:hover {
  background-color: #28c940;
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: auto;
  background-color: #fff;
}

.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.icon-external {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='9' y='9' width='13' height='13' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'%3E%3C/path%3E%3C/svg%3E");
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 设置内容样式 */
.settings-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

.settings-sidebar {
  width: 220px;
  background-color: rgba(0, 0, 0, 0.02);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: auto;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
  
  &.active {
    background: rgba(0, 0, 0, 0.08);
    border-left: 3px solid var(--accent-color, #4285F4);
    
    .settings-icon {
      color: var(--accent-color, #4285F4);
    }
    
    span {
      color: var(--accent-color, #4285F4);
      font-weight: 500;
    }
  }
}

.settings-content-area {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #fff;
}

.settings-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  color: #333;
}

.settings-nav-item span {
  color: #333;
  font-size: 14px;
}

.setting-panel {
  padding: 20px 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: fadeIn 0.3s ease;
  background-color: #fff;
}

.setting-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.setting-group {
  margin-bottom: 24px;
  
  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }
}

.settings-input {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: var(--accent-color, #4285F4);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  margin: 10px 0;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 14px;
    color: #333;
  }
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.apply {
    background: var(--accent-color, #4285F4);
    color: white;
  }
}

/* 壁纸设置相关样式 */
.wallpaper-mode-options {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.wallpaper-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  width: 110px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.02);
    transform: translateY(-3px);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.05);
  }
  
  [class^="i-carbon"] {
    font-size: 24px;
    color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 12px;
    font-weight: 500;
  }
}

.custom-wallpaper-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.upload-button {
  background: var(--accent-color, #4285F4);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: var(--accent-color-dark, #2b5797);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.upload-tip {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: -5px;
}

.custom-wallpaper-preview {
  width: 100%;
  max-width: 300px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.blur-preview-container {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
}

.blur-preview-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blur-preview-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blur-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.official-wallpaper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 10px;
  max-height: 250px;
  overflow-y: auto;
  padding: 5px;
}

.official-wallpaper-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  height: 80px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
  }

  &.active {
    box-shadow: 0 0 0 2px var(--accent-color, #4285F4), 0 5px 10px rgba(0, 0, 0, 0.1);
  }
}

.wallpaper-thumbnail {
  width: 100%;
  height: 80px;
  overflow: hidden;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.2s ease;

  /* 使用CSS优化渲染性能 */
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);

  /* 图片加载和渲染优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* 减少重绘和回流 */
  contain: layout style paint;

  &:hover {
    transform: scale(1.05) translateZ(0);
  }

  /* 加载状态优化 */
  &:not([src]) {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
}

.wallpaper-name {
  font-size: 11px;
  padding: 5px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(2px);
}

/* 动画设置相关样式 */
.animation-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  border: none;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.08);
  }
  
  &.active {
    background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
    color: var(--accent-color, #4285F4);
  }
}

.animation-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.animation-preview {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

#animation-demo-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  color: black;
}

.preview-label {
  font-size: 14px;
  color: #333;
}

.animation-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.animation-option {
  padding: 12px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.03);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
    border: 1px solid rgba(var(--accent-color-rgb, 66, 133, 244), 0.3);
  }
  
  .option-icon {
    margin-bottom: 5px;
    font-size: 1.5rem;
  }
  
  .option-name {
    font-size: 12px;
    text-align: center;
  }
}

.card-animation-preview {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.demo-card {
  width: 100px;
  height: 100px;
  background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.demo-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.demo-text {
  font-size: 12px;
  color: #333;
}

/* 主题设置相关样式 */
.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
  }
  
  .theme-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 16px;
    color: #555;
  }
  
  .active-check {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: var(--accent-color, #4285F4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }
  
  span {
    font-size: 13px;
    color: #333;
    text-transform: capitalize;
  }
}

.theme-preview {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-ui-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 20px 1fr;
  grid-template-rows: 20px 1fr;
  gap: 2px;
  padding: 4px;
}

.preview-header {
  grid-column: 1 / 3;
  border-radius: 2px;
}

.preview-sidebar {
  grid-row: 2 / 3;
  border-radius: 2px;
}

.preview-content {
  border-radius: 2px;
}

/* 主题预览样式 */
.theme-preview.light .preview-header {
  background-color: #f8f9fa;
}
.theme-preview.light .preview-sidebar {
  background-color: #e9ecef;
}
.theme-preview.light .preview-content {
  background-color: #ffffff;
}

.theme-preview.dark .preview-header {
  background-color: #1f2937;
}
.theme-preview.dark .preview-sidebar {
  background-color: #111827;
}
.theme-preview.dark .preview-content {
  background-color: #374151;
}

.theme-preview.blue .preview-header {
  background-color: #93c5fd;
}
.theme-preview.blue .preview-sidebar {
  background-color: #3b82f6;
}
.theme-preview.blue .preview-content {
  background-color: #dbeafe;
}

.theme-preview.green .preview-header {
  background-color: #6ee7b7;
}
.theme-preview.green .preview-sidebar {
  background-color: #10b981;
}
.theme-preview.green .preview-content {
  background-color: #d1fae5;
}

.theme-preview.purple .preview-header {
  background-color: #c4b5fd;
}
.theme-preview.purple .preview-sidebar {
  background-color: #8b5cf6;
}
.theme-preview.purple .preview-content {
  background-color: #ede9fe;
}

.theme-option span {
  font-size: 14px;
  color: #fff;
  text-transform: capitalize;
}

/* 使用深度选择器确保弹窗正确显示 */
:deep(.drawer-backdrop) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

/* 动画设置抽屉样式 */
.animation-settings {
  padding: 15px;
}

.animation-preview-container {
   display: flex;
   flex-direction: column;
   align-items: center;
   margin-bottom: 20px;
   padding: 20px;
   background: rgba(255, 255, 255, 0.1);
   border-radius: 12px;
}

.animation-preview {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

#animation-demo-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-label {
  font-size: 14px;
  color: #333;
}

.animation-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.animation-option {
  padding: 12px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(var(--accent-color-rgb), 0.3);
    box-shadow: 0 4px 10px rgba(var(--accent-color-rgb), 0.2);
  }
  
  .option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .option-icon {
    margin-bottom: 8px;
    font-size: 1.5rem;
  }
  
  .option-name {
    font-size: 12px;
  }
  
  .check-icon {
    color: var(--accent-color);
    font-size: 1.2rem;
  }
}

/* 动画效果定义 */
.animate-bounce {
  animation: bounce 1s ease infinite;
}

.animate-rotate {
  animation: rotate 1.5s linear infinite;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) infinite;
}

.animate-swing {
  transform-origin: center top;
  animation: swing 1.5s ease infinite;
}

/* 动画关键帧 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

@keyframes swing {
  0%, 100% { transform: rotate(0deg); }
  20% { transform: rotate(15deg); }
  40% { transform: rotate(-10deg); }
  60% { transform: rotate(5deg); }
  80% { transform: rotate(-5deg); }
}

.card-animation-preview {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.demo-card {
  width: 100px;
  height: 100px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}

/* 关于页面样式 */
.about-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.app-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.app-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.app-name {
  font-size: 24px;
  font-weight: 600;
  margin: 10px 0 5px;
  color: #333;
}

.app-version {
  font-size: 14px;
  color: #666;
}

.about-section {
  margin-bottom: 20px;
  
  h4 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
    font-weight: 500;
  }
  
  p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 10px;
  }
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  
  [class^="i-carbon"] {
    font-size: 18px;
    color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 14px;
    color: #555;
  }
}

.about-actions {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  
  .action-button {
    padding: 8px 16px;
    background-color: #f5f5f7;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #eaeaec;
      transform: translateY(-2px);
    }
  }
}

/* 时间设置样式 */
.time-settings {
  padding: 20px;
}

.time-format-options,
.date-format-options,
.time-style-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.format-option,
.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  color: black;
  &:hover {
    background: rgba(0, 0, 0, 0.02);
    transform: translateY(-3px);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.05);
  }
  
  .option-preview {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
  
  .option-label {
    font-size: 12px;
    color: #333;
  }
}

.style-preview {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analog-clock {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #333;
  position: relative;
}

.clock-face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.clock-hour {
  position: absolute;
  width: 2px;
  height: 10px;
  background: #333;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%) rotate(45deg);
  transform-origin: bottom center;
}

.clock-minute {
  position: absolute;
  width: 1px;
  height: 14px;
  background: #333;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%) rotate(180deg);
  transform-origin: bottom center;
}

.color-options {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &.active .color-preview {
    box-shadow: 0 0 0 3px var(--accent-color, #4285F4);
  }

  .color-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .color-label {
    font-size: 12px;
    color: #333;
  }
}

/* 字体选择下拉框样式 */
.font-select {
  width: 100%;
  padding: 8px 12px;
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #000000;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--accent-color, #4285F4);
    background: rgba(66, 133, 244, 0.1);
  }

  &:focus {
    outline: none;
    border-color: var(--accent-color, #4285F4);
    background: rgba(66, 133, 244, 0.1);
  }

  option {
    background: #ffffff;
    color: #000000;
    padding: 8px;
  }
}

/* 字体预览样式 */
.font-preview-container {
  margin-top: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.font-preview {
  text-align: center;
  line-height: 1;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}
</style>
