<template>
  <div 
    ref="contextMenu" 
    v-show="visible" 
    :style="position"
    @animationend="menuAnimationEnd"
    @click.stop
    class="context-menu"
  >
    <div class="menu-item-group">
      <!-- 空白区域菜单项 -->
      <template v-if="!activeApp">
        <!-- 编辑主页菜单项 -->
        <div class="menu-item" @click="toggleEditMode">
          <i class="menu-icon">✏️</i>
          <span class="menu-label">{{ isEditMode ? '完成编辑' : '编辑主页' }}</span>
        </div>
        
        <!-- 添加自定义图标菜单项 -->
        <div class="menu-item" @click="showAddIconModal">
          <i class="menu-icon">➕</i>
          <span class="menu-label">添加图标</span>
        </div>
        
        <!-- 添加创建文件夹菜单项 -->
        <div class="menu-item" @click="createFolder">
          <i class="menu-icon">📁</i>
          <span class="menu-label">创建文件夹</span>
        </div>
        
        <!-- 添加刷新壁纸菜单项 -->
        <div class="menu-item" @click="changeWallpaper">
          <i class="menu-icon">🖼️</i>
          <span class="menu-label">刷新壁纸</span>
        </div>
        
        <!-- 右键快捷打开设置 -->
        <div class="menu-item" @click="openSettingsModal">
          <i class="menu-icon">👁️</i>
          <span class="menu-label">打开设置</span>
        </div>
      </template>
      
      <!-- 图标上的菜单项 -->
      <template v-else>
        <!-- Dock栏项目的专用菜单 -->
        <template v-if="isDockItem">
          <div class="menu-item" @click="removeFromDock(activeApp)">
            <i class="menu-icon">💔</i>
            <span class="menu-label">从收藏栏移除</span>
          </div>
          <!-- 新增：切换打开方式 -->
          <div class="menu-item" v-if="activeApp.type === 'app' || !activeApp.type" @click="setOpenMode(activeApp.iscanopen === 2 ? 1 : 2)">
           <i class="menu-icon">🔄</i>
           <span class="menu-label">{{ activeApp.iscanopen === 2 ? '内嵌打开' : '新窗口打开' }}</span>
          </div>
          <div v-if="activeApp.isfixed === 1" class="menu-item" @click="unfixApp(activeApp)">
            <i class="menu-icon">📌</i>
            <span class="menu-label">取消固定收藏栏</span>
          </div>
          <div v-else-if="activeApp.isfixed === 0" class="menu-item" @click="fixApp(activeApp)">
            <i class="menu-icon">📍</i>
            <span class="menu-label">固定到收藏栏</span>
          </div>
        </template>

        <!-- 非Dock栏项目的完整菜单 -->
        <template v-else>
          <!-- 编辑主页菜单项 -->
          <div class="menu-item" @click="toggleEditMode">
            <i class="menu-icon">✏️</i>
            <span class="menu-label">{{ isEditMode ? '完成编辑' : '编辑主页' }}</span>
          </div>

          <!-- 添加自定义图标菜单项 -->
          <div class="menu-item" @click="showAddIconModal">
            <i class="menu-icon">➕</i>
            <span class="menu-label">添加图标</span>
          </div>

          <div class="menu-divider"></div>
        
          <!-- iframe特殊菜单项 -->
          <template v-if="activeApp.type === 'iframe'">
            <div class="menu-title">{{ activeApp.name }}</div>
            <div class="menu-item">
              <i class="menu-icon">📋</i>
              <div class="url-value truncate">{{ activeApp.url }}</div>
            </div>
            <div class="menu-divider"></div>
          </template>

          <!-- 通用菜单项 -->
          <div class="menu-item" v-if="activeApp.type === 'card' || activeApp.type == 'folder'" @click="toggleSubmenu('sizeMenu')">
            <i class="menu-icon">📏</i>
            <span class="menu-label">卡片大小</span>
            <i class="menu-arrow">›</i>

            <!-- 二级子菜单: 尺寸选择 -->
            <div class="submenu" :class="{ active: activeSubmenu === 'sizeMenu' }" @click.stop>
              <div
                v-for="size in availableSizes"
                :key="`${size.w}x${size.h}`"
                class="submenu-item"
                :class="{ active: activeApp && activeApp.size?.w === size.w && activeApp.size?.h === size.h }"
                @click="setAppSize(size.w, size.h)"
              >
                <i class="menu-icon">{{ size.icon }}</i>
                <span>{{ size.w }}x{{ size.h }}</span>
              </div>
            </div>
          </div>

          <!-- 新增：切换打开方式 -->
          <div class="menu-item" v-if="activeApp.type === 'app'" @click="setOpenMode(activeApp.iscanopen === 2 ? 1 : 2)">
            <i class="menu-icon">🔄</i>
            <span class="menu-label">{{ activeApp.iscanopen === 2 ? '内嵌打开' : '新窗口打开' }}</span>
          </div>

          <!-- 新增：编辑图标菜单项 -->
          <div class="menu-item" v-if="activeApp.type === 'app'" @click="editIcon">
            <i class="menu-icon">✏️</i>
            <span class="menu-label">编辑图标</span>
          </div>

          <div class="menu-item danger" @click="deleteApp">
            <i class="menu-icon">🗑️</i>
            <span class="menu-label">删除卡片</span>
          </div>

          <!-- Dock栏相关菜单项 -->
          <div class="menu-divider"></div>
          <div v-if="isInDock" class="menu-item" @click="removeFromDock(activeApp)">
            <i class="menu-icon">💔</i>
            <span class="menu-label">从收藏栏移除</span>
          </div>
          <div v-else class="menu-item" @click="addToDock(activeApp)">
            <i class="menu-icon">⭐</i>
            <span class="menu-label">添加到收藏栏</span>
          </div>

          <!-- 固定到收藏栏菜单项 - 只有登录用户且应用在收藏栏中才显示 -->
          <template v-if="isLoggedIn && isInDock">
            <div v-if="activeApp.isfixed === 1" class="menu-item" @click="unfixApp(activeApp)">
              <i class="menu-icon">📌</i>
              <span class="menu-label">取消固定收藏栏</span>
            </div>
            <div v-else-if="activeApp.isfixed === 0" class="menu-item" @click="fixApp(activeApp)">
              <i class="menu-icon">📍</i>
              <span class="menu-label">固定到收藏栏</span>
            </div>
          </template>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import emitter from '@/utils/mitt';
import { AddUseCollect, deleteCollect, fixedApp, cancalfixedApp  } from '@/api/collect'


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ top: '0px', left: '0px' })
  },
  activeApp: {
    type: Object,
    default: null
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  dockApps: {
    type: Array,
    default: () => []
  },
  isDockItem: {
    type: Boolean,
    default: false
  }
})



const emit = defineEmits([
  'close',
  'toggle-edit-mode',
  'add-icon-modal',
  'create-folder',
  'set-app-size',
  'set-open-mode',
  'move-app-to-category',
  'delete-app',
  'add-to-dock',
  'remove-from-dock',
  'edit-icon',
  'change-wallpaper',
  'toggle-pure-mode',
  'fix-app',
  'unfix-app'
])

// 活动子菜单
const activeSubmenu = ref(null)
const contextMenu = ref(null)

// 切换编辑模式
function toggleEditMode() {
  emit('toggle-edit-mode')
}

// 显示添加图标模态窗口
function showAddIconModal() {
  emit('add-icon-modal')
  emit('close')
}

// 创建文件夹
function createFolder() {
  emit('create-folder')
  emit('close')
}

// 设置应用尺寸
function setAppSize(width, height) {
  emit('set-app-size', { width, height })
  emit('close')
}

// 设置打开方式
function setOpenMode(mode) {
  emit('set-open-mode', mode)
  emit('close')
}

// 移动应用到分类
function moveAppToCategory(category) {
  emit('move-app-to-category', category)
  emit('close')
}

// 删除应用
function deleteApp() {
  emit('delete-app')
  emit('close')
}

// 添加到Dock栏
function addToDock(app) {
  const token = localStorage.getItem('token')  
  if(token) {
    AddUseCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('添加成功')
      }
    })
  }
  emit('add-to-dock', app)
  emit('close')
}

// 从Dock栏移除
function removeFromDock(app) {
  const token = localStorage.getItem('token')  
  if(token) {
    deleteCollect(app.id).then((res) => {
      if(res.status == 200) {
        console.log('删除成功')
      }
    })
  }
  emit('remove-from-dock', app)
  emit('close')
}

// 编辑图标
function editIcon() {
  emit('edit-icon', props.activeApp)
  emit('close')
}

// 切换壁纸
function changeWallpaper() {
  emit('change-wallpaper')
  emit('close')
}

// 切换纯净模式
function togglePureMode() {
  emit('toggle-pure-mode')
  emit('close')
}

function openSettingsModal() {
  emitter.emit('open-settings-modal', { message: 'Hello, mitt!' });
}


// 菜单动画结束处理
function menuAnimationEnd() {
  // 动画结束后的操作，如需要可以添加
}

// 切换子菜单
function toggleSubmenu(menu) {
  if (activeSubmenu.value === menu) {
    activeSubmenu.value = null
  } else {
    activeSubmenu.value = menu
  }
}

// 添加点击外部关闭菜单的功能
function handleClickOutside(event) {
  // 🔧 修复：忽略右键点击事件，避免与 contextmenu 事件冲突
  if (event.button === 2) {
    return
  }

  // 🔧 修复：如果菜单不可见，不处理点击外部事件
  if (!props.visible) {
    return
  }

  if (contextMenu.value && !contextMenu.value.contains(event.target)) {
    // 🔍 右键菜单调试 - 点击外部关闭菜单
    emit('close')
  }
}

// 在组件挂载时添加全局点击事件
onMounted(() => {
  // 设置一个全局的事件监听器来关闭菜单
  document.addEventListener('mousedown', handleClickOutside)
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

// 监听visible属性变化，重置子菜单状态
watch(() => props.visible, (newValue) => {
  if (!newValue) {
    // 菜单隐藏时，重置子菜单状态
    activeSubmenu.value = null
  }
})

// 动态判断当前应用是否在收藏栏
const isInDock = computed(() => {
  if (!props.activeApp || !props.activeApp.id) return false
  return props.dockApps.some(app => app.id === props.activeApp.id)
})

// 检查用户是否已登录
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('token')
})

// 固定应用到收藏栏
function fixApp(app) {
  const token = localStorage.getItem('token')
  if (token) {
    fixedApp(app.id).then((res) => {
      if (res.status == 200) {
        console.log('固定成功')
        emit('fix-app', app)
      }else{
        message.error('固定失败')
      }
    }).catch((error) => {
      console.error('固定失败:', error)
    })
  }else{
    message.error('请登录')
  }
  emit('close')
}

// 取消固定应用
function unfixApp(app) {
  const token = localStorage.getItem('token')
  if (token) {
    cancalfixedApp(app.id).then((res) => {
      if (res.status == 200) {
        console.log('取消固定成功')
        emit('unfix-app', app)
      }else{
        message.error('取消固定失败')
      }
    }).catch((error) => {
      console.error('取消固定失败:', error)
    })
  }else{
    message.error('请登录')
  }
  emit('close')
}


// 根据组件类型动态返回可用的尺寸选项
const availableSizes = computed(() => {
  console.log(props.activeApp,'activeApp')
  if (!props.activeApp) return []

  let appType = props.activeApp.websiteAddress
  if(props.activeApp.type == 'folder') {
    appType = 'folder'
  }
  console.log(appType,'activeApp')
  // 定义不同组件类型的尺寸配置
  const sizeConfigs = {
    'folder': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 1, icon: '▬▬▬' }
    ],
    // 下班倒计时、实时新闻、日历、时钟、天气、敲木鱼 - 2*1 2*2 3*2 3*3 4*2 4*3
    'RelaxCard': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      // { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'HotCard': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'CalendarCard': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'clock': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'Weather': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'WoodenFish': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],

    // 赛博喂仓鼠、摸鱼小视频 - 2*1 2*2 3*2 3*3 4*2 4*3
    'iframe': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 2, icon: '▮▮▮' },
      { w: 3, h: 3, icon: '▩▩' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 3, icon: '▩▩▮' }
    ],
    'VideoCardAdapter': [
      { w: 2, h: 3, icon: '▮▮' },
      { w: 5, h: 3, icon: '▮▮▮▮▮' },
      { w: 3, h: 5, icon: '▩▩▩' },
      { w: 6, h: 4, icon: '▮▮▮▮▮▮' },
      { w: 4, h: 6, icon: '▩▩▩▩▩▩' },
      { w: 10, h: 6, icon: '▬▬▬▬▬▬▬▬▬▬' }
    ],

    // 摸鱼日报、迷你浏览器 - 2*3 5*3 3*5 6*4 4*6 10*6
    'ImageCard': [
      { w: 2, h: 3, icon: '▮▮' },
      // { w: 5, h: 3, icon: '▮▮▮▮▮' },
      { w: 3, h: 5, icon: '▩▩▩' },
      // { w: 6, h: 4, icon: '▮▮▮▮▮▮' },
      { w: 4, h: 6, icon: '▩▩▩▩▩▩' },
      // { w: 10, h: 6, icon: '▬▬▬▬▬▬▬▬▬▬' }
    ],
    'LinkCard': [
      { w: 2, h: 3, icon: '▮▮' },
      { w: 5, h: 3, icon: '▮▮▮▮▮' },
      { w: 3, h: 5, icon: '▩▩▩' },
      { w: 4, h: 6, icon: '▩▩▩▩▩▩' },
      { w: 5, h: 8, icon: '▩▩▩▩▩▩' },
      { w: 6, h: 4, icon: '▮▮▮▮▮▮' },
      { w: 6, h: 8, icon: '▩▩▩▩▩▩' },
      { w: 7, h: 8, icon: '▩▩▩▩▩▩' },
      { w: 8, h: 5, icon: '▩▩▩▩▩▩' },
      { w: 8, h: 6, icon: '▩▩▩▩▩▩' },
      { w: 8, h: 8, icon: '▩▩▩▩▩▩' },
      { w: 10, h: 6, icon: '▬▬▬▬▬▬▬▬▬▬' }
    ],
    'AnimatedClock': [
      { w: 2, h: 1, icon: '▬▬' },
      { w: 2, h: 2, icon: '▩' },
      { w: 3, h: 3, icon: '▩▩' },
    ],
    'TodoList': [
      // { w: 2, h: 2, icon: '▩' },
      { w: 2, h: 4, icon: '▮▮' },
      { w: 4, h: 2, icon: '▬▬▬▬' },
      { w: 4, h: 4, icon: '▩▩▩▩' }
    ]
  }

  // 如果有特定配置，返回特定配置，否则返回默认配置
  if (sizeConfigs[appType]) {
    return sizeConfigs[appType]
  }

  // 默认尺寸选项（普通应用）
  return [
    // { w: 1, h: 1, icon: '▬' },
    // { w: 2, h: 1, icon: '▬▬' },
    // { w: 2, h: 2, icon: '▩' },
    // { w: 3, h: 2, icon: '▮▮▮' },
    // { w: 3, h: 3, icon: '▩▩' },
    // { w: 4, h: 2, icon: '▬▬▬▬' },
    // { w: 4, h: 3, icon: '▩▩▮' },
    // { w: 4, h: 6, icon: '▩▩▩▩▩▩' }
  ]
})
</script>

<style scoped lang="scss">
/* 右键菜单 */
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  max-width: 120px;
  overflow: visible;
  color: #333;
  backdrop-filter: blur(10px);
  animation: menuAppear 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: top left;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 13px;
  padding: 4px 0;
}

.menu-title {
  font-weight: 500;
  color: #666;
  font-size: 12px;
  padding: 4px 12px;
  margin-top: 0;
}

.menu-item-group {
  width: 100%;
}

.menu-item {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.menu-item:hover {
  background-color: rgba(var(--accent-color-rgb, 0, 120, 212), 0.1);
}

.menu-icon {
  display: none;
  // display: flex !important;
  margin-right: 8px;
  font-size: 14px;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.menu-label {
  flex: 1;
  font-size: 13px;
}

.menu-arrow {
  font-size: 14px;
  color: #777;
  margin-left: 4px;
}

.menu-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 4px 0;
}

.submenu {
  position: absolute;
  top: -4px;
  left: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  width: 200px;
  overflow: hidden;
  transform: translateX(8px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 4px 0;
  z-index: 10;
  display: flex;
  flex-wrap: wrap;

}

.submenu.active {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

.submenu-item {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 12px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  justify-content: center;
  // width: 100%;
}

.submenu-item:hover {
  background-color: rgba(var(--accent-color-rgb, 0, 120, 212), 0.1);
}

.submenu-item.active {
  background-color: rgba(var(--accent-color-rgb, 0, 120, 212), 0.15);
  color: var(--accent-color, #4285F4);
  font-weight: 500;
}

.menu-item.danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.url-value {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  word-break: break-all;
  max-width: 150px;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes menuAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 