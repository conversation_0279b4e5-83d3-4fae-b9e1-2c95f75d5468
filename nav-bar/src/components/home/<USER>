<template>
  <div class="collection-wrapper">
    <div class="collection-card" @click="openCollection(collection)">
      <div class="collection-icon-container">
        <div class="collection-icon-wrapper">
          <!-- 直接使用websiteAddress作为图标 -->
          <img v-if="collection.websiteAddress" :src="collection.websiteAddress" alt="collection-icon" class="collection-custom-icon" />
          <!-- 使用icon作为备选 -->
          <img v-else-if="collection.icon" :src="collection.icon" alt="collection-icon" class="collection-custom-icon" />
          <!-- 默认图标 -->
          <span v-else class="collection-icon">📦</span>
          <span class="collection-icon-text">{{ collection.name }}</span>
          <!-- 调试信息(开发阶段使用) -->
          <div v-if="false" style="position: absolute; bottom: 0; color: red; font-size: 10px; background: rgba(255,255,255,0.8);">
            WA: {{ collection.websiteAddress ? 'Y' : 'N' }}
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="app-name">{{ collection.name }}</div> -->
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  collection: {
    type: Object,
    required: true
  }
});

const emit = defineEmits([
  'open-collection',
  'contextmenu',
  'click',
  'mousedown',
  'touchstartPassive',
  'mouseenter',
  'mouseleave',
  'focus',
  'blur'
]);

const openCollection = (collection) => {  
  // 确保深拷贝集合，避免引用问题
  const collectionCopy = JSON.parse(JSON.stringify(collection));
  
  // 确保children存在且格式正确
  if (collectionCopy.children) {
    collectionCopy.children = collectionCopy.children.map(child => {
      // 确保icon属性正确
      if (!child.icon && child.logo) {
        child.icon = child.logo;
      }
      // 确保URL格式正确
      if (child.websiteAddress && !child.url) {
        child.url = `https://${child.websiteAddress}`;
      }
      return child;
    });
  } else {
    collectionCopy.children = [];
  }
  
  emit('open-collection', collectionCopy);
};
</script>

<style scoped>
.collection-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.collection-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 5px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collection-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.collection-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.collection-icon-wrapper {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 100%;
  /* background-color: rgba(255, 255, 255, 0.7); */
  border-radius: 12px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  /* gap: 10px; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.collection-icon {
  font-size: 38px;
  color: #4285F4;
  width: 40px;
  height: 40px;
}
.collection-icon-text{
  color: black;
  font-size: 14px;
  width: 56px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.collection-custom-icon {
  object-fit: contain;
  border-radius: 8px;
  /* width: 0%;
  height: 80%; */
  background-size: 100% 100%;
  background-repeat: no-repeat;
  /* margin-right: 10px; */
}

/* 应用名称样式和主应用卡片保持一致 
   注意：这些样式会被Home.vue中的全局app-name样式覆盖 */
.app-name {
  font-size: 12px;
  text-align: center;
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 