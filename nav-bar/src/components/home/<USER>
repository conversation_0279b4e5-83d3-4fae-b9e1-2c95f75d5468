<template>
  <div>
    <!-- 展开/收起按钮 - 独立于面板外部显示 -->
    <!-- <div class="toggle-button" @click="toggleExpand">
      <div v-if="isExpanded" style="height: 30px; width: 30px;">

        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M10.5752 24.5998C10.3802 24.5998 10.1852 24.5248 10.0502 24.3748C9.7502 24.0748 9.7502 23.6098 10.0502 23.3098L18.3752 14.9848L10.0502 6.67477C9.7502 6.37477 9.7502 5.90977 10.0502 5.60977C10.3502 5.30977 10.8152 5.30977 11.1152 5.60977L19.9652 14.4598C20.2652 14.7598 20.2652 15.2248 19.9652 15.5248L11.1152 24.3748C10.9652 24.5248 10.7702 24.5998 10.5752 24.5998Z"
            fill="#1F1F1F" />
        </svg>
      </div>
      <div v-else style="height: 30px; width: 30px;">
        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M19.4248 5.39999C19.6198 5.39999 19.8148 5.47499 19.9498 5.62499C20.2498 5.92499 20.2498 6.38999 19.9498 6.68999L11.6248 15.015L19.9498 23.325C20.2498 23.625 20.2498 24.09 19.9498 24.39C19.6498 24.69 19.1848 24.69 18.8848 24.39L10.0348 15.54C9.73481 15.24 9.73481 14.775 10.0348 14.475L18.8848 5.62499C19.0348 5.47499 19.2298 5.39999 19.4248 5.39999Z"
            fill="#1F1F1F" />
        </svg>
      </div>
    </div> -->

    <!-- 弹幕面板 -->
    <div class="danmaku-panel" :class="{ 'expanded': isExpanded, 'pinned': isPinned }" @mouseenter="onPanelMouseEnter"
      @mouseleave="onPanelMouseLeave">
      <!-- 面板主体内容 -->
      <div class="panel-content" v-if="isExpanded">
        <!-- 顶部操作栏 -->
        <div class="panel-header">
          <div class="panel-title">弹幕</div>
        </div>
        <div class="panel-top-info">
          <div class="panel-top-info-mimi">
            <img src="@/assets/header/mimi.png" alt="mimi">
          </div>

          <div class="panel-top-info-mimi-title">使用技巧：</div>
          <div class="panel-top-info-mimi-text">双击鼠标进入办公模式</div>
        </div>

        <!-- 弹幕显示区域 -->
        <div class="danmaku-container" ref="danmakuContainer">
          <div v-for="(msg, index) in danmakuMessages" :key="index" class="danmaku-item"
            :class="{ 'animate-fade-in': true, 'highlight': msg.highlight }"
            :style="{ 'animation-delay': `${index * 0.05}s` }">
            <div class="danmaku-user" v-if="msg.user">{{ msg.user }}:</div>
            <div class="danmaku-content">{{ msg.content }}</div>
            <div class="danmaku-time">{{ formatTime(msg.time) }}</div>
          </div>

          <div v-if="danmakuMessages.length === 0" class="empty-message">
            暂无弹幕，发送一条试试吧！
          </div>
        </div>

        <!-- 底部发送区域 -->
        <div class="send-container">
          <Textarea v-model:value="newMessage" @keyup.enter="sendDanmaku" class="danmaku-input" placeholder="发送一条弹幕..."
            style="background-color: rgba(255, 255, 255, 0.5);color: #1E1E1E;" />
          <button class="send-button" @click="sendDanmaku">发送</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Textarea } from 'ant-design-vue';
// 状态管理
const isExpanded = ref(false); // 默认展开面板
const isPinned = ref(false); // 是否固定面板
const newMessage = ref(''); // 新消息输入框
const danmakuMessages = ref([]); // 弹幕消息列表
const danmakuContainer = ref(null); // 弹幕容器引用
let autoHideTimer = null; // 自动隐藏计时器

// 启动自动隐藏定时器
const startAutoHideTimer = () => {
  clearTimeout(autoHideTimer);
  autoHideTimer = setTimeout(() => {
    isExpanded.value = false;
  }, 30000); // 30秒后自动隐藏
};

// 清除自动隐藏定时器
const clearAutoHideTimer = () => {
  clearTimeout(autoHideTimer);
};

// 鼠标进入面板时停止自动隐藏
const onPanelMouseEnter = () => {
  clearAutoHideTimer();
};

// 鼠标离开面板时启动自动隐藏定时器
const onPanelMouseLeave = () => {
  if (isExpanded.value) {
    startAutoHideTimer();
  }
};

// 切换面板展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
  localStorage.setItem('danmakuPanelExpanded', isExpanded.value);

  if (isExpanded.value) {
    startAutoHideTimer(); // 展开时启动自动隐藏定时器
  } else {
    clearAutoHideTimer(); // 收起时清除定时器
  }

  // 滚动到底部
  setTimeout(() => {
    if (danmakuContainer.value) {
      danmakuContainer.value.scrollTop = danmakuContainer.value.scrollHeight;
    }
  }, 100);
};

// 切换面板固定状态
const togglePin = () => {
  isPinned.value = !isPinned.value;
  localStorage.setItem('danmakuPanelPinned', isPinned.value);
};

// 发送弹幕
const sendDanmaku = () => {

  if (!newMessage.value.trim()) return;

  // 创建新弹幕
  const newDanmaku = {
    user: '用户' + Math.floor(Math.random() * 1000),
    content: newMessage.value,
    time: new Date(),
    highlight: true // 高亮显示自己发送的弹幕
  };

  // 添加到列表
  danmakuMessages.value.push(newDanmaku);
  newMessage.value = '';

  // 存储到本地
  localStorage.setItem('danmakuMessages', JSON.stringify(danmakuMessages.value));

  // 滚动到底部
  setTimeout(() => {
    if (danmakuContainer.value) {
      danmakuContainer.value.scrollTop = danmakuContainer.value.scrollHeight;
    }
  }, 100);
};

// 清空弹幕
const clearDanmaku = () => {
  danmakuMessages.value = [];
  localStorage.setItem('danmakuMessages', JSON.stringify([]));
};

// 格式化时间
const formatTime = (time) => {
  const date = new Date(time);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

// 生成随机弹幕的内容
const randomMessages = [
  '这个应用真不错！',
  '有谁知道如何使用这个功能吗？',
  '大家好，我是新来的~',
  '今天天气真好！',
  '推荐一下好用的应用',
  '这个界面设计得太棒了',
  '有人在线吗？',
  '学习使我快乐',
  '分享一下工作技巧',
  '周末有什么计划？',
  '网站还行啊',
  '帮我看看这个Bug怎么解决？',
  '刚刚更新了版本，有什么新特性？',
  '界面加载有点慢',
  '有没有小伙伴一起组队？',
  '这个功能有点入口深',
  '请问有人遇到同样的问题吗？',
  '今日份打卡完成✅',
  '加个好友聊聊技术吧',
  '谁能给个教程链接？',
  '操作步骤有点复杂',
  '期待下次更新',
  '这个图标很有创意',
  '请问支持多语言吗？',
  'App崩溃了，求解',
  '短信验证码一直收不到',
  '有交流群吗？',
  '数据同步失败怎么办？',
  '真是太方便了！',
  '这个按钮应该换个颜色',
  '有人测试过新功能吗？',
  '界面布局有点乱',
  '功能很强大，值得推荐',
  '加载动画很流畅',
  '收藏夹功能在哪？',
  '希望能优化一下性能',
  '反馈问题怎么提交？',
  '欢迎新人报道',
  '体验了一下，很喜欢',
  '求开发者给个答复',
  '有试过夜间模式吗？'
];

// 生成随机弹幕
const generateRandomDanmaku = () => {
  // 随机选择一条消息
  const messageIndex = Math.floor(Math.random() * randomMessages.length);
  const userNumber = Math.floor(Math.random() * 1000);

  // 创建随机弹幕
  const randomDanmaku = {
    user: '访客' + userNumber,
    content: randomMessages[messageIndex],
    time: new Date(),
    highlight: false
  };

  // 添加到列表
  danmakuMessages.value.push(randomDanmaku);

  // 限制弹幕数量，防止过多
  if (danmakuMessages.value.length > 50) {
    danmakuMessages.value.shift();
  }

  // 存储到本地
  localStorage.setItem('danmakuMessages', JSON.stringify(danmakuMessages.value));

  // 如果在视图底部，则滚动到底部
  if (danmakuContainer.value &&
    danmakuContainer.value.scrollHeight - danmakuContainer.value.scrollTop <= danmakuContainer.value.clientHeight + 100) {
    setTimeout(() => {
      danmakuContainer.value.scrollTop = danmakuContainer.value.scrollHeight;
    }, 100);
  }
};

let randomInterval;

onMounted(() => {
  // 从本地存储恢复设置和消息
  const savedExpanded = localStorage.getItem('danmakuPanelExpanded');
  isExpanded.value = savedExpanded !== null ? savedExpanded === 'true' : true; // 默认为展开
  isPinned.value = localStorage.getItem('danmakuPanelPinned') === 'true';

  // 启动自动隐藏定时器
  if (isExpanded.value) {
    startAutoHideTimer();
  }

  // 尝试恢复弹幕消息
  try {
    const savedMessages = localStorage.getItem('danmakuMessages');
    if (savedMessages) {
      danmakuMessages.value = JSON.parse(savedMessages);
    }
  } catch (error) {
    console.error('Failed to restore danmaku messages:', error);
  }

  // 定时生成随机弹幕
  randomInterval = setInterval(generateRandomDanmaku, 15000); // 每15秒生成一条随机弹幕
});

onUnmounted(() => {
  // 清除定时器
  clearInterval(randomInterval);
  clearAutoHideTimer();
});
</script>

<style scoped lang="scss">
.danmaku-panel {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  height: 600px;
  max-height: 600px;
  width: 0;
  /* 收起状态宽度为0 */
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 20px 0 0 20px;
  display: flex;
  flex-direction: column;
  z-index: 9999;
  /* 增加z-index确保在最上层 */
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  /* Rectangle 1361 */


  &.pinned {
    z-index: 10000;
  }

  &.expanded {
    width: 250px;
    min-height: 600px;
    height: 600px;
  }
}

.toggle-button {
  width: 80px;
  /* 按钮宽度 */
  height: 80px;
  /* 按钮高度 */
  border-radius: 50%;
  /* 半圆形 */
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: flex-start;
  cursor: pointer;
  background-color: white;
  color: #333;
  font-size: 20px;
  font-weight: bold;
  position: fixed;
  top: 50%;
  right: -40px;
  transform: translateY(-50%);
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
  z-index: 10001;
  /* 确保按钮显示在最顶层 */
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: -3px 0 8px rgba(0, 0, 0, 0.4);
    color: #4285F4;
  }
}

.panel-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding-top: 20px;
  overflow-y: auto;
}

.panel-header {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  /* 使用黑色字体 */
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
  }
}

.pin-button {
  color: #4285F4;

  &:hover {
    color: #2a56c6;
  }
}

.clear-button {
  color: #ff4d4f;

  &:hover {
    color: #f5222d;
  }
}

.danmaku-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
}

.danmaku-item {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  //   background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  transition: all 0.3s;
  text-align: left;

  &.highlight {
    // background-color: rgba(66, 133, 244, 0.1);
    border-left: 3px solid #4285F4;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.danmaku-user {
  font-weight: 500;
  font-size: 13px;
  color: #1E1E1E;
  margin-bottom: 4px;
}

.danmaku-content {
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
  color: #646464;
  padding: 5px 10px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.5);
}

.danmaku-time {
  align-self: flex-end;
  font-size: 11px;
  color: rgba(0, 0, 0, 0.4);
  margin-top: 4px;
}

.empty-message {
  padding: 30px 0;
  text-align: center;
  color: rgba(0, 0, 0, 0.3);
  font-size: 14px;
}

.send-container {
  padding: 10px;
  display: flex;
  gap: 10px;
  position: sticky;
  bottom: 0;
  z-index: 2;
}

.danmaku-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  outline: none;
  transition: all 0.3s;

  &:focus {
    border-color: #4285F4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }
}

.send-button {
  background-color: #ffffff;
  color: rgb(0, 0, 0);
  border: none;
  border-radius: 10px;
  padding: 0 15px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 12px;
  /* Rectangle 1368 */
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: 54px;
  height: 26px;
  background: #FFFFFF;
  border-radius: 6px;
}

/* 动画定义 */
.animate-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .danmaku-panel.expanded {
    width: 250px;
  }
}

@media (max-width: 480px) {
  .danmaku-panel.expanded {
    width: 220px;
  }
}

.panel-top-info {
  background-image: url('../../assets/header/dmtopbg.png');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  height: 80px;
  width: 100%;
  position: relative;
  padding: 10px 30px;
  text-align: left;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 10px;
  justify-content: center;

  .panel-top-info-mimi-title {
    font-family: 'Alibaba PuHuiTi 2.0';
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 15px;
    letter-spacing: 0.05em;
    color: #232323;
  }

  .panel-top-info-mimi-text {
    font-family: 'Alibaba PuHuiTi 2.0';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 17px;
    /* identical to box height */
    letter-spacing: 0.05em;
    color: #232323;
  }

  .panel-top-info-mimi {
    width: 71px;
    height: 50px;
    position: absolute;
    right: 20px;
    top: -35px;
  }
}
</style>
