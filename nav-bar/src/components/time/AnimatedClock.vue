<template>
  <div v-if="gridStyle == '2x1'" class="animated-clock digital-layout size-2x1">
    <div class="digital-clock">
      <div class="time digi-font">{{ formattedTime }}</div>
    </div>
  </div>
  <div v-else class="animated-clock" :class="{ 'dark-mode': isDarkMode }">
    <div class="clock-container">
      <div class="clock-face" style="margin: 0;">
        <!-- 时钟刻度 -->
        <div 
          v-for="n in 12" 
          :key="n" 
          class="hour-mark" 
          :style="getHourMarkStyle(n)"
        ></div>
        
        <!-- 时针 -->
        <div class="hour-hand" :style="{ transform: `rotate(${hourDegrees}deg)` }"></div>
        
        <!-- 分针 -->
        <div class="minute-hand" :style="{ transform: `rotate(${minuteDegrees}deg)` }"></div>
        
        <!-- 秒针 -->
        <div class="second-hand" :style="{ transform: `rotate(${secondDegrees}deg)` }"></div>
        
        <!-- 中心点 -->
        <div class="center-point"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';

/**
 * 动画时钟组件属性
 * @typedef {Object} Props
 * @property {String} timezone - 显示的时区名称（仅文本展示，不影响实际时间）
 * @property {Boolean} isDarkMode - 是否启用深色模式
 * @property {Boolean} showSeconds - 是否在数字时钟中显示秒
 * @property {Boolean} use24Hour - 是否使用24小时制
 */
const props = defineProps({
  timezone: {
    type: String,
    default: '北京时间'
  },
  isDarkMode: {
    type: Boolean,
    default: false
  },
  showSeconds: {
    type: Boolean,
    default: true
  },
  use24Hour: {
    type: Boolean,
    default: true
  },
  size: {
    type: Object,
    default: () => ({ w: 2, h: 2 })
  }
});

watch(() => props.size, (newSize) => {
  gridStyle.value = newSize.w + 'x' + newSize.h
})

const gridStyle = ref()


// 当前时间
const now = ref(new Date());
// 计时器
let timer = null;

// 更新时间的函数
const updateTime = () => {
  now.value = new Date();
};

// 时针角度
const hourDegrees = computed(() => {
  const hours = now.value.getHours();
  const minutes = now.value.getMinutes();
  return (hours % 12) * 30 + minutes * 0.5; // 每小时30度，分钟也会影响时针位置
});

// 分针角度
const minuteDegrees = computed(() => {
  const minutes = now.value.getMinutes();
  const seconds = now.value.getSeconds();
  return minutes * 6 + seconds * 0.1; // 每分钟6度，秒数也会影响分针位置
});

// 秒针角度
const secondDegrees = computed(() => {
  const seconds = now.value.getSeconds();
  return seconds * 6; // 每秒钟6度
});

// 格式化时间
const formattedTime = computed(() => {
  const hours = now.value.getHours();
  const minutes = now.value.getMinutes();
  const seconds = now.value.getSeconds();

  // 2x1布局始终显示秒数，使用24小时制格式 HH:MM:SS
  if (gridStyle.value === '2x1') {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  // 其他布局按原有逻辑
  if (props.use24Hour) {
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    return props.showSeconds ? `${timeStr}:${seconds.toString().padStart(2, '0')}` : timeStr;
  } else {
    const displayHours = hours % 12 || 12;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const timeStr = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    const fullTimeStr = props.showSeconds ? `${timeStr}:${seconds.toString().padStart(2, '0')}` : timeStr;
    return `${fullTimeStr} ${ampm}`;
  }
});

// 格式化日期
const formattedDate = computed(() => {
  const year = now.value.getFullYear();
  const month = now.value.getMonth() + 1;
  const date = now.value.getDate();
  const day = now.value.getDay();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  return `${year}年${month}月${date}日 ${weekdays[day]}`;
});

// 获取时钟刻度样式
const getHourMarkStyle = (hour) => {
  const isQuarterHour = hour % 3 === 0;
  const rotation = (hour - 3) * 30; // 从3点钟位置开始，每小时30度
  
  return {
    transform: `rotate(${rotation}deg) translateX(${isQuarterHour ? '68px' : '70px'})`,
    width: isQuarterHour ? '10px' : '5px',
    height: isQuarterHour ? '3px' : '2px'
  };
};

// 组件挂载时启动时钟
onMounted(() => {
  updateTime();
  timer = setInterval(updateTime, 1000);
  gridStyle.value = props.size.w + 'x' + props.size.h
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style lang="scss" scoped>
/* 引入digi字体 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

.animated-clock {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  color: #333;
  transition: all 0.3s ease;
}

/* 2x1数字时钟布局 */
.digital-layout {
  padding: 10px;
}

.digital-layout .digital-clock {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: #000000;
}

.digi-font {
  font-family: 'Orbitron', 'Courier New', monospace;
  font-weight: 700;
  letter-spacing: 0.1em;
}

.clock-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.clock-face {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f5f5f5;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.dark-mode .clock-face {
  background: #2c3e50;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hour-mark {
  position: absolute;
  background-color: #333;
  transform-origin: 0 0;
  transition: background-color 0.3s ease;
}

.dark-mode .hour-mark {
  background-color: #ddd;
}

.hour-hand, .minute-hand, .second-hand {
  position: absolute;
  transform-origin: bottom center;
  bottom: 50%;
  left: 50%;
  transition: transform 0.05s cubic-bezier(0.4, 2.08, 0.55, 0.44);
}

.hour-hand {
  width: 5px;
  height: 40px;
  margin-left: -2.5px;
  background: #333;
  border-radius: 4px;
  z-index: 2;
}

.dark-mode .hour-hand {
  background: #ecf0f1;
}

.minute-hand {
  width: 3px;
  height: 55px;
  margin-left: -1.5px;
  background: #555;
  border-radius: 4px;
  z-index: 3;
}

.dark-mode .minute-hand {
  background: #bdc3c7;
}

.second-hand {
  width: 1.5px;
  height: 65px;
  margin-left: -0.75px;
  background: #e74c3c;
  border-radius: 4px;
  z-index: 4;
}

.center-point {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #e74c3c;
  z-index: 5;
}

.digital-clock {
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: color 0.3s ease;
  position: relative;
  .opacityTime{
    position: absolute;
    color: white;
  }
}

.dark-mode .digital-clock {
  color: #ecf0f1;
}

.time {
  font-size: 2.5rem;
  font-weight: 600;
  // margin-bottom: 5px;
}

/* 2x1布局的时间样式 */
.digital-layout .time {
  font-size: 1.1rem;
  font-weight: 900;
  // margin-bottom: 2px;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
  max-width: 100%;
}

.digital-layout .opacityTime{
  font-size: 1.1rem;
  font-weight: 900;
  margin-bottom: 2px;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
  max-width: 100%;
}

.dark-mode .digital-layout .time {
  color: #00ff41;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.date {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

/* 2x1布局的日期样式 */
.digital-layout .date {
  font-size: 0.75rem;
  margin-bottom: 0;
  color: #7f8c8d;
  font-weight: 400;
}

.dark-mode .digital-layout .date {
  color: #95a5a6;
}

.timezone {
  font-size: 1rem;
  color: #666;
}

.dark-mode .timezone {
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .time {
    font-size: 2rem;
  }
  
  .date {
    font-size: 1rem;
  }
  
  .timezone {
    font-size: 0.875rem;
  }
}
.size-2x1{
  background: #000000;
}
</style>