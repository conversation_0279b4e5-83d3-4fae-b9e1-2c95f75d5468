<template>
  <div class="swiper-app-grid-container">
    <!-- 空状态提示 -->
    <div v-if="!props.categories || props.categories.length === 0" class="empty-state">
      <div class="empty-content">
        <h3>正在加载分类数据...</h3>
        <p>Categories 长度: {{ props.categories?.length || 0 }}</p>
        <p>Current Category: {{ props.currentCategory }}</p>
      </div>
    </div>

    <!-- Swiper 容器 -->
    <swiper
      v-else
      ref="swiperRef"
      :modules="modules"
      direction="vertical"
      :slides-per-view="1"
      :space-between="0"
      :loop="props.categories.length > 1"
      :allow-touch-move="true"
      :threshold="10"
      :follow-finger="true"
      :grab-cursor="false"
      :simulate-touch="true"
      :touch-start-prevent-default="false"
      :touch-move-stop-propagation="false"
      :prevent-clicks="false"
      :prevent-clicks-propagation="false"
      :slide-to-clicked-slide="false"
      :observer="true"
      :observer-parents="true"
      :observer-slide-children="true"
      :watch-slides-progress="true"
      :watch-slides-visibility="true"
      :initial-slide="currentCategoryIndex"
      @swiper="onSwiper"
      @slide-change="onSlideChange"
      @slide-change-transition-start="onSlideChangeStart"
      @slide-change-transition-end="onSlideChangeEnd"
      @touch-start="onTouchStart"
      @touch-move="onTouchMove"
      @touch-end="onTouchEnd"
      class="category-swiper"
    >
      <!-- 为每个分类创建一个 slide -->
      <swiper-slide
        v-for="(category, index) in categories"
        :key="category.type"
        :data-category="category.type"
        class="category-slide"
      >
        <!-- 应用网格容器 -->
        <div 
          ref="gridContainerRefs"
          class="grid-container"
          :class="`grid-container-${category.type}`"
        >
          <!-- 应用网格 -->
          <div
            class="app-grid"
            :class="`app-grid-${category.type}`"
            :style="gridStyle"
            :key="`grid-${category.type}-${refreshKey}`"
          >
            <!-- 应用项目 -->
            <div 
              v-for="(app, appIndex) in getCategoryApps(category.type)" 
              :key="app.id || `dynamic-app-${appIndex}-${Date.now()}`"
              class="app-item" 
              :class="[
                `size-${app.size?.w || 1}x${app.size?.h || 1}`,
                { 'hidden': !isAppVisible(app) },
                { 'add-icon-item': app.type === 'add-icon' }
              ]" 
              :style="[
                { '--card-color': app.color },
                isEditMode ? {
                  // animation: 'shake-edit ease .3s infinite !important',
                } : {}
              ]"
              :data-app-id="app.id"
              :data-category="category.type"
              @click="handleAppClick(app, $event)"
              @contextmenu.prevent.stop="app.type !== 'add-icon' && handleAppContextMenu(app, $event)"
              @mousedown="handleAppMouseDown(app, $event)"
              :data-id="app.id" :data-size-w="app.size?.w || 1" :data-size-h="app.size?.h || 1"
              :data-is-folder="app.type === 'folder'"
              :data-is-collection="app.type === 'collection' || app.type === 'collect'"
              :data-is-add-icon="app.type === 'add-icon'"
              :draggable="!shouldDisableDrag && app.type !== 'add-icon'"
              @dragstart="!shouldDisableDrag && app.type !== 'add-icon' && handleDragStart(app, $event)"
            >
              <!-- 删除按钮 -->
              <div v-if="isEditMode && app.type !== 'add-icon'" class="delete-icon" @click.stop="handleDeleteApp(app)">
                ×
              </div>

              <!-- 普通应用卡片 -->
              <Tooltip v-if="app.type === 'app'" :title="app.description" placement="right" :mouseEnterDelay="0.5">
                <div class="app-card" @click="handleAppClick(app, $event)">
                  <div class="app-icon" :style="{ 'background-color': app.color || '#ffffff' }">
                    <img v-if="app.icon" style="width: 100%;height: 100%; border-radius: 15px;object-fit: contain"
                      :src="concatUrl(app.icon)" alt="icon" />
                    <div v-else class="app-icon-div">
                      <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                    </div>
                    <div v-if="app.iscanopen == 2" class="newWindow">
                      <svg t="1748943554105" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="5226" width="20" height="20">
                        <path
                          d="M914.285714 914.285714h-804.571428v-804.571428h248.685714V0H109.714286C51.2 0 0 51.2 0 109.714286v797.257143c0 65.828571 51.2 117.028571 109.714286 117.028571h797.257143c65.828571 0 109.714286-51.2 109.714285-109.714286V658.285714h-109.714285v256h7.314285zM629.028571 0v109.714286h204.8L277.942857 665.6l80.457143 80.457143 555.885714-555.885714v204.8H1024V0H629.028571z"
                          fill="#999999" p-id="5227"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="app-name">{{ app.name }}</div>
                </div>
              </Tooltip>

              <!-- 文件夹卡片 -->
              <Tooltip v-else-if="app.type === 'folder'"
                :title="app.description || `拖动应用到此文件夹 (${app.children?.length || 0}个应用)`" placement="right"
                :mouseEnterDelay="0.5">
                <div class="folder-card" @click="handleAppClick(app, $event)">
                  <div class="folder-top">
                    <div v-if="app.children && app.children.length > 0" class="folder-grid"
                      :style="getFolderGridStyle(app)">
                      <div v-for="childApp in getFolderDisplayItems(app)" :key="`folder-preview-${childApp.id}`"
                        class="folder-grid-item">
                        <img v-if="childApp.icon" :src="concatUrl(childApp.icon)" alt="app-icon" />
                        <div v-else class="app-icon-div">
                          <span v-text-scale="childApp.name.slice(0, 6)" class="app-icon-text">{{ childApp.name.slice(0,
                            6) }}</span>
                        </div>
                      </div>
                    </div>
                    <span v-else class="folder-icon">📁</span>
                  </div>
                  <div class="app-name">{{ app.name }} </div>
                </div>
              </Tooltip>

              <!-- 应用合集卡片 -->
              <Tooltip v-else-if="app.type === 'collection' || app.type === 'collect'"
                :title="app.description || `应用合集 (${app.children?.length || 0}个应用)`" placement="right"
                :mouseEnterDelay="0.5">
                <CollectionCard class="collect-card" :collection="app" @open-collection="handleAppClick(app, $event)" />
              </Tooltip>

              <!-- 小组件类型 -->
              <Tooltip v-else-if="app.type === 'card'" :title="app.name" placement="right" :mouseEnterDelay="0.5">
                <component v-if="app.websiteAddress" :is="getComponentByName(app.websiteAddress)" class="card-component"
                  :url="app.icon" :appId="String(app.id)" :title="app.name" :size="app.size || { w: 2, h: 2 }"
                  :headerColor="app.headerColor"></component>
                <div v-else class="card-placeholder">
                  未指定组件名称
                </div>
                <div class="app-name">{{ app.name }}</div>
              </Tooltip>

              <!-- 添加图标应用 -->
              <Tooltip v-else-if="app.type === 'add-icon'" :title="app.description" placement="right" :mouseEnterDelay="0.5">
                <div class="add-icon-card" @click="handleAppClick(app, $event)" @contextmenu.prevent.stop>
                  <div class="add-icon-container">
                    <div class="add-icon-plus">+</div>
                  </div>
                  <div class="app-name">{{ app.name }}</div>
                </div>
              </Tooltip>

              <!-- 其他自定义卡片 -->
              <div class="custom-component-wrapper" v-else-if="app.type && app.type !== 'app'">
                <component :is="getCardComponent(app.type)" class="card-component" :url="app.url" :appId="String(app.id)"
                  :title="app.name" :headerColor="app.headerColor" :size="app.size || { w: 2, h: 2 }" />
                <div class="app-name">{{ app.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <!-- 滑动提示 -->
    <div v-if="showSlideHint" class="slide-hint-container">
      <div class="slide-hint">
        {{ showSlideHint }}
      </div>
    </div>

    <!-- 分类指示器（可选） -->
    <div v-if="showIndicator" class="category-indicator">
      <div 
        v-for="(category, index) in categories"
        :key="category.type"
        class="indicator-dot"
        :class="{ active: index === currentCategoryIndex }"
        @click="slideTo(index)"
      >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Navigation, Pagination, EffectFade, Controller } from 'swiper/modules'
import { Tooltip } from 'ant-design-vue'
import CollectionCard from '@/components/home/<USER>'
import vTextScale from '@/directives/textScale'
import { folderGridCalculator } from '@/utils/folderGridCalculator'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-fade'

// 导入所有需要的组件
import Weather from '@/components/weather/index.vue'
import AnimatedClock from '@/components/time/AnimatedClock.vue'
import WoodenFish from '@/components/woodenFish/WoodenFish.vue'
import RelaxCard from '@/components/relax/RelaxCard.vue'
import IframeCard from '@/components/iframe/IframeCard.vue'
import ImageCard from '@/components/iframe/ImageCard.vue'
import LinkCard from '@/components/iframe/LinkCard.vue'
import VideoCardAdapter from '@/components/video/VideoCardAdapter.vue'
import HotCard from '@/components/hotnet/hotcard.vue'
import CalendarCard from '@/components/calendar/calendarCard.vue'
import TodoList from '@/components/todoList/todoList.vue'
import { useUrlStore } from '@/stores/url'
import { GridUtils } from '@/utils/gridLayoutCalculator'
import '@/views/home.scss'
// Props 定义
const props = defineProps({
  categories: {
    type: Array,
    required: true,
    default: () => []
  },
  currentCategory: {
    type: String,
    required: true
  },
  categoryAppsData: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  showIndicator: {
    type: Boolean,
    default: false
  },
  showSlideHint: {
    type: [String, Boolean],
    default: false
  },
  shouldDisableDrag: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'category-change',
  'app-click',
  'app-context-menu',
  'app-mouse-down',
  'delete-app',
  'update-app'
])

// Swiper 模块
const modules = [Navigation, Pagination, EffectFade, Controller]

// URL Store
const urlStore = useUrlStore()

// 响应式数据
const swiperRef = ref(null)
const gridContainerRefs = ref([])
const refreshKey = ref(0)
const swiperInstance = ref(null)
const isSliding = ref(false)
const touchStartTime = ref(0)

// 网格样式相关
const gridStyle = ref({})
const gridColumns = ref(12)

// 计算属性
const currentCategoryIndex = computed(() => {
  return props.categories.findIndex(cat => cat.type === props.currentCategory)
})

// 方法定义
const onSwiper = (swiper) => {
  swiperInstance.value = swiper
}

const onSlideChange = (swiper) => {
  if (isSliding.value) return
  
  const activeIndex = swiper.realIndex
  const activeCategory = props.categories[activeIndex]
  
  if (activeCategory && activeCategory.type !== props.currentCategory) {
    emit('category-change', activeCategory.type)
  }
}

const onSlideChangeStart = () => {
  isSliding.value = true
}

const onSlideChangeEnd = () => {
  isSliding.value = false
}

const onTouchStart = (swiper, event) => {
  touchStartTime.value = Date.now()
}

const onTouchMove = (swiper, event) => {
  // 可以在这里添加触摸移动的处理逻辑
}

const onTouchEnd = (swiper, event) => {
  const touchDuration = Date.now() - touchStartTime.value
  // 可以根据触摸时长进行不同的处理
}

// 滑动到指定分类
const slideTo = (index, speed = 300) => {
  if (swiperInstance.value && !isSliding.value) {
    swiperInstance.value.slideTo(index, speed)
  }
}

// 滑动到指定分类（通过分类类型）
const slideToCategory = (categoryType, speed = 300) => {
  const index = props.categories.findIndex(cat => cat.type === categoryType)
  if (index !== -1) {
    slideTo(index, speed)
  }
}

// 获取分类应用数据的方法
const getCategoryApps = (categoryType) => {
  // 从 props 中获取应用数据，如果没有则返回空数组
  if (props.categoryAppsData && props.categoryAppsData[categoryType]) {
    const apps = props.categoryAppsData[categoryType]

    // 在每个分类末尾添加"添加图标"应用
    const appsWithAddIcon = [...apps]

    // 创建"添加图标"应用对象
    const addIconApp = {
      id: `add-icon-${categoryType}`,
      name: '添加应用',
      type: 'add-icon',
      description: '点击添加新的应用',
      size: { w: 1, h: 1 },
      category: categoryType
    }

    appsWithAddIcon.push(addIconApp)
    return appsWithAddIcon
  }

  // 如果没有应用数据，至少返回添加图标
  return [{
    id: `add-icon-${categoryType}`,
    name: '添加应用',
    type: 'add-icon',
    description: '点击添加新的应用',
    size: { w: 1, h: 1 },
    category: categoryType
  }]
}

// 应用可见性检查
const isAppVisible = (app) => {
  // 检查应用是否应该显示
  if (!app) return false
  if (app.hidden === true) return false
  if (app.visible === false) return false
  return true
}

// 组件名称获取 - 直接返回组件引用
const getComponentByName = (componentName) => {
  // 组件映射表，将组件名字符串映射到导入的组件
  const componentsMap = {
    'VideoCardAdapter': VideoCardAdapter,
    'Weather': Weather,
    'AnimatedClock': AnimatedClock,
    'WoodenFish': WoodenFish,
    'RelaxCard': RelaxCard,
    'IframeCard': IframeCard,
    'ImageCard': ImageCard,
    'LinkCard': LinkCard,
    'HotCard': HotCard,
    'CalendarCard': CalendarCard,
    'TodoList': TodoList
  }

  return componentsMap[componentName] || null
}

// 根据卡片类型获取对应的组件
const getCardComponent = (type) => {
  const componentMap = {
    'weather': Weather,
    'clock': AnimatedClock,
    'woodenFish': WoodenFish,
    'RelaxCard': RelaxCard,
    'iframe': IframeCard,
    'ImageCard': ImageCard,
    'imageCard': ImageCard,
    'linkCard': LinkCard,
    'video': VideoCardAdapter,
    'HotCard': HotCard,
    'calendar': CalendarCard,
    'todo': TodoList
  }

  return componentMap[type] || null
}

const getDefaultComponentName = (type) => {
  // 根据类型返回默认组件名称
  const typeMap = {
    'video': 'VideoCardAdapter',
    'weather': 'Weather',
    'clock': 'AnimatedClock',
    'woodenFish': 'WoodenFish',
    'RelaxCard': 'RelaxCard',
    'iframe': 'IframeCard',
    'imageCard': 'ImageCard',
    'ImageCard': 'ImageCard',
    'linkCard': 'LinkCard',
    'HotCard': 'HotCard',
    'calendar': 'CalendarCard',
    'todo': 'TodoList'
  }

  return typeMap[type] || type
}

// 事件处理方法
const handleAppClick = (app, event) => {
  emit('app-click', app, event)
}

const handleAppContextMenu = (app, event) => {
  emit('app-context-menu', app, event)
}

const handleAppMouseDown = (app, event) => {
  emit('app-mouse-down', app, event)
}

const handleDragStart = (app, event) => {
  emit('app-mouse-down', app, event) // Can be repurposed for drag start
}

const handleDeleteApp = (app) => {
  emit('delete-app', app)
}

const updateAppData = (data) => {
  emit('update-app', data)
}

const handleImageError = (event) => {
  // 处理图片加载错误
  console.warn('应用图标加载失败:', event.target.src)
}

// URL 拼接方法
const concatUrl = (path) => {
  return urlStore.concatUrl(path)
}

function calculateFolderMaxItems(folder) {
  if (!folder || !folder.children) return 3
  const itemCount = folder.children.length
  const folderSize = folder.size || { w: 2, h: 2 }
  const containerWidth = 120
  const containerHeight = 120
  const layout = folderGridCalculator.calculateOptimalLayout(
    containerWidth,
    containerHeight,
    itemCount,
    folderSize
  )
  return layout.maxItems
}

function getFolderDisplayItems(folder) {
  if (!folder || !folder.children) return []
  const maxItems = calculateFolderMaxItems(folder)
  return folder.children.slice(0, maxItems)
}

function getFolderGridStyle(folder) {
  const folderSize = folder?.size || { w: 2, h: 2 }
  if (!folder || !folder.children || folder.children.length === 0) {
    let widthPercent, heightPercent
    if (folderSize.w === 1 && folderSize.h === 1) {
      widthPercent = '70%'
      heightPercent = '70%'
    } else if (folderSize.h === 1) {
      widthPercent = '80%'
      heightPercent = '80%'
    } else {
      widthPercent = '85%'
      heightPercent = '85%'
    }
    const columns = (folderSize.w === 3 && folderSize.h === 1) ? 4 : 3
    return {
      'display': 'grid',
      'grid-template-columns': `repeat(${columns}, 1fr)`,
      'grid-template-rows': 'repeat(1, 1fr)',
      'gap': '6px',
      'width': widthPercent,
      'height': heightPercent,
      'padding': '8px',
      'place-items': 'center',
      'place-content': 'start'
    }
  }
  const itemCount = folder.children.length
  const containerWidth = 120
  const containerHeight = 120
  const layout = folderGridCalculator.calculateOptimalLayout(
    containerWidth,
    containerHeight,
    itemCount,
    folderSize
  )
  return {
    'display': 'grid',
    'grid-template-columns': `repeat(${layout.columns}, 1fr)`,
    'grid-template-rows': `repeat(${layout.rows}, 1fr)`,
    'gap': `${layout.gap}px`,
    'width': layout.gridStyle.width,
    'height': layout.gridStyle.height,
    'padding': '8px',
    'place-items': 'center',
    'place-content': 'start',
    'box-sizing': 'border-box'
  }
}

// 计算网格样式
const calculateGridStyle = async () => {
  try {
    const result = await GridUtils.quickCalculate({
      isMobile: false // 可以根据需要传入移动端检测
    })

    gridStyle.value = result.gridStyle
    gridColumns.value = result.N

    return result
  } catch (error) {
    console.error('SwiperAppGrid 网格样式计算失败:', error)
    // 使用默认样式
    gridStyle.value = {
      'grid-template-columns': 'repeat(12, 1fr)',
      'grid-auto-rows': '80px',
      'gap': '15px',
      'max-width': '100%',
      'margin': '0 auto'
    }
    gridColumns.value = 12
  }
}

// 监听当前分类变化，同步 Swiper
watch(() => props.currentCategory, (newCategory, oldCategory) => {
  if (newCategory !== oldCategory && swiperInstance.value && !isSliding.value) {
    const index = props.categories.findIndex(cat => cat.type === newCategory)
    if (index !== -1 && index !== swiperInstance.value.realIndex) {
      slideTo(index, 300)
    }
  }
})

// 监听分类数据变化，刷新网格
watch(() => props.categories, (newCategories, oldCategories) => {
  refreshKey.value++
  nextTick(() => {
    if (swiperInstance.value) {
      swiperInstance.value.update()
    }
  })
}, { deep: true, immediate: true })

// 暴露方法给父组件
defineExpose({
  slideTo,
  slideToCategory,
  getSwiperInstance: () => swiperInstance.value,
  refresh: () => {
    refreshKey.value++
    nextTick(() => {
      if (swiperInstance.value) {
        swiperInstance.value.update()
      }
    })
  }
})

// 生命周期
onMounted(async () => {

  // 初始化网格样式
  await calculateGridStyle()

  // 监听窗口大小变化，重新计算网格样式
  const cleanupGridWatcher = GridUtils.watchResize(async (result) => {
    await calculateGridStyle()

    // 刷新 Swiper
    nextTick(() => {
      if (swiperInstance.value) {
        swiperInstance.value.update()
      }
    })
  }, 150, true) // 150ms防抖，启用自动文字样式应用
})

// 在组件卸载时清理监听器
onUnmounted(() => {
  if (cleanupGridWatcher) {
    cleanupGridWatcher()
  }
  if (swiperInstance.value) {
    swiperInstance.value.destroy(true, true)
  }
})
</script>

<style scoped>
.swiper-app-grid-container, .category-swiper {
  height: 100%;
}
</style>
