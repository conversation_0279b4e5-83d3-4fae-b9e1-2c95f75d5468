<template>
  <div
    class="video-player-container"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 视频播放器，始终存在以保持播放状态 -->
    <div class="video-wrapper" :class="{ 'video-hidden': !isHovering }">
      <video
        ref="videoRef"
        :src="videoSource"
        controls
        @ended="handleVideoEnded"
        @loadeddata="handleVideoLoaded"
      ></video>
    </div>

    <!-- 默认背景，当鼠标不在组件内时显示 -->
    <div class="cover-image" :class="{ 'cover-hidden': isHovering }">
      <img :src="coverImage" alt="视频封面" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'

// 定义组件属性
const props = defineProps({
  // 视频链接
  videoSource: {
    type: String,
    default: ''
  },
  // 封面图片
  coverImage: {
    type: String,
    required: true
  }
})

// 定义emit事件
const emit = defineEmits(['mouse-enter', 'mouse-leave', 'video-ended'])

// 鼠标悬停状态
const isHovering = ref(false)

// 视频元素引用
const videoRef = ref(null)

// 定时器ID，用于防抖动
let enterTimerId = null
let leaveTimerId = null

// 防抖动间隔（毫秒）
const debounceDelay = 300

// 正在播放操作中的标志
let isPlayingOperation = false

// 视频是否已加载完成
const isVideoLoaded = ref(false)

// 是否应该播放（鼠标悬停且视频已加载）
const shouldPlay = ref(false)

// 视频加载完成事件处理
const handleVideoLoaded = () => {
  console.log('视频加载完成')
  isVideoLoaded.value = true

  // 如果鼠标正在悬停且应该播放，则开始播放
  if (shouldPlay.value && isHovering.value) {
    playVideo()
  }
}

// 播放视频的统一方法
const playVideo = () => {
  if (videoRef.value && !isPlayingOperation && props.videoSource && props.videoSource.trim() !== '') {
    console.log('开始播放视频')
    isPlayingOperation = true

    videoRef.value.play()
      .then(() => {
        isPlayingOperation = false
        console.log('视频播放成功')
      })
      .catch(err => {
        isPlayingOperation = false
        // 如果是自动播放策略阻止，则不视为错误，让用户手动点击播放
        if (err.name === 'NotAllowedError') {
          console.log('自动播放被浏览器阻止，需要用户手动播放')
        } else {
          console.error('无法播放视频:', err)
        }
      })
  }
}

// 鼠标进入事件处理
const handleMouseEnter = () => {
  // 清除离开定时器
  if (leaveTimerId) {
    clearTimeout(leaveTimerId)
    leaveTimerId = null
  }

  // 设置进入定时器
  if (enterTimerId) clearTimeout(enterTimerId)

  enterTimerId = setTimeout(() => {
    console.log('鼠标移入视频区域')
    // 设置悬停状态
    isHovering.value = true
    shouldPlay.value = true

    // 发射鼠标移入事件给父组件
    emit('mouse-enter')

    // 如果视频已加载，立即播放；否则等待加载完成
    if (isVideoLoaded.value) {
      playVideo()
    } else {
      console.log('视频尚未加载完成，等待加载')
    }
  }, debounceDelay)
}

// 鼠标离开事件处理
const handleMouseLeave = () => {
  // 清除进入定时器
  if (enterTimerId) {
    clearTimeout(enterTimerId)
    enterTimerId = null
  }

  // 设置离开定时器
  if (leaveTimerId) clearTimeout(leaveTimerId)

  leaveTimerId = setTimeout(() => {
    console.log('鼠标离开视频区域')
    isHovering.value = false
    shouldPlay.value = false

    // 发射鼠标离开事件给父组件
    emit('mouse-leave')

    // 如果视频正在播放，暂停它
    if (videoRef.value && !videoRef.value.paused && !isPlayingOperation) {
      console.log('暂停视频播放')
      videoRef.value.pause()
    }
  }, debounceDelay)
}

// 视频结束事件处理
const handleVideoEnded = () => {
  console.log('视频播放结束')

  // 发射视频结束事件，让父组件处理视频切换
  // 不重置isHovering状态，保持真实的鼠标悬停状态
  emit('video-ended')
}

// 监听视频源变化
watch(() => props.videoSource, (newSource, oldSource) => {
  console.log('视频源发生变化:', { oldSource, newSource })

  // 重置视频加载状态
  isVideoLoaded.value = false
  shouldPlay.value = false

  // 如果视频正在播放，先暂停
  if (videoRef.value && !videoRef.value.paused) {
    console.log('视频源变化，暂停当前播放')
    videoRef.value.pause()
  }

  // 如果新的视频源为空，确保视频停止
  if (!newSource || newSource.trim() === '') {
    console.log('视频源为空，确保视频停止')
    if (videoRef.value) {
      videoRef.value.pause()
      videoRef.value.currentTime = 0
    }
  }
})

// 组件挂载时的处理
onMounted(() => {
  console.log('VideoPlayer组件挂载')
})

// 组件卸载时清理定时器
onUnmounted(() => {
  console.log('VideoPlayer组件卸载')
  if (enterTimerId) clearTimeout(enterTimerId)
  if (leaveTimerId) clearTimeout(leaveTimerId)
})
</script>

<style scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
}

.cover-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  z-index: 2;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.cover-image.cover-hidden {
  opacity: 0;
  pointer-events: none;
}

.cover-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.video-wrapper.video-hidden {
  opacity: 0;
  pointer-events: none;
}

.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>