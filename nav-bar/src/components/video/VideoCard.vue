<template>
  <div class="video-card" @wheel="handleWheel">
    <VideoPlayer
      ref="videoPlayerRef"
      :videoSource="videoUrl"
      :coverImage="defaultBg"
      @mouse-enter="handleVideoMouseEnter"
      @mouse-leave="handleVideoMouseLeave"
      @video-ended="handleVideoEnded"
    />
    
    <!-- 设置按钮，放在右上角 -->
        <button class="settings-button" @click="showSettings = !showSettings">
          <span class="settings-icon">⚙️</span>
        </button>
    
    <!-- 加载指示器，只在真正加载时显示 -->
    <div v-if="isLoading && (!videoUrl || isRequesting)" class="loading-indicator">
      <a-spin tip="视频加载中..." />
    </div>
    
    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-panel">
      <div class="settings-form">
        <!-- 预设视频选择 -->
        <div class="form-group">
          <label>预设视频链接</label>
          <div class="preset-videos">
            <a-select 
              v-model:value="selectedPreset" 
              style="width: 100%"
              placeholder="选择预设视频"
              @change="handlePresetChange"
              :loading="isLoading"
            >
              <a-select-option 
                v-for="(video, index) in presetVideos" 
                :key="index" 
                :value="video.url"
              >
                {{ video.name }}-{{ video.from }}
              </a-select-option>
            </a-select>
          </div>
        </div>
        <span>视频中鼠标下滑切换视频</span>


        
        <div class="form-actions">
          <button @click="saveSettings" class="save-btn">保存设置</button>
          <button @click="showSettings = false" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import VideoPlayer from './VideoPlayer.vue'
import { Select, Spin, message } from 'ant-design-vue'
import axios from 'axios'

// ant-design-vue 组件
const ASelect = Select
const ASelectOption = Select.Option
const ASpin = Spin

// 导入默认背景图片
import defaultBg from '@/assets/videobg4.png'
// 随机微视
const randomWeishi = 'https://v.api.aa1.cn/api/api-vs/index.php'

// 热点视频API
const HOT_VIDEO_API = 'https://api.4qb.cn/api/suiji-sp?msg=%E9%A3%8E%E6%99%AF&type=json'
// 明星视频API
const STAR_VIDEO_API = 'https://api.4qb.cn/api/suiji-sp?msg=%E6%98%8E%E6%98%9F&type=json'
// 动物
const ANIMAL_VIDEO_API = 'https://api.4qb.cn/api/suiji-sp?msg=%E5%8A%A8%E7%89%A9&type=json'
// 动漫
const DM_VIDEO_API = 'https://api.4qb.cn/api/suiji-sp?msg=%E5%8A%A8%E6%BC%AB&type=json'
// boy
const HANDSOME_BOY_API = 'https://api.52vmy.cn/api/video/boy'
// 清纯
const QC_VIDEO_URL = 'https://www.yviii.com/video/i.php'
// 美女视频
const BEAUTY_VIDEO_URL = 'https://api.mmp.cc/api/ksvideo?type=mp4&id=jk'
// 美女视频(1)
const EEENAV_Grils = 'https://api.e1e1.top/crosswise/cw.php'
// 帅哥视频
const HANDSOME_Boys = 'https://api.e1e1.top/xgg/g.php'

// 预设视频链接列表
const presetVideos = [
  { name: '随机微视', url: 'random_weishi', isApiSource: true, api: randomWeishi, from: '夏柔公益API' },
  { name: '美女视频', url: 'beauty_video', isApiSource: true, api: BEAUTY_VIDEO_URL, from: '远梦API', alwaysRefresh: true },
  { name: '美女视频', url: 'EEENAV_Grils', isApiSource: true, api: EEENAV_Grils, from: 'EEENAV', alwaysRefresh: true },
  { name: '帅哥视频', url: 'HANDSOME_Boys', isApiSource: true, api: HANDSOME_Boys, from: 'EEENAV' },
  { name: '随机视频', url: 'qc_video', isApiSource: true, api: QC_VIDEO_URL, from: 'www.yviii.com', alwaysRefresh: true },
  { name: '美女视频', url: 'handsome_boy', isApiSource: true, api: HANDSOME_BOY_API, from: '维梦API' },
  { name: '风景视频', url: 'hot_video', isApiSource: true, api: HOT_VIDEO_API, from: '小职API' }, 
  { name: '明星视频', url: 'star_video', isApiSource: true, api: STAR_VIDEO_API, from: '小职API' }, 
  { name: '动物视频', url: 'animal_video', isApiSource: true, api: ANIMAL_VIDEO_API, from: '小职API' }, 
  { name: '动漫视频', url: 'dm_video', isApiSource: true, api: DM_VIDEO_API, from: '小职API' }, 
]

// 定义组件属性
const props = defineProps({
  appId: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    default: '视频播放器'
  },
  url: {
    type: String,
    default: ''
  },
  headerColor: {
    type: String,
    default: '#1890ff'
  }
})

// 视频URL，默认风景视频
const videoUrl = ref('')

// 当前选择的预设，默认风景视频
const selectedPreset = ref('random_weishi')

// 当前选择的API视频类型，默认风景视频
const currentApiType = ref('random_weishi')

// 是否是热点视频来源，默认true
const isHotVideoSource = ref(true)

// 自定义视频链接列表
const customVideoLinks = ref([])

// 自定义链接名称输入
const customLinkName = ref('')

// 设置面板显示状态
const showSettings = ref(false)

// 加载状态
const isLoading = ref(false)

// 防止重复请求的标志
const isRequesting = ref(false)

// 上次请求时间戳
const lastRequestTime = ref(0)

// 请求冷却时间（毫秒）
const REQUEST_COOLDOWN = 2000

// 请求计数器 - 用于调试
let requestCounter = 0

// 是否最近移出了视频区域
const recentlyLeft = ref(false)

// 防抖定时器ID
let debounceTimerId = null

// 滚动提示显示状态
const showSlideHint = ref(false)

// 滚动事件节流定时器
let wheelThrottleTimer = null

// 滚动事件切换冷却时间（毫秒）
const WHEEL_COOLDOWN = 1000

// 上次滚动切换时间
const lastWheelChangeTime = ref(0)

// 滚动切换状态标志 - 防止滚动时鼠标移入触发重复加载
const isScrolling = ref(false)

// VideoPlayer组件引用
const videoPlayerRef = ref(null)

// 本地存储键
const storageKey = computed(() => `video-card-${props.appId}`)

/**
 * 获取API视频链接
 * @param {string} apiUrl API地址
 * @returns {Promise<string>} 返回视频URL
 */
const fetchApiVideo = async (apiUrl) => {
  const startTime = Date.now()

  try {
    const response = await axios.get(apiUrl, {
      timeout: 10000, // 10秒超时
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })


    // 获取视频URL
    const videoUrl = (response.data?.data?.url ||
                    response.data?.data?.video ||
                    response.data?.url ||
                    response.data).replace('http://', 'https://')

    // 检查URL是否为空
    if (!videoUrl || videoUrl === 'https://') {
      console.warn('API返回了空的视频URL，使用备用视频')
      return QC_VIDEO_URL
    }

    const duration = Date.now() - startTime
    return videoUrl
  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`获取API视频失败，耗时: ${duration}ms，错误:`, error.message)
    return QC_VIDEO_URL
  } finally {
    lastRequestTime.value = Date.now()
  }
}

/**
 * 处理预设视频选择变化
 * @param {string} value 选中的预设视频URL
 */
const handlePresetChange = async (value) => {
  
  
  // 查找选中的预设视频
  const selectedVideo = presetVideos.find(video => video.url === value)
  if (!selectedVideo) return
  
  // 设置当前API类型和热点视频源标志
  currentApiType.value = selectedVideo.url
  isHotVideoSource.value = selectedVideo.isApiSource
  
  // 如果是API视频源或者标记为总是刷新，需要请求
  if (selectedVideo.isApiSource || selectedVideo.alwaysRefresh) {
    try {
      if (isRequesting.value) return
      
      isRequesting.value = true
      videoUrl.value = ''
      isLoading.value = true
      
      // 获取API视频URL
      const apiVideoUrl = await fetchApiVideo(selectedVideo.api)
      videoUrl.value = apiVideoUrl
    } catch (error) {
      console.error('获取视频失败:', error)
      videoUrl.value = QC_VIDEO_URL
    } finally {
      isRequesting.value = false
      isLoading.value = false
    }
  } else {
    // 非API视频源，直接使用api作为URL
    videoUrl.value = selectedVideo.api
  }
}

/**
 * 优化防抖函数，支持取消
 */
const debounce = (fn, delay) => {
  if (debounceTimerId) clearTimeout(debounceTimerId)
  debounceTimerId = setTimeout(fn, delay)
}

/**
 * 获取实际视频URL
 * @param {string} presetKey 预设视频的URL
 * @returns {string|null} 返回视频URL或null
 */
const getActualVideoUrl = (presetKey) => {
  const preset = presetVideos.find(v => v.url === presetKey)
  if (!preset) return null
  
  return preset.isApiSource ? null : preset.api
}

/**
 * 确保重置加载状态
 */
const resetLoadingState = () => {
  isLoading.value = false;
  isRequesting.value = false;
}

/**
 * 鼠标移入视频区域时
 * 修改：只在真正没有视频URL时才请求新视频，已有视频时直接恢复播放
 */
const handleVideoMouseEnter = () => {
  // 重置移出标志
  recentlyLeft.value = false

  // 如果正在滚动切换视频，跳过鼠标移入的视频加载逻辑
  if (isScrolling.value) {
    return
  }

  // 如果已经有视频URL，直接恢复播放，无需重新请求
  if (videoUrl.value && videoUrl.value.trim() !== '') {
    return
  }

  // 只有在没有视频URL时才需要请求新视频

  // 检查当前预设是否存在
  const currentPreset = presetVideos.find(v => v.url === currentApiType.value);
  if (!currentPreset) {
    return
  }

  // 设置加载状态
  isLoading.value = true

  // 清理任何现有定时器
  if (debounceTimerId) {
    clearTimeout(debounceTimerId);
    debounceTimerId = null;
  }

  // 设置新定时器
  debounceTimerId = setTimeout(async () => {
    // 检查是否已移出或正在请求中
    if (recentlyLeft.value) {
      resetLoadingState();
      return;
    }

    if (isRequesting.value) {
      return;
    }

    // 获取对应预设
    const apiVideo = presetVideos.find(v => v.url === currentApiType.value)
    if (!apiVideo || !apiVideo.api) {
      resetLoadingState();
      return;
    }

    try {
      isRequesting.value = true;

      const apiVideoUrl = await fetchApiVideo(apiVideo.api);

      // 检查是否已移出
      if (recentlyLeft.value) {
        resetLoadingState();
        return;
      }

      // 设置视频URL
      videoUrl.value = apiVideoUrl;

    } catch (e) {
      console.error('获取视频失败:', e);
      if (!recentlyLeft.value) {
        videoUrl.value = QC_VIDEO_URL;
      }
    } finally {
      // 重置请求状态
      isRequesting.value = false;

      // 只有在未移出时才重置加载状态
      if (!recentlyLeft.value) {
        // 添加100ms延迟确保视频有时间加载
        setTimeout(() => {
          isLoading.value = false;
        }, 100);
      }
    }
  }, 200);
}

/**
 * 鼠标移出视频区域时
 * 修改：不再清空视频URL，改为通过VideoPlayer组件的暂停机制控制播放
 */
const handleVideoMouseLeave = () => {
  // 设置移出标志
  recentlyLeft.value = true

  // 取消定时器
  if (debounceTimerId) {
    clearTimeout(debounceTimerId)
    debounceTimerId = null
  }

  // 移除清空URL的逻辑，保持视频URL以便暂停/恢复播放
  // 注释掉原来的清空逻辑：
  // if (isHotVideoSource.value) {
  //   videoUrl.value = ''
  // }

  // 确保关闭加载状态
  resetLoadingState();
}

/**
 * 保存当前URL为自定义链接
 */
const saveAsCustomLink = () => {
  // 检查URL不为空
  if (!videoUrl.value.trim()) return
  
  // 检查链接是否已存在
  const exists = customVideoLinks.value.some(link => link.url === videoUrl.value)
  if (!exists) {
    customVideoLinks.value.push({
      name: customLinkName.value || `自定义链接 ${customVideoLinks.value.length + 1}`,
      url: videoUrl.value
    })
    // 清空名称输入
    customLinkName.value = ''
  }
}

/**
 * 选择自定义链接
 */
const selectCustomLink = (link) => {
  videoUrl.value = link.url
  isHotVideoSource.value = false
  currentApiType.value = ''
}

/**
 * 删除自定义链接
 */
const removeCustomLink = (index) => {
  customVideoLinks.value.splice(index, 1)
}

// 加载保存的设置，默认风景视频
const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem(storageKey.value)
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings)
      
      // 加载自定义链接列表
      if (parsedSettings.customLinks && Array.isArray(parsedSettings.customLinks)) {
        customVideoLinks.value = parsedSettings.customLinks
      }
      
      // 恢复保存的预设和视频源类型
      const savedPreset = parsedSettings.currentApiType || 'random_weishi'
      selectedPreset.value = savedPreset
      
      // 查找对应的预设
      const matchedPreset = presetVideos.find(preset => preset.url === savedPreset)
      if (matchedPreset) {
        currentApiType.value = matchedPreset.url
        isHotVideoSource.value = matchedPreset.isApiSource
        
        // 如果保存了视频URL，使用它，否则按预设类型处理
        if (parsedSettings.videoUrl) {
          videoUrl.value = parsedSettings.videoUrl
        } else if (!matchedPreset.isApiSource) {
          // 非API视频源，直接使用api作为URL
          videoUrl.value = matchedPreset.api
        }
      } else {
        // 没找到匹配的预设，使用风景视频
        selectedPreset.value = 'random_weishi'
        currentApiType.value = 'random_weishi'
        isHotVideoSource.value = true
        videoUrl.value = ''
      }
    } else {
      // 默认风景视频
      selectedPreset.value = 'random_weishi'
      currentApiType.value = 'random_weishi'
      isHotVideoSource.value = true
      videoUrl.value = ''
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    selectedPreset.value = 'random_weishi'
    currentApiType.value = 'random_weishi'
    isHotVideoSource.value = true
    videoUrl.value = ''
  }
}

// 保存设置
const saveSettings = () => {
  try {
    // 如果是非API源但URL为空，使用预设URL
    if (!isHotVideoSource.value && (!videoUrl.value || videoUrl.value.trim() === '')) {
      const preset = presetVideos.find(v => v.url === currentApiType.value)
      if (preset) {
        videoUrl.value = preset.api
      }
    }
    
    // 保存到本地存储
    const settingsToSave = {
      videoUrl: videoUrl.value,
      customLinks: customVideoLinks.value,
      isHotVideoSource: isHotVideoSource.value,
      currentApiType: currentApiType.value
    }
    
    localStorage.setItem(storageKey.value, JSON.stringify(settingsToSave))
    showSettings.value = false
    
  } catch (error) {
    console.error('保存视频设置失败:', error)
    message.error('保存设置失败')
  }
}

// 组件挂载时，自动加载风景视频
onMounted(() => {
  loadSettings()
  
  // 如果是API视频源且没有视频URL，自动请求
  if (isHotVideoSource.value && !videoUrl.value) {
    
    setTimeout(() => handleVideoMouseEnter(), 100)
  } else if (!isHotVideoSource.value && !videoUrl.value) {
    // 如果是非API视频源但URL为空，获取实际URL
    const actualUrl = getActualVideoUrl(currentApiType.value)
    if (actualUrl) {
      
      videoUrl.value = actualUrl
    }
  }
})

// 组件卸载时清理定时器和状态
onUnmounted(() => {
  if (debounceTimerId) clearTimeout(debounceTimerId)
  if (wheelThrottleTimer) clearTimeout(wheelThrottleTimer)
  // 重置滚动状态
  isScrolling.value = false
})

// 监听ID变化，以便在不同实例间切换时加载正确的设置
watch(() => props.appId, (newId) => {
  loadSettings()
})

// 监听props中url的变化
watch(() => props.url, (newUrl) => {
  if (newUrl && !videoUrl.value) {
    videoUrl.value = newUrl
  }
})

// 监听videoUrl变化，确保加载状态正确
watch(() => videoUrl.value, (newUrl) => {
  // 如果有新的视频URL且不是API请求状态，确保关闭加载动画
  if (newUrl && !isRequesting.value) {
    // 给视频加载留出一点时间
    setTimeout(() => {
      isLoading.value = false;
    }, 300);
  }
});

/**
 * 处理滚轮事件，实现向下滑动切换视频
 * 增强：添加更严格的防重复机制
 * @param {WheelEvent} event 滚轮事件对象
 */
const handleWheel = (event) => {
  // 防止页面滚动
  event.preventDefault()

  // 获取当前时间
  const now = Date.now()


  // 检查是否在冷却期
  if (now - lastWheelChangeTime.value < WHEEL_COOLDOWN) {
    return
  }

  // 如果正在请求中，不允许新的滚动切换
  if (isRequesting.value) {
    return
  }

  // 如果正在加载中，也不允许新的滚动切换
  if (isLoading.value) {
    return
  }

  // 向下滚动时切换视频
  if (event.deltaY > 0) {

    // 立即设置滚动状态，防止鼠标移入事件触发重复加载
    isScrolling.value = true

    // 立即更新最后切换时间，防止重复触发
    lastWheelChangeTime.value = now

    // 显示滑动提示
    showSlideHint.value = true

    // 清除之前的提示定时器
    if (wheelThrottleTimer) {
      clearTimeout(wheelThrottleTimer)
    }

    // 设置提示消失定时器
    wheelThrottleTimer = setTimeout(() => {
      showSlideHint.value = false
    }, 2000)

    // 获取当前选中的预设视频
    const currentPreset = presetVideos.find(v => v.url === currentApiType.value)
    if (!currentPreset) {
      isScrolling.value = false // 重置滚动状态
      return
    }

    // 不论是API源还是直接URL，都调用loadVideoFromCurrentPreset
    loadVideoFromCurrentPreset()
  } else {
  }
}

/**
 * 处理视频播放结束事件
 */
const handleVideoEnded = () => {

  // 视频结束后自动加载下一个视频
  // 使用与滚动切换相同的逻辑，但不显示滑动提示
  loadVideoFromCurrentPreset()
}

/**
 * 检查并恢复播放状态
 * 在滚动切换视频完成后，如果鼠标仍在视频区域内，恢复播放状态
 */
const checkAndRestorePlayState = () => {
  // 检查鼠标是否仍在视频区域内（通过recentlyLeft标志判断）
  if (!recentlyLeft.value && videoUrl.value && videoUrl.value.trim() !== '') {

    // 给VideoPlayer一些时间来处理新的视频源，然后触发鼠标移入事件
    setTimeout(() => {
      if (videoPlayerRef.value && !recentlyLeft.value) {
        // 获取VideoPlayer的DOM元素并触发鼠标移入事件
        const playerElement = videoPlayerRef.value.$el
        if (playerElement) {
          const mouseEnterEvent = new MouseEvent('mouseenter', {
            bubbles: true,
            cancelable: true,
            view: window
          })
          playerElement.dispatchEvent(mouseEnterEvent)
        }
      }
    }, 200)
  }
}

/**
 * 从当前选中的预设加载视频
 * 重构：移除复杂的Promise链，使用简单的状态锁定机制
 */
const loadVideoFromCurrentPreset = async () => {
  const currentRequestId = ++requestCounter

  // 立即检查是否正在请求中，防止并发调用
  if (isRequesting.value) {
    // 如果是滚动触发的请求被跳过，需要重置滚动状态
    if (isScrolling.value) {
      isScrolling.value = false
    }
    return
  }

  // 立即设置请求状态锁，防止其他调用进入
  isRequesting.value = true

  try {
    // 获取当前选中的预设
    const currentPreset = presetVideos.find(v => v.url === currentApiType.value)
    if (!currentPreset) {
      return
    }

    // 设置加载状态
    videoUrl.value = ''
    isLoading.value = true


    // 如果是API源或者被标记为总是需要刷新的直接URL
    if (currentPreset.isApiSource || currentPreset.alwaysRefresh) {
      // API源视频需要发送请求
      const apiVideoUrl = await fetchApiVideo(currentPreset.api)
      videoUrl.value = apiVideoUrl
    } else {
      // 非API源视频直接使用URL
      videoUrl.value = currentPreset.api
    }

  } catch (error) {
    console.error(`[请求${currentRequestId}] 获取视频失败:`, error)
    // 出错时使用备用视频
    videoUrl.value = QC_VIDEO_URL
  } finally {
    // 确保状态重置
    isRequesting.value = false

    // 延迟重置状态，确保视频有时间加载
    setTimeout(() => {
      isLoading.value = false

      // 重置滚动状态（如果是滚动触发的请求）
      if (isScrolling.value) {
        isScrolling.value = false

        // 滚动切换完成后，检查是否需要恢复播放状态
        checkAndRestorePlayState()
      }

    }, 300)
  }
}
</script>

<style lang="scss" scoped>
.video-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  background-color: transparent;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.video-card:hover{
  opacity: 1 !important;
}
.settings-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-card:hover .settings-button {
  opacity: 1;
}

.settings-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.settings-icon {
  font-size: 16px;
  color: white;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 20;
  padding: 15px;
  overflow-y: auto;
  color: white;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.preset-videos {
  margin-bottom: 10px;
}

.form-group input[type="text"], .text-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  color: #333;
}

.form-group input[type="text"]::placeholder, .text-input::placeholder {
  color: #999;
}

.save-custom-link {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.add-btn {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #52c41a;
  color: white;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.custom-links-container {
  margin-top: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 10px;
}

.custom-links-header {
  margin-bottom: 8px;
}

.custom-links-list {
  max-height: 150px;
  overflow-y: auto;
}

.custom-link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }

  .link-name {
    cursor: pointer;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      text-decoration: underline;
      color: #1890ff;
    }
  }

  .link-delete {
    cursor: pointer;
    color: #ff4d4f;
    font-size: 16px;
    margin-left: 8px;

    &:hover {
      color: #ff7875;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.save-btn, .cancel-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}

.save-btn {
  background-color: #1890ff;
  color: white;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.slide-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 15;
  animation: fadeInOut 2s ease-in-out;
  white-space: nowrap;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
</style>