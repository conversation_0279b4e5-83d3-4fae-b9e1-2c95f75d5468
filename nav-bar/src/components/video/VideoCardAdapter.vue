<template>
  <VideoCard 
    :appId="appId"
    :title="title"
    :url="url"
    :headerColor="headerColor"
  />
</template>

<script setup>
import VideoCard from './VideoCard.vue'

// 定义组件属性
const props = defineProps({
  appId: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    default: '视频播放器'
  },
  url: {
    type: String,
    default: ''
  },
  headerColor: {
    type: String,
    default: '#1890ff'
  }
})
</script>

<style scoped>
/* 该组件不需要额外样式，所有样式由VideoCard组件提供 */
</style>