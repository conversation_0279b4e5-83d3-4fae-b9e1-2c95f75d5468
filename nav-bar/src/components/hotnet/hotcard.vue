<template>
    <div class="hotnet-container size-2x1" v-if="gridStyle == '2x1'" @click.stop="openAllPlatformsModal">
      网络热搜
    </div>
    <div class="hotnet-container size-2x2" v-else-if="gridStyle == '2x2'" @click.stop="openAllPlatformsModal">
      <div class="hotnet-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
        
        <ul v-else class="hotnet-list">
          <li v-for="item in displayItems" :key="item.index" class="hotnet-item">
            <!-- <div :class="['hotnet-index']" :style="calebackgroundColor">{{ item.index }}</div> -->
            <div class="hotnet-info">
              <a :href="item.url" class="hotnet-title-link" target="_blank" @click.stop>{{ item.index }}.{{ item.title }}</a>
              <span class="hot-value" :style="caleColor">
                <svg width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.92885 2.14213C5.65177 2.52672 5.41781 3.61713 5.5001 4.30984C4.58906 3.17859 4.62635 1.87755 4.62635 0.00317383C1.70427 1.16672 2.38385 4.52172 2.29656 5.54047C1.56156 4.90505 1.4226 3.38713 1.4226 3.38713C0.646771 3.8088 0.257812 4.93484 0.257812 5.84817C0.257812 8.05692 1.95281 9.84734 4.04406 9.84734C6.1351 9.84734 7.8301 8.05692 7.8301 5.84817C7.8301 4.53567 6.9176 3.93005 6.92885 2.14192V2.14213Z" :fill="caleFill"/>
                </svg>
                {{ item.hot }}
              </span>
              <span v-if="!isWidgetMode && item.hot_zh" class="hotnet-hot">{{ item.hot_zh }}</span>
            </div>
          </li>
        </ul>

      </div>
    </div>

    <div
      v-else
      :class="['hotnet-container', {'widget-mode': isWidgetMode}, $attrs.class]"
      :style="$attrs.style"
      @click="toggleFullView"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
      
    >
      <!-- 顶部热搜平台选择 -->
      <div class="hotnet-platform-tabs" :style="calebackgroundColor">
        <div 
          v-for="(platform, index) in displayPlatforms" 
          :key="platform.value"
          :class="['platform-tab', { active: selectedType === platform.value }]"
          @click.stop="changeType(platform.value, index)"
          :ref="el => { if (el) tabRefs[index] = el }"
        >
          {{ platform.label }}
        </div>
        <!-- 查看全部按钮 -->
        <div
          v-show="isHovered"
          class=" view-all-tab"
          @click.stop="openAllPlatformsModal"
        >
          <img :src="more" />
        </div>
        <!-- 滑块指示器 -->
        <div class="tab-slider" :style="sliderStyle"></div>
      </div>
      
      <div class="hotnet-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
        
        <ul v-else class="hotnet-list">
          <li v-for="item in displayItems" :key="item.index" class="hotnet-item">
            <!-- <div :class="['hotnet-index']" :style="calebackgroundColor">{{ item.index }}</div> -->
            <div class="hotnet-info">
              <a :href="item.url" class="hotnet-title-link" target="_blank" @click.stop>{{ item.index }}.{{ item.title }}</a>
              <span class="hot-value" :style="caleColor">
                <svg width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.92885 2.14213C5.65177 2.52672 5.41781 3.61713 5.5001 4.30984C4.58906 3.17859 4.62635 1.87755 4.62635 0.00317383C1.70427 1.16672 2.38385 4.52172 2.29656 5.54047C1.56156 4.90505 1.4226 3.38713 1.4226 3.38713C0.646771 3.8088 0.257812 4.93484 0.257812 5.84817C0.257812 8.05692 1.95281 9.84734 4.04406 9.84734C6.1351 9.84734 7.8301 8.05692 7.8301 5.84817C7.8301 4.53567 6.9176 3.93005 6.92885 2.14192V2.14213Z" :fill="caleFill"/>
                </svg>

                {{ item.hot }}
              </span>
              <span v-if="!isWidgetMode && item.hot_zh" class="hotnet-hot">{{ item.hot_zh }}</span>
            </div>
          </li>
        </ul>
      </div>
      <!-- <div v-if="!isWidgetMode" class="hotnet-copyright">{{ hotlist.copyright }}</div> -->
    </div>
    
    <!-- 小组件弹窗模式 -->
    <Teleport to="body">
      <div v-if="showFullView" class="hotnet-modal" @click="toggleFullView">
        <div class="hotnet-modal-content" @click.stop>
          <div class="hotnet-close" @click="toggleFullView">×</div>
          
          <div class="modal-header">
            <div class="hotnet-title">网络热搜</div>
            <div class="hotnet-update">更新时间：{{ hotlist.update_time }}</div>
          </div>
          
          <!-- 热搜平台选择 -->
          <div class="hotnet-platform-tabs modal-tabs">
            <div 
              v-for="(platform, index) in displayPlatforms" 
              :key="platform.value"
              :class="['platform-tab', { active: selectedType === platform.value }]"
              @click.stop="changeType(platform.value)"
              :ref="el => { if (el) modalTabRefs[index] = el }"
            >
              {{ platform.label }}
            </div>
            <!-- 查看全部按钮 -->
            <div 
              class="platform-tab view-all-tab"
              @click.stop="openAllPlatformsModal"
            >
              <i class="more-icon">•••</i>
            </div>
            <!-- 弹窗滑块指示器 -->
            <div class="tab-slider" :style="modalSliderStyle"></div>
          </div>
          
          <!-- 热搜列表 -->
          <div class="modal-content">
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">加载中...</div>
            </div>
            
            <ul v-else class="hotnet-list modal-list">
              <li v-for="item in hotlist.data" :key="item.index" class="hotnet-item">
                <div :class="['hotnet-index', 'rank-' + Math.min(item.index, 3)]">{{ item.index }}</div>
                <div class="hotnet-info">
                  <a :href="item.url" class="hotnet-title-link" target="_blank">{{ item.title }}</a>
                  <span class="hotnet-hot">{{ item.hot_zh }}</span>
                </div>
              </li>
            </ul>
          </div>
          <div class="hotnet-copyright">{{ hotlist.copyright }}</div>
        </div>
      </div>
    </Teleport>
    
    <!-- 全部平台弹窗 - 使用AppModal样式 -->
    <Teleport to="body">
      <div v-if="showAllPlatformsModal" class="app-modal-overlay" @click.self="handleOverlayClick">
        <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
          <div class="app-modal-header">
            <div class="app-modal-title">全部热搜平台</div>
            <div class="app-modal-spacer"></div>
            <div class="app-modal-controls">
              <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
                <img :src="closeSvg" alt="close" />
              </button>
            </div>
          </div>
          
          <div class="app-modal-content">
            <div class="all-platforms-container">
              <!-- 平台选择区域 -->
              <div class="all-platforms-sidebar">
                <div 
                  v-for="platform in allPlatforms" 
                  :key="platform.value"
                  :class="['platform-item', { active: modalSelectedType === platform.value }]"
                  @click="changeModalType(platform.value)"
                >
                  {{ platform.label }}
                </div>
              </div>
              
              <!-- 热搜内容区域 -->
              <div class="all-platforms-content">
                <!-- 加载状态 -->
                <div v-if="modalLoading" class="loading-container">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">加载中...</div>
                </div>
                
                <div v-else class="platform-content">
                  <div class="platform-header">
                    <h3>{{ currentPlatformName }}</h3>
                    <div class="platform-update-time">更新时间：{{ modalHotlist.update_time || '暂无数据' }}</div>
                  </div>
                  
                  <ul v-if="modalHotlist.data && modalHotlist.data.length > 0" class="hotnet-list modal-list">
                    <li v-for="item in modalHotlist.data" :key="item.index" class="hotnet-item">
                      <div :class="['hotnet-index', 'rank-' + Math.min(item.index, 3)]">{{ item.index }}</div>
                      <div class="hotnet-info">
                        <a :href="item.url" class="hotnet-title-link" target="_blank">{{ item.title }}</a>
                        <span class="hotnet-hot">{{ item.hot_zh }}</span>
                      </div>
                    </li>
                  </ul>
                  
                  <div v-else class="empty-data">
                    <div class="empty-icon">📈</div>
                    <div class="empty-text">暂无热搜数据</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </Teleport>
  </template>
  
  <script setup>
  import { ref, onMounted, computed, reactive, watch, nextTick, onUnmounted } from 'vue'
  import { useSettingStore } from '@/stores/setting.js';
  // 不再导入Ant Design组件，使用自定义加载动画
  import more from '@/assets/icon/more.svg'
  import  closeSvg  from '@/assets/modal/close.svg'

  const props = defineProps({
    isWidget: {
      type: Boolean,
      default: false
    },
    appId: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '热搜榜单'
    },
    headerColor: {
      type: String,
      default: '#ff4d4f'
    },
    size: {
      type: Object,
      default: () => ({ w: 2, h: 2 })
    }
  });

  const settingStore = useSettingStore();

  watch(() => props.size, (newSize, oldSize) => {
    console.log(newSize,oldSize, 'wc')
    gridStyle.value = newSize.w + 'x' + newSize.h
  })
  
  const gridStyle = ref()


  // 禁用attribute继承，我们会手动处理
  defineOptions({
    inheritAttrs: false
  });
  
  const hotlist = ref({
    title: '',
    subtitle: '',
    update_time: '',
    data: [],
    copyright: ''
  });
  
  // 主弹窗的热搜数据
  const modalHotlist = ref({
    title: '',
    subtitle: '',
    update_time: '',
    data: [],
    copyright: ''
  });
  
  // 所有热搜平台定义
  const allPlatforms = [
    { label: '抖音', value: 'douyin' },
    { label: '微博', value: 'weibo' },
    { label: '百度', value: 'baidu' },
    { label: '知乎', value: 'zhihu' },
    { label: '少数派', value: 'sspai' },
    { label: 'CSDN', value: 'csdn' },
    { label: '历史上的今天', value: 'history' },
    { label: '哔哩哔哩全站', value: 'biliall' },
    { label: '哔哩哔哩热搜', value: 'bilihot' },
    { label: '搜狗', value: 'sogou' },
    { label: '搜狐', value: 'sohu' },
    { label: '今日头条', value: 'toutiao' },
    { label: 'ACFUN', value: 'acfun' },
    { label: '安全客', value: 'ker' },
    { label: '懂球帝', value: 'dongqiudi' },
    { label: '爱范儿', value: 'ifanr' },
    { label: '掘金', value: 'juejin' },
    { label: '网易新闻', value: 'netease_news' },
    { label: '51CTO', value: '51cto' },
    { label: 'GitHub', value: 'github' },
    { label: '知乎热门问题', value: 'billboard' }
  ];
  
  // 默认显示的热搜平台（只显示3个）
  const platforms = [
    { label: '抖音', value: 'douyin' },
    { label: '微博', value: 'weibo' },
    { label: '百度', value: 'baidu' }
  ];
  
  // 默认显示的平台
  const displayPlatforms = computed(() => {
    return platforms;
  });
  
  const selectedType = ref('douyin'); // 默认选择抖音热榜
  const modalSelectedType = ref('douyin'); // 弹窗中默认选择抖音热榜
  const isWidgetMode = ref(props.isWidget); // 小组件模式
  const showFullView = ref(false); // 小组件弹窗显示
  const showAllPlatformsModal = ref(false); // 全部平台弹窗显示
  const loading = ref(false); // 加载状态
  const modalLoading = ref(false); // 弹窗加载状态
  const isFullscreen = ref(false); // 全屏状态
  const isHovered = ref(false); // 鼠标悬停状态
  
  const selectIndex = ref(0)


  const calebackgroundColor = computed(() => {
    switch(selectIndex.value){
      case 0:
        return 'background: linear-gradient(90deg, rgba(255, 99, 99, 0.9) 0%, rgba(178, 31, 31, 0.9) 100%)'
      case 1:
        return 'background: linear-gradient(90deg, rgba(96, 152, 255, 0.9) 0%, rgba(38, 114, 255, 0.9) 100%)'
      case 2:
        return 'background: linear-gradient(90deg, rgba(255, 175, 62, 0.9) 0%, rgba(228, 137, 10, 0.9) 100%)'
    }
  });

  const caleColor = computed(() => {
    switch(selectIndex.value){
      case 0:
        return 'color: #de4239;'
      case 1:
        return 'color: #565df6;'
      case 2:
        return 'color: #f19a37;'
    }
  });

    const caleFill = computed(() => {
    switch(selectIndex.value){
      case 0:
        return '#de4239'
      case 1:
        return '#565df6'
      case 2:
        return '#f19a37'
    }
  });

  // 当前平台名称
  const currentPlatformName = computed(() => {
    const platform = allPlatforms.find(p => p.value === modalSelectedType.value);
    return platform ? platform.label : '热搜榜单';
  });
  
  // 用于滑块动画的引用
  const tabRefs = reactive([]);
  const modalTabRefs = reactive([]);
  const sliderStyle = ref({});
  const modalSliderStyle = ref({});
  
  // 计算显示的热搜条目
  const displayItems = computed(() => {
    if (isWidgetMode.value) {
      return hotlist.value.data.slice(0, 5); // 小组件模式只显示前5条
    }
    return hotlist.value.data;
  });
  
  // 更新滑块位置
  const updateSliderPosition = () => {
    nextTick(() => {
      // 查找当前激活的tab索引
      const activeIndex = displayPlatforms.value.findIndex(platform => platform.value === selectedType.value);
      if (activeIndex !== -1) {
        // 更新主视图滑块 - 箭头指向
        if (tabRefs[activeIndex]) {
          const activeTab = tabRefs[activeIndex];
          const centerX = activeTab.offsetLeft + activeTab.offsetWidth / 2;
          sliderStyle.value = {
            transform: `translateX(${centerX}px)`
          };
        }

        // 更新弹窗视图滑块 - 箭头指向
        if (showFullView.value && modalTabRefs[activeIndex]) {
          const activeModalTab = modalTabRefs[activeIndex];
          const centerX = activeModalTab.offsetLeft + activeModalTab.offsetWidth / 2;
          modalSliderStyle.value = {
            transform: `translateX(${centerX}px)`
          };
        }
      }
    });
  };
  
  // 监听选中类型变化，更新滑块位置
  watch(selectedType, () => {
    updateSliderPosition();
  });
  
  // 监听弹窗显示状态，弹窗显示时更新滑块位置
  watch(showFullView, (newValue) => {
    if (newValue) {
      nextTick(() => {
        updateSliderPosition();
      });
    }
  });
  // 切换热搜平台
  const changeType = (type, index) => {
    
    if(index != -1) {
      selectIndex.value = index
    }
    if (selectedType.value === type) return;
    
    selectedType.value = type;
    loading.value = true; // 显示加载动画
    fetchHotList();
  };
  
  // 切换完整视图
  const toggleFullView = () => {
    if (isWidgetMode.value) {
      showFullView.value = !showFullView.value;
    }
  };
  
  // 打开全部平台弹窗
  const openAllPlatformsModal = () => {
    showAllPlatformsModal.value = true;
    
    // 默认加载当前选择的平台内容
    modalSelectedType.value = selectedType.value;
    fetchModalHotList();
  };
  
  // 关闭全部平台弹窗
  const closeAllPlatformsModal = () => {
    showAllPlatformsModal.value = false;
    
    // 退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
    }
  };

  const handleOverlayClick = () => {
    if (settingStore.closeModalOnOutsideClick) {
      closeAllPlatformsModal();
    }
  };

  const handleCloseButtonClick = () => {
    if (settingStore.closeModalOnButtonClick) {
      closeAllPlatformsModal();
    }
  };
  
  // 切换全屏模式
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
    
    // 添加或移除body类以禁用滚动
    if (isFullscreen.value) {
      document.body.classList.add('modal-fullscreen-active');
    } else {
      document.body.classList.remove('modal-fullscreen-active');
    }
  };
  
  // 切换弹窗中的热搜平台
  const changeModalType = (type) => {
    if (modalSelectedType.value === type) return;
    
    modalSelectedType.value = type;
    modalLoading.value = true; // 显示加载动画
    fetchModalHotList();
  };
  
  // 获取主视图热搜列表
  const fetchHotList = async () => {
    try {
      // 使用API地址，添加type参数
      const url = `https://api.guiguiya.com/api/hotlist${selectedType.value ? `?type=${selectedType.value}` : ''}`;
      const res = await fetch(url);
      const json = await res.json();
      if (json.success) {
        hotlist.value = json;
      }
    } catch (e) {
      console.error('获取热搜失败:', e);
      hotlist.value.title = '热搜获取失败';
      hotlist.value.data = [];
    } finally {
      // 无论成功失败，都结束加载状态
      loading.value = false;
    }
  }
  
  // 获取弹窗热搜列表 
  const fetchModalHotList = async () => {
    try {
      // 使用API地址，添加type参数
      const url = `https://api.guiguiya.com/api/hotlist${modalSelectedType.value ? `?type=${modalSelectedType.value}` : ''}`;
      const res = await fetch(url);
      const json = await res.json();
      if (json.success) {
        modalHotlist.value = json;
      }
    } catch (e) {
      console.error('获取弹窗热搜失败:', e);
      modalHotlist.value.title = '热搜获取失败';
      modalHotlist.value.data = [];
    } finally {
      // 无论成功失败，都结束加载状态
      modalLoading.value = false;
    }
  }
  
  // 监听ESC键以关闭弹窗
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      if (showAllPlatformsModal.value) {
        closeAllPlatformsModal();
      }
    }
  };
  
  onMounted(() => {
    loading.value = true; // 显示初始加载动画
    fetchHotList();
    
    // 初始化滑块位置
    nextTick(() => {
      updateSliderPosition();
    });
    
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);

    gridStyle.value = props.size.w + 'x' + props.size.h

  });
  
  // 组件销毁时清理
  onUnmounted(() => {
    // 移除键盘事件监听
    window.removeEventListener('keydown', handleKeyDown);
    
    // 确保移除body类
    document.body.classList.remove('modal-fullscreen-active');
  });
  </script>
  
  <style lang="scss" scoped>
  .hotnet-container {
    width: 100%;
    height: 100%;
    overflow: auto;
    scrollbar-width: none;
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }
  
  .hotnet-container::-webkit-scrollbar {
    display: none;
  }
  
.hotnet-container:hover {
  box-shadow: 0 10px 25px rgba(0,0,0,0.12);
  /* transform: translateY(-2px); // transform 属性会导致 position: sticky/fixed 定位异常 */
}
  
  /* 小组件模式 */
  .widget-mode {
    width: 240px;
    height: 240px;
    cursor: pointer;
  }
  
  /* 内容区域 */
  .hotnet-content {
    flex: 1;
    position: relative;
    /* overflow: hidden; */
  }
  
  /* 加载动画容器 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 100px;
  }
  
  /* 自定义加载动画 */
  .loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(243, 243, 243, 0.3);
    border-top: 3px solid v-bind('props.headerColor');
    border-radius: 50%;
    animation: spin 1s cubic-bezier(0.45, 0, 0.55, 1) infinite;
    margin-bottom: 10px;
  }
  
  .loading-text {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* 平台标签样式 */
  .hotnet-platform-tabs {
    display: flex;
    background: linear-gradient(to right, #f9f9f9, #f0f0f0);
    border-bottom: 1px solid rgba(238, 238, 238, 0.8);
    // padding: 0 5px;
    position: sticky; /* 使用 sticky 使其在容器内置顶 */
    top: 0; /* 粘性定位的阈值 */
    z-index: 10; /* 确保在内容之上 */
    width: 100%;
    border-radius: 12px 12px 0 0;
    min-height: 50px;
    height: 50px;
  }
  
  .platform-tab {
    max-width: 60px;
    // padding: 12px 15px;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
    color: #ffffff;
    position: relative;
    flex: 1;
    transition: all 0.25s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .platform-tab:hover {
    color: #ffffff;
    background-color: rgba(0,0,0,0.02);
  }
  
  .platform-tab.active {
    color: white;
    font-weight: 600;
    font-size: 18px;
  }
  
  /* 查看全部按钮样式 */
  .view-all-tab {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // background-color: rgba(0,0,0,0.02);
    border-radius: 0 12px 0 0;
    padding: 12px;
    width: 40px;
    flex: none;
    color: #666;
    flex: 1;
    transition: all 0.3s ease;
    opacity: 0.8;
    span{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .view-all-tab:hover {
    background-color: rgba(0,0,0,0.05);
    color: #ffffff;
    opacity: 1;
    transform: scale(1.05);
  }
  
  .more-icon {
    font-style: normal;
    font-weight: bold;
    letter-spacing: -1px;
    font-size: 14px;
  }
  
  /* 滑块样式 - 箭头指向 */
  .tab-slider {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 0;
    width: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tab-slider::after {
    // content: '';
    // position: absolute;
    // bottom: 0;
    // left: 50%;
    // transform: translateX(-50%);
    // width: 0;
    // height: 0;
    // border-left: 8px solid transparent;
    // border-right: 8px solid transparent;
    // border-bottom: 8px solid white;
    // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .widget-mode .platform-tab {
    padding: 10px 6px;
    font-size: 12px;
  }
  
  /* 热搜列表样式 */
  .hotnet-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
  }
  
  .hotnet-item {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    text-align: left;
    transition: all 0.2s ease;
    position: relative;
  }
  
  .hotnet-item:hover {
    background-color: rgba(0,0,0,0.02);
  }
  
  .hotnet-index {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f0f0f0;
    color: #ffffff;
    border-radius: 50%;
    font-weight: bold;
    font-size: 14px;
    margin-right: 14px;
    flex-shrink: 0;
    transition: transform 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }
  
  .hotnet-item:hover .hotnet-index {
    transform: scale(1.05);
  }
  
  .hotnet-index.rank-1 {
    background: linear-gradient(135deg, #ff4d4d, #f54545);
    color: white;
  }
  
  .hotnet-index.rank-2 {
    background: linear-gradient(135deg, #ff9447, #ff8547);
    color: white;
  }
  
  .hotnet-index.rank-3 {
    background: linear-gradient(135deg, #ffb238, #ffac38);
    color: white;
  }
  
  .hotnet-info {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .hotnet-title-link {
    font-size: 14px;
    color: #878787;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    transition: color 0.2s ease;
  }
  
  .hot-value{
    font-size: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    // position: absolute;
    // bottom: 0px;
  }

  .hotnet-title-link:hover {
    color: v-bind('props.headerColor');
  }
  
  .hotnet-hot {
    font-size: 12px;
    color: #999;
    margin-left: 10px;
    white-space: nowrap;
    background-color: rgba(240,240,240,0.6);
    padding: 2px 6px;
    border-radius: 10px;
  }
  
  /* 版权信息 */
  .hotnet-copyright {
    padding: 8px 0;
    text-align: center;
    font-size: 12px;
    color: #999;
    background-color: rgba(250,250,250,0.8);
    border-top: 1px solid rgba(240,240,240,0.7);
  }
  
  /* 小组件模式下的样式调整 */
  .widget-mode .hotnet-item {
    padding: 8px 10px;
  }
  
  .widget-mode .hotnet-index {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    margin-right: 8px;
  }
  
  .widget-mode .hotnet-title-link {
    font-size: 12px;
  }
  
  /* 弹窗样式 */
  .hotnet-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn 0.25s ease;
  }
  
  @keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .hotnet-modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    background: #fff;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    animation: modalSlideIn 0.3s ease;
  }
  
  @keyframes modalSlideIn {
    from { transform: translateY(20px); opacity: 0.8; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  .modal-header {
    padding: 20px 15px 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
    background: linear-gradient(to right, #f8f8f8, #f0f0f0);
  }
  
  .modal-content {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  
  .hotnet-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }
  
  .hotnet-update {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
  
  .hotnet-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 1;
    transition: all 0.2s ease;
    background-color: rgba(240,240,240,0.5);
  }
  
  .hotnet-close:hover {
    background-color: rgba(0,0,0,0.08);
    color: #333;
    transform: rotate(90deg);
  }
  
  .modal-tabs {
    padding: 0 10px;
  }
  
  .modal-list {
    max-height: 60vh;
    overflow-y: auto;
    padding-bottom: 15px;
    scrollbar-width: thin;
    scrollbar-color: #ddd #f5f5f5;
  }
  
  .modal-list::-webkit-scrollbar {
    width: 6px;
  }
  
  .modal-list::-webkit-scrollbar-track {
    background: #f5f5f5;
  }
  
  .modal-list::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 6px;
  }
  
  /* AppModal样式 - 全部平台弹窗 */
  .app-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
  }
  
  .app-modal {
    width: 90%;
    height: 80%;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    max-width: 1200px;
    position: relative;
    z-index: 1001;
  }
  
  .app-modal-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0;
    z-index: 10000;
  }
  
  .app-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f5f5f7;
    border-bottom: 1px solid #eaeaea;
    height: 53px;
    -webkit-app-region: drag;
  }
  
  .app-modal-title {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    flex: 1;
    user-select: none;
  }
  
  .app-modal-spacer {
    width: 60px;
  }
  
  .app-modal-controls {
    display: flex;
    gap: 6px;
    margin-left: 4px;
    -webkit-app-region: no-drag;
  }
  
  .control-btn {
    width: 12px;
    height: 12px;
    padding: 5px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
      outline: none;
  border: none;
  }
  
  .control-btn .icon {
    opacity: 0.7;
    width: 8px;
    height: 8px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .control-btn:hover .icon {
    opacity: 1;
  }
  
  
  
  .maximize-btn {
    background-color: #28c940;
  }
  
  .maximize-btn:hover {
    background-color: #28c940;
    filter: brightness(0.9);
  }
  
  .app-modal-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #fff;
  }
  
  .app-modal-footer {
    padding: 8px 0;
    text-align: center;
    font-size: 12px;
    color: #999;
    background-color: rgba(250,250,250,0.8);
    border-top: 1px solid rgba(240,240,240,0.7);
  }
  
  /* 图标样式 */
  .icon {
    display: inline-block;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
  }
  
  .icon-close {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  }
  
  .icon-maximize {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
  }
  
  .icon-minimize {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  }
  
  /* 全部平台弹窗的特殊样式 */
  .all-platforms-container {
    display: flex;
    height: 100%;
    overflow: hidden;
  }
  
  .all-platforms-sidebar {
    width: 180px;
    height: 100%;
    overflow-y: auto;
    background-color: #f5f5f7;
    border-right: 1px solid #eaeaea;
    scrollbar-width: thin;
    scrollbar-color: #ddd #f5f5f7;
    position: relative; /* 添加相对定位 */
  }
  
  .all-platforms-sidebar::-webkit-scrollbar {
    width: 4px;
  }
  
  .all-platforms-sidebar::-webkit-scrollbar-track {
    background: #f5f5f7;
  }
  
  .all-platforms-sidebar::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 4px;
  }
  
  .platform-item {
    padding: 12px 15px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    border-bottom: 1px solid rgba(0,0,0,0.03);
    transition: all 0.2s ease;
    border-left: 0 solid transparent;
    position: relative;
  }
  
  .platform-item:hover {
    background-color: rgba(0,0,0,0.03);
  }
  
  .platform-item.active {
    background-color: rgba(0,0,0,0.05);
    color: v-bind('props.headerColor');
    font-weight: 500;
    border-left: 3px solid v-bind('props.headerColor');
    transition: border-left 0.3s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
  }
  
  /* 移除伪元素，直接使用边框 */
  .platform-item.active::after {
    content: none;
  }
  
  .all-platforms-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .platform-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0;
  }
  
  .platform-header {
    padding: 15px;
    border-bottom: 1px solid #eaeaea;
  }
  
  .platform-header h3 {
    margin: 0 0 5px;
    font-size: 16px;
    color: #333;
  }
  
  .platform-update-time {
    font-size: 12px;
    color: #999;
  }
  
  .empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    gap: 10px;
  }
  
  .empty-icon {
    font-size: 32px;
  }
  
  .empty-text {
    font-size: 14px;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @media (max-width: 600px) {
    .platform-tab {
      padding: 10px 8px;
      font-size: 13px;
    }
    
    .hotnet-modal-content {
      width: 95%;
      max-height: 85vh;
    }
    
    .all-platforms-sidebar {
      width: 120px;
    }
    
    .platform-item {
      padding: 10px;
      font-size: 12px;
    }
  }
  .size-2x1{
    background: linear-gradient(135deg, #F5655A 0%, #FF8E53 100%);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  </style>

<style>
/* 全局样式，确保全屏模式下禁用页面滚动 */
body.modal-fullscreen-active {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}
</style>
