<template>
  <div class="search-section" v-bind="$attrs">
    <div class="search-container" :style="containerStyle">
      <img v-if="showPeople" src="@/assets/officialWallpaper/mian-people.png" alt="people" class="search-people">
      <div class="engine-selector">
        <button 
          @click="toggleEngineDropdown" 
          class="engine-icon"
          ref="engineButton"
        >
          <img :src="selectedEngine.ico || defaultIcon" alt="搜索引擎" class="engine-img" />
          <!-- <span class="engine-name">{{ selectedEngine.name }}</span> -->
          <svg 
            class="dropdown-arrow" 
            :class="{ 'arrow-open': showEngineDropdown }" 
            xmlns="http://www.w3.org/2000/svg" 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            stroke-width="2" 
            stroke-linecap="round" 
            stroke-linejoin="round"
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </button>
        
        <transition name="dropdown">
          <div v-if="showEngineDropdown" class="engine-dropdown">
            <button 
              v-for="engine in allSearchEngines" 
              :key="engine.name"
              @click="selectEngine(engine)"
              :class="['engine-option', { active: selectedEngine.name === engine.name }]"
            >
              <img :src="engine.ico || defaultIcon" alt="搜索引擎" class="engine-img" />
              <span>{{ engine.name }}</span>
              <transition name="check">
                <svg 
                  v-if="selectedEngine.name === engine.name" 
                  class="check-icon" 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  stroke-width="2" 
                  stroke-linecap="round" 
                  stroke-linejoin="round"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </transition>
            </button>
            <div class="divider"></div>
            <button class="engine-option custom-engine-btn" @click="openCustomEngineModal">
              <span class="custom-icon">+</span>
              <span>自定义</span>
            </button>
          </div>
        </transition>
      </div>
      
      <div class="search-input-container">
        <input 
          type="text" 
          v-model="searchQuery"
          @keydown="handleSearchKeydown"
          :placeholder="placeholder"
          class="search-input"
          :style="[inputColor, placeholderStyle]"
          />
      </div>
      
      <!-- <button class="search-button" @click="executeSearch">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </button> -->
    </div>
  </div>

  <!-- 自定义浏览器模态窗口 -->
  <Teleport to="body">
    <div class="app-modal-overlay" v-if="customEngineModalVisible" @click.self="handleOverlayClick">
      <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
        <div class="app-modal-header">
          <div class="app-modal-title">自定义浏览器</div>
          <div class="app-modal-spacer"></div>
          <div class="app-modal-controls">
            <button class="control-btn maximize-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏'">
              <i class="icon" :class="isFullscreen ? 'icon-minimize' : 'icon-maximize'"></i>
            </button>
            <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
              <i class="icon icon-close"></i>
            </button>
          </div>
        </div>
        <div class="app-modal-content">
          <div class="custom-engine-container">
            <div class="add-engine-form">
              <h3>添加新搜索引擎</h3>
              <div class="form-group">
                <label>名称</label>
                <input type="text" v-model="newEngine.name" placeholder="例如: 知乎">
              </div>
              <div class="form-group">
                <label>搜索 URL</label>
                <input 
                  type="text" 
                  v-model="newEngine.url" 
                  placeholder="例如: https://www.zhihu.com/search?q="
                >
              </div>
              <div class="form-group">
                <label>图标 URL</label>
                <input 
                  type="text" 
                  v-model="newEngine.ico" 
                  placeholder="输入图标URL或留空使用默认图标"
                >
              </div>
              <div class="preview">
                <span color="#333">预览:</span>
                <div class="engine-preview">
                  <img :src="newEngine.ico || defaultIcon" alt="图标预览" class="engine-img">
                  <span>{{ newEngine.name || '搜索引擎名称' }}</span>
                </div>
              </div>
              <button class="add-button" @click="addCustomEngine">
                {{ editingIndex !== null ? '更新' : '添加' }} 搜索引擎
              </button>
            </div>
            
            <div class="custom-engine-list">
              <h3>已添加的搜索引擎</h3>
              <div v-if="customEngines.length === 0" class="empty-state">
                暂无自定义搜索引擎
              </div>
              <div v-else class="engines-list">
                <div v-for="(engine, index) in customEngines" :key="index" class="custom-engine-item">
                  <div class="engine-info">
                    <img :src="engine.icon || defaultIcon" alt="搜索引擎" class="engine-img">
                    <div class="engine-details">
                      <div class="engine-name">{{ engine.name }}</div>
                      <div class="engine-url">{{ engine.url }}</div>
                    </div>
                  </div>
                  <div class="engine-actions">
                    <button @click="editEngine(index)" class="action-btn edit-btn">编辑</button>
                    <button @click="deleteEngine(index)" class="action-btn delete-btn">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, onMounted, computed, watch, onBeforeUnmount } from 'vue'
import { searchEngine } from '@/api/search'
import { useSettingStore } from '@/stores/setting.js'

// 禁用属性自动继承，因为我们手动绑定了 $attrs
defineOptions({
  inheritAttrs: false
})
import bd from '@/assets/image/searchIcon/bd.png'
import bing from '@/assets/image/searchIcon/bing.png'
import google from '@/assets/image/searchIcon/google.png'
import sogou from '@/assets/image/searchIcon/sg.png'
import { message } from 'ant-design-vue'

const props = defineProps({
  placeholder: {
    type: String,
    default: '输入关键词搜索...'
  },
  backgroundColor: {
    type: String,
    default: 'rgba(255, 255, 255, 0.5)'
  },
  showPeople: {
    type: Boolean,
    default: true
  },
  searchFontColor:{
    type: String,
    default: ''
  }
})

// 计算容器样式
const containerStyle = computed(() => {
  return {
    background: props.backgroundColor,
    color: props.searchFontColor
  }
})

// 定义 placeholder 的样式
const placeholderStyle = computed(() => ({
  '--placeholder-color': props.searchFontColor
}))

const inputColor = computed(() => {
  return {
    color: props.searchFontColor,
  }
})

const emit = defineEmits(['query'])

const settingStore = useSettingStore()

// 搜索引擎相关
const searchQuery = ref('')
const defaultIcon = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiPjwvY2lyY2xlPjxsaW5lIHgxPSIyMSIgeTE9IjIxIiB4Mj0iMTYuNjUiIHkyPSIxNi42NSI+PC9saW5lPjwvc3ZnPg=='
const searchEngines = ref([])
const selectedEngine = ref([])
const showEngineDropdown = ref(false)
const engineButton = ref(null)

// 自定义搜索引擎
const customEngines = ref([])
const customEngineModalVisible = ref(false)
const isFullscreen = ref(false)
const newEngine = ref({
  name: '',
  url: '',
  icon: ''
})
const editingIndex = ref(null)

// 合并默认和自定义搜索引擎
const allSearchEngines = computed(() => {
  return [...searchEngines.value, ...customEngines.value]
})

// 监听搜索输入变化，用于本地搜索
watch(searchQuery, (newVal) => {
  // emit('query', newVal)
})

// 切换搜索引擎下拉菜单
function toggleEngineDropdown() {
  showEngineDropdown.value = !showEngineDropdown.value
}

// 点击外部关闭下拉菜单
function closeDropdownOnClickOutside(event) {
  if (engineButton.value && !engineButton.value.contains(event.target) && showEngineDropdown.value) {
    showEngineDropdown.value = false
  }
}

// 执行搜索
function executeSearch() {
  if (searchQuery.value.trim()) {
    // 如果是网页搜索，打开新标签
    window.open(selectedEngine.value.url + encodeURIComponent(searchQuery.value), '_blank')
    // 不清空输入，允许继续本地搜索
  }
}

// 切换搜索引擎
function selectEngine(engine) {
  selectedEngine.value = engine
  showEngineDropdown.value = false
}

// 处理键盘事件
function handleSearchKeydown(e) {
  if (e.key === 'Enter') {
    executeSearch()
  } else if (e.key === 'Escape') {
    // 按ESC清空搜索
    searchQuery.value = ''
    emit('query', '')
  }
}

// 获取搜索引擎列表
function getSarchEngineList() {
  searchEngine().then(res => {
    if(res.status === 200) {
      searchEngines.value = res.data
      selectedEngine.value = searchEngines.value[0]

      console.log(selectedEngine.value,'search')
    }else{
      message.error('获取搜索引擎失败')
    }
  })
}

// 加载自定义搜索引擎
onMounted(() => {
  document.addEventListener('click', closeDropdownOnClickOutside)
  loadCustomEngines()
  getSarchEngineList()
})

// 保存自定义搜索引擎
function saveCustomEngines() {
  localStorage.setItem('customSearchEngines', JSON.stringify(customEngines.value))
}

// 加载自定义搜索引擎
function loadCustomEngines() {
  const saved = localStorage.getItem('customSearchEngines')
  if (saved) {
    try {
      customEngines.value = JSON.parse(saved)
    } catch (e) {
      console.error('Failed to load custom engines:', e)
      customEngines.value = []
    }
  }
}

// 打开自定义引擎模态窗口
function openCustomEngineModal() {
  customEngineModalVisible.value = true
  showEngineDropdown.value = false
}

// 关闭自定义引擎模态窗口
function closeCustomEngineModal() {
  customEngineModalVisible.value = false
  resetNewEngine()
}

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    closeCustomEngineModal();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    closeCustomEngineModal();
  }
};

// 重置新引擎表单
function resetNewEngine() {
  newEngine.value = {
    name: '',
    url: '',
    icon: ''
  }
  editingIndex.value = null
}

// 添加自定义搜索引擎
function addCustomEngine() {
  if (!newEngine.value.name || !newEngine.value.url) {
    alert('请输入搜索引擎名称和URL')
    return
  }
  
  // 确保URL具有搜索参数
  if (!newEngine.value.url.includes('?')) {
    newEngine.value.url += '?q='
  }
  
  // 如果是编辑现有引擎
  if (editingIndex.value !== null) {
    customEngines.value[editingIndex.value] = { ...newEngine.value }
  } else {
    // 添加新引擎
    customEngines.value.push({ ...newEngine.value })
  }
  
  // 保存到本地存储
  saveCustomEngines()
  
  // 重置表单
  resetNewEngine()
}

// 编辑搜索引擎
function editEngine(index) {
  const engine = customEngines.value[index]
  newEngine.value = { ...engine }
  editingIndex.value = index
}

// 删除搜索引擎
function deleteEngine(index) {
  if (confirm('确定要删除这个搜索引擎吗?')) {
    // 如果正在编辑的是要删除的，重置表单
    if (editingIndex.value === index) {
      resetNewEngine()
    }
    customEngines.value.splice(index, 1)
    saveCustomEngines()
  }
}

// 切换全屏状态
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active')
  } else {
    document.body.classList.remove('modal-fullscreen-active')
  }
}

onBeforeUnmount(() => {
  document.removeEventListener('click', closeDropdownOnClickOutside)
  document.body.classList.remove('modal-fullscreen-active')
})
</script>

<style lang="scss" scoped>
.search-section {
  width: 100%;
  max-width: 561px;
  height: 42px;
  margin: 0 auto;
  z-index: 1;
}

// 添加从Office.vue复制过来的纯净模式样式
.search-center {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, 20%);
  animation: slideDown 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  z-index: 10;
}

/* 定义从上往下滑动的动画 */
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translate(-50%, -150%);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, 20%);
  }
}

/* 添加退出纯净模式时的动画类 */
.exit-pure-mode-search {
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

/* 定义从下往上滑动的动画 */
@keyframes slideUp {
  0% {
    opacity: 1;
    transform: translate(-50%, 20%);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -150%);
  }
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 4px 6px;
  gap: 8px;
  max-width: 600px;
  height: 42px;
  margin: 0 auto;
  border-radius: 70px;
  position: relative;
  .search-people{
    position: absolute;
    top: -31px;
    right: 0;
    width: 153px;
    height: 84px;
    z-index: 100;
  }
}

.engine-selector {
  position: relative;
}

.engine-icon {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-primary);
  transition: all 0.2s ease;
  outline: none;
  border: none;
}


.dropdown-arrow {
  margin-left: 2px;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.arrow-open {
  transform: rotate(180deg);
}

.engine-img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.engine-name {
  font-size: 14px;
}

.engine-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px;
  margin-top: 4px;
  width: 160px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
  transform-origin: top center;
}

.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.engine-dropdown .engine-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px;
  border: none;
  background: none;
  text-align: left;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-primary);
  transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.engine-dropdown .engine-option:hover {
  background-color: var(--hover-bg);
  transform: translateY(-1px);
}

.engine-dropdown .engine-option:active {
  transform: translateY(0);
}

.engine-dropdown .engine-option.active {
  color: var(--accent-color);
  background-color: rgba(var(--accent-color-rgb), 0.08);
  font-weight: 500;
}

.check-icon {
  position: absolute;
  right: 8px;
  color: var(--accent-color);
}

.check-enter-active,
.check-leave-active {
  transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.check-enter-from,
.check-leave-to {
  opacity: 0;
  transform: scale(0.5);
}

.search-input-container {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  outline: none;
  font-size: 14px;
  color: var(--text-primary);
}

.search-input::placeholder {
  color: var(--placeholder-color);
  // color: #ffffff;
  // color: var(--text-secondary);
}

.search-button {
  padding: 8px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.search-button:hover {
  background-color: var(--accent-color-hover);
  transform: translateY(-1px);
}

.search-button:active {
  transform: translateY(0);
}

@media (max-width: 640px) {
  .search-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 4px 10px;
    // padding: 8px;
  }
  
  .engine-selector {
    width: 100%;
    margin-bottom: 8px;
    width: auto;
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .engine-icon {
    width: 100%;
    justify-content: center;
  }
  
  .engine-dropdown {
    width: 100%;
  }
  
  .search-input-container {
    width: 100%;
  }
}

/* 自定义浏览器相关样式 */
.divider {
  height: 1px;
  background-color: var(--border-color, #eaeaea);
  margin: 8px 0;
}

.custom-engine-btn {
  z-index: 999;
  color: var(--accent-color, #1890ff);
  font-weight: 500;
}

.custom-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--accent-color, #1890ff);
  color: white;
  font-size: 14px;
  margin-right: 2px;
}

/* 模态窗口样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 900px;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 32px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.2) inset;
}

.control-btn .icon {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.close-btn {
  background-color: #ff5f57;
}

.close-btn:hover {
  background-color: #ff5f57;
  filter: brightness(0.9);
}

.maximize-btn {
  background-color: #28c940;
}

.maximize-btn:hover {
  background-color: #28c940;
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow-y: auto;
  background-color: #fff;
  padding: 20px;
}

/* 自定义引擎容器样式 */
.custom-engine-container {
  display: flex;
  gap: 20px;
}

.add-engine-form {
  flex: 1;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  max-width: 400px;
}

.add-engine-form h3 {
  margin-top: 0;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

.help-text {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.preview {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: white;
}

.engine-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
}

.add-button {
  width: 100%;
  padding: 10px;
  background-color: var(--accent-color, #1890ff);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 20px;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: var(--accent-color-hover, #40a9ff);
}

.custom-engine-list {
  flex: 1;
  overflow-y: auto;
}

.custom-engine-list h3 {
  margin-top: 0;
  color: #333;
}

.empty-state {
  color: #888;
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.engines-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.custom-engine-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  transition: all 0.2s;
}

.custom-engine-item:hover {
  background-color: #f0f0f0;
}

.engine-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.engine-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.engine-name {
  font-weight: 500;
  color: #333;
}

.engine-url {
  font-size: 12px;
  color: #666;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.engine-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-btn {
  background-color: #f0f0f0;
  color: #555;
}

.edit-btn:hover {
  background-color: #e0e0e0;
}

.delete-btn {
  background-color: #ffedeb;
  color: #ff4d4f;
}

.delete-btn:hover {
  background-color: #ffc8c5;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 图标样式 */
.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

@media (max-width: 768px) {
  .custom-engine-container {
    flex-direction: column;
  }
  
  .add-engine-form {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .search-section {
    max-width: 100vw;
    padding: 0 4px;
  }
  .search-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 4px 10px;
    border-radius: 30px;
    gap: 4px;
    min-width: 0;
  }
  .search-people {
    width: 100px;
    height: 60px;
    top: -18px;
  }
  .engine-selector {
    width: auto;
    margin-bottom: 0;
    flex-shrink: 0;
  }
  .engine-icon {
    width: auto;
    min-width: 0;
    justify-content: center;
    padding: 4px 0;
  }
  .engine-dropdown {
    width: 100vw;
    left: -12px;
    min-width: unset;
    max-width: 100vw;
    font-size: 14px;
    padding: 4px 0;
  }
  .engine-dropdown .engine-option {
    padding: 6px 8px;
    font-size: 14px;
  }
  .search-input-container {
    width: auto;
    min-width: 0;
    flex: 1;
  }
  .search-input {
    font-size: 15px;
    padding: 8px 6px;
    border-radius: 20px;
  }
  .search-button {
    padding: 6px;
    font-size: 14px;
    border-radius: 20px;
  }
  .app-modal {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    border-radius: 0;
    margin: 0;
    padding: 0;
  }
  .app-modal-content {
    padding: 8px 2px;
  }
  .custom-engine-container {
    flex-direction: column;
    gap: 8px;
  }
  .add-engine-form {
    max-width: 100vw;
    padding: 8px 2px;
  }
  .custom-engine-list {
    padding: 0 2px;
  }
  .custom-engine-item {
    padding: 8px 4px;
    font-size: 14px;
  }
  .engine-details {
    gap: 2px;
  }
  .engine-name {
    font-size: 15px;
  }
  .engine-url {
    font-size: 11px;
    max-width: 160px;
  }
  .action-btn {
    padding: 4px 8px;
    font-size: 13px;
  }
  .add-button {
    padding: 8px;
    font-size: 15px;
  }
  .app-modal-header {
    height: 28px;
    padding: 4px 6px;
  }
  .app-modal-title {
    font-size: 12px;
  }
  .control-btn {
    width: 10px;
    height: 10px;
    padding: 3px;
  }
}
</style>
