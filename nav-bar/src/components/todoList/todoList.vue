<template>
  <div class="todo-container">
    <header class="current-date" v-if="false">
      <div class="date">
        <span class="day-number">{{ date.dayNum }}</span>
        <div class="month-year-wrapper">
          <span class="month">{{ date.month }}</span>
          <span class="year">{{ date.year }}</span>
        </div>
      </div>
      <div class="today"><span>{{ date.dayName }}</span></div>
    </header>
    <main class="todo-list">
      <Empty v-if="tasks.length === 0" description="暂无任务" />
      <div class="tasks-wrapper" v-else>
        <ul class="tasks-container">
          <li 
            class="task-container" 
            v-for="(task, index) in tasks" 
            :key="index" 
            :class="{ checked: !task.state }"
          >
            <div class="task-content">
              <Radio 
                :checked="!task.state" 
                @change="checkTask(index)"
              />
              <p class="task">{{ task.name }}</p>
            </div>
            <Button 
              type="text" 
              class="delete-btn" 
              @click="confirmDelete(index)"
              shape="circle"
            >
              <template #icon><DeleteOutlined /></template>
            </Button>
          </li>
        </ul>
      </div>
    </main>
    <!-- 添加任务按钮 - 简化结构 -->
    <button class="add-task-btn" @click="showAddTaskModal" title="添加新任务">+</button>

    <!-- 添加任务对话框 -->
    <Modal
      v-model:visible="modalVisible"
      title="添加新任务"
      @ok="handleAddTask"
      :centered="true"
      :maskClosable="false"
      class="todo-modal"
      :width="300"
    >
      <div class="todo-input-wrapper">
        <Input 
          v-model:value="newTaskText" 
          placeholder="请输入任务内容" 
          @pressEnter="handleAddTask"
          class="todo-input"
          ref="inputRef"
        />
      </div>
    </Modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, createApp, watch } from 'vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import './todoListOffice.css'; // 引入专门针对Office页面的样式

// 按需引入Ant Design Vue组件
import { message, Modal, Radio, Button, Empty, Input } from 'ant-design-vue';


// 定义属性
const props = defineProps({
  size: {
    type: Object,
    default: () => ({ w: 4, h: 4 })
  }
});

watch(() => props.size, (newSize, oldSize) => {
  gridStyle.value = newSize.w + 'x' + newSize.h
})



const gridStyle = ref()

// 任务列表数据
const tasks = ref([]);
// 新任务文本
const newTaskText = ref('');
// 对话框可见性
const modalVisible = ref(false);
// 输入框引用
const inputRef = ref(null);

// 日期相关数据
const date = reactive({
  dayNum: '',
  month: '',
  year: '',
  dayName: ''
});

/**
 * 获取当前日期并格式化
 */
const getCurrentDay = () => {
  const today = new Date();
  
  // 获取星期几
  const dayName = dayjs(today).format('dddd').toUpperCase();
  
  // 获取月份和日期
  const monthStr = dayjs(today).format('MMM').toUpperCase();
  const dayNum = dayjs(today).format('D');
  
  // 获取年份
  const year = dayjs(today).format('YYYY');
  
  // 更新日期数据
  date.dayNum = dayNum;
  date.month = monthStr;
  date.year = year;
  date.dayName = dayName;
};

/**
 * 显示添加任务对话框
 */
const showAddTaskModal = () => {
  modalVisible.value = true;
  // 在对话框显示后，自动聚焦到输入框
  nextTick(() => {
    inputRef.value?.focus();
  });
};

/**
 * 处理添加任务
 */
const handleAddTask = () => {
  if (newTaskText.value && newTaskText.value.trim()) {
    // 创建新任务对象
    const objTask = {
      name: newTaskText.value.trim(),
      state: true, // 默认为未完成状态
      createTime: Date.now()
    };
    
    // 将新任务添加到任务列表的开头
    tasks.value.unshift(objTask);
    message.success('任务添加成功');
    
    // 保存到本地存储
    saveTasks();
    
    // 重置表单并关闭对话框
    newTaskText.value = '';
    modalVisible.value = false;
  } else {
    message.warning('任务内容不能为空');
  }
};

/**
 * 切换任务完成状态
 * @param {Number} index - 任务在数组中的索引
 */
const checkTask = (index) => {
  tasks.value[index].state = !tasks.value[index].state;
  // 保存更新后的任务状态
  saveTasks();
};

/**
 * 确认删除任务
 * @param {Number} index - 任务在数组中的索引
 */
const confirmDelete = (index) => {
  Modal.confirm({
    title: '确认删除',
    content: '您确定要删除这个任务吗？',
    okText: '确认',
    cancelText: '取消',
    centered: true,
    class: 'todo-confirm-modal',
    onOk: () => deleteTask(index)
  });
};

/**
 * 删除任务
 * @param {Number} index - 任务在数组中的索引
 */
const deleteTask = (index) => {
  tasks.value.splice(index, 1);
  message.success('任务已删除');
  saveTasks();
};

/**
 * 保存任务到本地存储
 */
const saveTasks = () => {
  localStorage.setItem('todoTasks', JSON.stringify(tasks.value));
};

/**
 * 从本地存储加载任务
 */
const loadTasks = () => {
  const savedTasks = localStorage.getItem('todoTasks');
  if (savedTasks) {
    tasks.value = JSON.parse(savedTasks);
  }
};



// 组件挂载时获取当前日期和加载任务
onMounted(() => {
  gridStyle.value = props.size.w + 'x' + props.size.h
  getCurrentDay();
  loadTasks();
});
</script>

<style lang="scss">
/* 移除scoped，让样式全局生效 */
// 颜色变量定义
:root {
  --body-bkg: #F0EFE9;
  --todo-bkg: #FFFFFF;
  --text-color: #5F6271;
  --text-unselected-color: #d7d7dc;
  --control-color: #50E3A4;
}

// 基础样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// Todo容器样式
.todo-container {
  height: 100%;
  padding: 2em;
  position: relative;
  width: 100%;
  color: var(--text-color);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.2);
  // 顶部日期栏样式
  header {
    width: 100%;
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 1em;
    flex-shrink: 0;
    
    .date {
      display: flex;
      justify-content: space-between;
      width: 22%;
      min-width: 80px;
      
      .day-number {
        font-size: 2em;
        font-weight: bold;
        line-height: 1em;
      }
      
      .month-year-wrapper {
        align-items: center;
        display: flex;
        flex-direction: column;
        font-size: 0.8em;
        
        .month {
          font-weight: bold;
        }
      }
    }
    
    .today {
      font-weight: 600;
    }
  }
  
  // 任务列表样式
  main.todo-list {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    position: relative;
    
    .ant-empty {
      margin: 32px 0;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .ant-empty-image{
      height: 50%;
    }
    .ant-empty-description{
      margin: 0;
    }
    .tasks-wrapper {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    
    .tasks-container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow-y: auto;
      list-style: none;
      padding-right: 5px;
      
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--control-color);
        border-radius: 4px;
      }
      
      li.task-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.7em;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.02);
          
          .delete-btn {
            opacity: 1;
          }
        }
        
        &.checked {
          .task {
            color: var(--text-unselected-color);
            text-decoration: line-through;
            transition: all 500ms ease-in-out;
          }
        }
        
        .task-content {
          display: flex;
          align-items: center;
          flex: 1;

          .task {
            margin-left: 10px;
            word-break: break-word;
            margin: 0px 10px;
          }
        }
        
        .delete-btn {
          opacity: 0;
          transition: opacity 0.3s;
          color: #ff4d4f;
        }
      }
    }
  }
  
  // 添加任务按钮样式
  button.add-task-btn {
    background-color: var(--control-color) !important;
    border: none !important;
    border-radius: 50% !important;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.18) !important;
    cursor: pointer !important;
    height: 20px !important;
    width: 20px !important;
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    left: auto !important;
    transform: none !important;
    outline: none !important;
    transition: 200ms ease-in-out !important;
    z-index: 10 !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    span {
      font-weight: bold !important;
      color: #46BE8B !important;
      font-family: none !important;
      font-size: 14px !important;
      line-height: 1 !important;
      display: block !important;
      transform: translateY(-1px) !important;
    }
    
    &:active {
      transform: scale(0.9) !important;
    }
  }
}

/* 自定义对话框和输入框样式 */
.todo-modal {
  .ant-modal-content {
    border-radius: 12px !important;
    overflow: hidden !important;
  }
  
  .ant-modal-header {
    background-color: unset;
  }
  
  .ant-modal-body {
    padding: 16px !important;
  }
  
  .todo-input-wrapper {
    width: 100%;
    
    .todo-input {
      width: 100%;
      height: 36px !important;
    }
  }
}

.todo-confirm-modal {
  .ant-modal-content {
    border-radius: 12px !important;
    overflow: hidden !important;
  }
  
  .ant-modal-body {
    padding: 20px !important;
  }
}

// 自定义 Ant Design 组件样式
.ant-radio-wrapper {
  margin-right: 0 !important;
}

.ant-btn-circle {
  min-width: auto !important;
}

// 响应式样式调整
@media (max-width: 480px) {
  .todo-container {
    padding: 1.5em;

    header {
      .date {
        width: 26%;
      }
    }

    button.add-task-btn {
      height: 18px !important;
      width: 18px !important;
      top: 8px !important;
      right: 8px !important;

      span {
        font-size: 12px !important;
      }
    }
  }
}
</style>

<!-- 创建单独的样式文件，作为插件导入Ant Design自定义样式 -->
<style>
/* 防止与其他组件冲突的全局样式 */
.ant-modal.todo-modal .ant-modal-content,
.ant-modal.todo-confirm-modal .ant-modal-content {
  border-radius: 12px !important;
  overflow: hidden !important;
}


.ant-modal.todo-modal .ant-modal-body {
  padding: 16px !important;
}

.ant-modal.todo-modal .ant-input {
  height: 36px !important;
}

/* 确保Radio和Button样式正确 */
.todo-container .ant-radio-wrapper {
  margin-right: 0 !important;
}

.todo-container .ant-btn-circle {
  min-width: auto !important;
}
</style>
