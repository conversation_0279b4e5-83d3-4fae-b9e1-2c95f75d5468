/* 专门针对Office页面中todoList组件的样式覆盖 */

/* 确保按钮大小和位置正确 */
.card-item .todo-container button.add-task-btn {
  height: 20px !important;
  width: 20px !important;
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
  left: auto !important;
  transform: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
  line-height: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
  z-index: 99 !important;
}

/* Ant Design 组件样式覆盖 */
.card-item .ant-radio-wrapper {
  margin-right: 0 !important;
}

.card-item .todo-container .ant-btn-circle {
  min-width: auto !important;
}

/* 调整任务容器高度，确保滚动正常 */
.card-item .todo-container .tasks-container {
  max-height: 100% !important;
} 