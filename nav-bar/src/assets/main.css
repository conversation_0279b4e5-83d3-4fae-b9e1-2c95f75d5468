:root {
  --primary-color: #4a6cf7;
  --primary-hover: #3151cd;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --dark-color: #343a40;
  --light-color: #f8f9fa;
  --border-color: #dee2e6;
  --transition: all 0.3s ease-out;
  --text-primary: #fff;
  --text-secondary: rgba(255, 255, 255, 0.8);
}

/* 主题相关变量 */
[data-theme='light'] {
  --background-color: #f8f9fa;
  --text-color: #fff;
  --header-bg: rgba(255, 255, 255, 0.9);
  --sidebar-bg: #fff;
  --card-bg: #fff;
  --dropdown-bg: #fff;
  --hover-bg: #f1f3f5;
  --border-color: #e9ecef;
  --shadow-color: rgba(0, 0, 0, 0.075);
}

[data-theme='dark'] {
  --background-color: #121212;
  --text-color: #fff;
  --header-bg: rgba(33, 37, 41, 0.9);
  --sidebar-bg: #1e1e1e;
  --card-bg: #2d2d2d;
  --dropdown-bg: #2d2d2d;
  --hover-bg: #3a3a3a;
  --border-color: #444;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

[data-theme='blue'] {
  --background-color: #f0f8ff;
  --text-color: #fff;
  --header-bg: rgba(37, 67, 117, 0.9);
  --sidebar-bg: #2c4f7b;
  --card-bg: #fff;
  --dropdown-bg: #fff;
  --hover-bg: #e8f0fe;
  --border-color: #c7d8eb;
  --shadow-color: rgba(100, 149, 237, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #fff;
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 全局过渡效果 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 背景图片样式 */
.app-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  background-size: 100% 100%;
  /* background-position: top; */
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.app-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

.transparent-bg .app-overlay {
  background-color: rgba(0, 0, 0, 0.2);
}

.transparent-bg [data-theme="light"] .header-bar,
.transparent-bg [data-theme="light"] .sidebar {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
}

.transparent-bg [data-theme="dark"] .header-bar,
.transparent-bg [data-theme="dark"] .sidebar {
  background-color: rgba(30, 30, 30, 0.7);
  backdrop-filter: blur(10px);
}

.transparent-bg [data-theme="blue"] .header-bar,
.transparent-bg [data-theme="blue"] .sidebar {
  background-color: rgba(44, 79, 123, 0.7);
  backdrop-filter: blur(10px);
}

/* 常用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.p-4 {
  padding: 1rem;
}

.rounded {
  border-radius: 0.25rem;
}

.shadow {
  box-shadow: 0 4px 6px var(--shadow-color);
} 