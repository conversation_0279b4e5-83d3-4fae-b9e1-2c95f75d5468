{"data": [{"ico": "ico", "children": [{"webType": "collect", "children": [{"id": 58, "name": "萌导航", "logo": "https://asset.static.moe48.com/builds/20250330135341/d23af47124230555128ec8de84c5c873.png", "websiteAddress": "moe48.com", "classification": "游戏合集", "classificationId": 57, "icpNumber": "沪ICP备2023018523号", "descs": "涵盖视频、社区、图片等多领域资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}, {"id": 59, "name": "暖雀设计", "logo": "https://hao.nuanque.com/wp-content/themes/blackgrid/assets/images/logo.png", "websiteAddress": "hao.nuanque.com", "classification": "游戏合集", "classificationId": 57, "icpNumber": "粤ICP备18142173号", "descs": "提供AIGC工具、字体设计、摄影美图等设计资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}], "w": "3", "h": "1", "x": "0", "y": "0", "logo": "http://************:8888/file/frame.png", "type": "游戏合集"}, {"webType": "collect", "children": [{"id": 61, "name": "萌导航", "logo": "https://asset.static.moe48.com/builds/20250330135341/d23af47124230555128ec8de84c5c873.png", "websiteAddress": "moe48.com", "classification": "赛事直播", "classificationId": 60, "icpNumber": "沪ICP备2023018523号", "descs": "涵盖视频、社区、图片等多领域资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}, {"id": 62, "name": "暖雀设计", "logo": "https://hao.nuanque.com/wp-content/themes/blackgrid/assets/images/logo.png", "websiteAddress": "hao.nuanque.com", "classification": "赛事直播", "classificationId": 60, "icpNumber": "粤ICP备18142173号", "descs": "提供AIGC工具、字体设计、摄影美图等设计资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}], "w": "3", "h": "1", "x": "0", "y": "0", "logo": "http://************:8888/file/frame.png", "type": "赛事直播"}, {"webType": "collect", "children": [{"id": 64, "name": "萌导航", "logo": "https://asset.static.moe48.com/builds/20250330135341/d23af47124230555128ec8de84c5c873.png", "websiteAddress": "moe48.com", "classification": "摸鱼盲盒", "classificationId": 63, "icpNumber": "沪ICP备2023018523号", "descs": "涵盖视频、社区、图片等多领域资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}, {"id": 65, "name": "暖雀设计", "logo": "https://hao.nuanque.com/wp-content/themes/blackgrid/assets/images/logo.png", "websiteAddress": "hao.nuanque.com", "classification": "摸鱼盲盒", "classificationId": 63, "icpNumber": "粤ICP备18142173号", "descs": "提供AIGC工具、字体设计、摄影美图等设计资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}], "w": "3", "h": "1", "x": "0", "y": "0", "logo": "http://************:8888/file/frame.png", "type": "摸鱼盲盒"}, {"webType": "collect", "children": [{"id": 67, "name": "萌导航", "logo": "https://asset.static.moe48.com/builds/20250330135341/d23af47124230555128ec8de84c5c873.png", "websiteAddress": "moe48.com", "classification": "资源合集", "classificationId": 66, "icpNumber": "沪ICP备2023018523号", "descs": "涵盖视频、社区、图片等多领域资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}, {"id": 68, "name": "暖雀设计", "logo": "https://hao.nuanque.com/wp-content/themes/blackgrid/assets/images/logo.png", "websiteAddress": "hao.nuanque.com", "classification": "资源合集", "classificationId": 66, "icpNumber": "粤ICP备18142173号", "descs": "提供AIGC工具、字体设计、摄影美图等设计资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}], "w": "3", "h": "1", "x": "0", "y": "0", "logo": "http://************:8888/file/frame.png", "type": "资源合集"}, {"webType": "collect", "children": [{"id": 70, "name": "萌导航", "logo": "https://asset.static.moe48.com/builds/20250330135341/d23af47124230555128ec8de84c5c873.png", "websiteAddress": "moe48.com", "classification": "AI工具集", "classificationId": 69, "icpNumber": "沪ICP备2023018523号", "descs": "涵盖视频、社区、图片等多领域资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}, {"id": 71, "name": "暖雀设计", "logo": "https://hao.nuanque.com/wp-content/themes/blackgrid/assets/images/logo.png", "websiteAddress": "hao.nuanque.com", "classification": "AI工具集", "classificationId": 69, "icpNumber": "粤ICP备18142173号", "descs": "提供AIGC工具、字体设计、摄影美图等设计资源", "createtime": "2025-05-14 13:49:48", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "home"}], "w": "3", "h": "1", "x": "0", "y": "0", "logo": "http://************:8888/file/frame.png", "type": "AI工具集"}], "type": "首页"}, {"ico": "ico", "children": [{"id": 1, "name": "要玩网页游戏平台", "logo": "https://www.yaowan.com/Template/2016/images/logo.png", "websiteAddress": "yaowan.com", "classification": "游戏", "classificationId": 1, "icpNumber": "粤ICP备10000936号", "descs": "汇聚众多网页游戏，玩法多样满足不同玩家需求。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 2, "name": "4399游戏盒", "logo": "https://h.4399.com/images/2022/logo.png", "websiteAddress": "4399.com", "classification": "游戏", "classificationId": 1, "icpNumber": "ICP证闽B2-20040099", "descs": "一款以丰富小游戏资源闻名，找回童年记忆，适合各年龄段休闲娱乐的网站。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 3, "name": "小霸王", "logo": "https://www.yikm.net/logo.png", "websiteAddress": "yikm.net", "classification": "游戏", "classificationId": 1, "icpNumber": "粤B2-20090191-18", "descs": "充满回忆的游戏平台，包含忍者神龟格斗、魂斗罗、冒险岛等许多经典游戏。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 4, "name": "hao123 游戏", "logo": "https://hao123-static.cdn.bcebos.com/fe-res-cls2/resource/game-fe-common/widget/common-header/img/logo-game.c39e5b1.png?__sprite", "websiteAddress": "game.hao123.com", "classification": "游戏", "classificationId": 1, "icpNumber": "京ICP证030173号", "descs": "涵盖多种类型的热门游戏，包括角色扮演、传奇等，同时提供游戏资讯、新游上线等内容。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 5, "name": "17173 游戏网", "logo": "https://ue.17173cdn.com/a/www/index/2015/m/img/logo.png", "websiteAddress": "m.17173.com", "classification": "游戏", "classificationId": 1, "icpNumber": "", "descs": "1717游戏网拥有近600个游戏专区，内容全面，包括网游新闻、新游评测、游戏论坛、下载中心等，涵盖国内 95% 以上的网络游戏。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 6, "name": "摸鱼导航", "logo": "https://hu123.cc/hu123/images/moyu_logo.png", "websiteAddress": "hu123.cc/more.html", "classification": "游戏", "classificationId": 1, "icpNumber": "苏ICP备2023043116号-1", "descs": "收录遥控小车、桌游合集、免费在线农场等摸鱼游戏", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 7, "name": "Boomcatcher", "logo": "https://www.boomcatcher.com/wp-content/uploads/2024/09/1726101863-未命名设计-2.png", "websiteAddress": "boomcatcher.com", "classification": "游戏", "classificationId": 1, "icpNumber": "粤ICP备2024194495号", "descs": "小众趣味网站大合集，含创意工具、休闲游戏、白噪音等资源", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 8, "name": "Listverse", "logo": "https://listverse.com/wp-content/themes/listverse2013/assets/img/<EMAIL>", "websiteAddress": "listverse.com", "classification": "冷知识", "classificationId": 1, "icpNumber": "", "descs": "专注于人文、科学、娱乐等主题，每篇文章精选10条冷知识，内容比List25更详细，适合希望深入学习的用户，但需一定英语能力。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 15, "name": "中华珍宝馆", "logo": "https://cdn-g2.ltfc.net/wudang/cag_logo_2021_2.svg", "websiteAddress": "ltfc.net", "classification": "历史", "classificationId": 1, "icpNumber": "皖ICP备15001975号-1", "descs": "高清书画数据库，含永乐宫壁画、宋徽宗《竹禽图》等艺术珍品。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 22, "name": "AI工具集", "logo": "https://ai-bot.cn/ai-bot-logo-vector.svg", "websiteAddress": "ai-bot.cn", "classification": "AI", "classificationId": 1, "icpNumber": "蜀ICP备2022019184号-2", "descs": "汇聚千余款优质 AI 工具，助力高效工作生活", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "2", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 29, "name": "腾讯动漫", "logo": "https://gtimgcdn.ac.qq.com/media/images/ac_logo.png", "websiteAddress": "ac.qq.com", "classification": "无聊", "classificationId": 1, "icpNumber": "粤ICP备16117015号-1", "descs": "海量正版漫画资源，轻松打发闲暇时光", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": " ", "filetype": 1, "menuhometype": "public"}, {"id": 36, "name": "优酷视频", "logo": "https://img.alicdn.com/imgextra/i1/O1CN01180Rqd1u3Lo8PdgSs_!!6000000005981-55-tps-213-72.svg", "websiteAddress": "youku.com", "classification": "影音娱乐", "classificationId": 1, "icpNumber": "", "descs": "海量影视综艺视频，闲暇时光娱乐首选", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 43, "name": "地球在线", "logo": "https://map.jiqrxx.com/images/logo.gif", "websiteAddress": "map.jiqrxx.com", "classification": "街景", "classificationId": 1, "icpNumber": "", "descs": "集成高清卫星等地图，可看街景的一款只能平台", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 50, "name": "办公导航", "logo": "https://www.office999.cn/skin/hao/images/logo.png", "websiteAddress": "hao.office999.cn", "classification": "其他", "classificationId": 1, "icpNumber": "", "descs": "汇聚办公工具、模板等资源的导航平台", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}], "type": "游戏"}, {"ico": "ico", "children": [{"id": 9, "name": "观沧海", "logo": "https://ageeye-media.oss-cn-hangzhou.aliyuncs.com/logo/ageeye-small.png", "websiteAddress": "ageeye.app.ditushu.com", "classification": "冷知识", "classificationId": 2, "icpNumber": "蜀ICP备12026271号-1", "descs": "整合中国古旧地图，如上海租界地图、金庸小说地图等，结合地理与历史，提供独特视角的冷知识", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 10, "name": "知识薯条", "logo": "https://www.zhidaome.cn/wp-content/uploads/2023/04/zhidaome.png", "websiteAddress": "zhidaome.cn", "classification": "冷知识", "classificationId": 2, "icpNumber": "冀ICP备18000161号-11", "descs": "专注于生活常识动态以及各类网络娱乐资讯，是冷知识百科最全的独立博客之一，分享日常干货知识、网络资讯、生活常识。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 11, "name": "龙公网", "logo": "http://lgteacher.com/zb_users/theme/quietlee/style/images/logo.png", "websiteAddress": "lgteacher.com", "classification": "冷知识", "classificationId": 2, "icpNumber": "滇ICP备2023010826号", "descs": "精心挑选并创作一系列引人入胜的冷知识文章，内容覆盖科学奇观、历史谜团、文化逸事、自然奥秘、生活小窍门等多个领域。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 12, "name": "冷饭网", "logo": "https://www.lzs100.com/zb_users/theme/tx_jiandan/include/logo.png", "websiteAddress": "lzs100.com", "classification": "冷知识", "classificationId": 2, "icpNumber": "京ICP备2020048080号-2", "descs": "专门分享冷知识，内容涵盖天文地理、动物植物、生活百科、历史文化等丰富内容。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 13, "name": "纪妖", "logo": "https://www.cbaigui.com/favicon.ico", "websiteAddress": "cbaigui.com", "classification": "冷知识", "classificationId": 2, "icpNumber": "粤ICP备19068709号", "descs": "中国传统妖怪文化与冷知识", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 14, "name": "奇客", "logo": "https://icon.solidot.org/images/solidot-logo.png", "websiteAddress": "solidot.org", "classification": "冷知识", "classificationId": 2, "icpNumber": "京ICP证161336号", "descs": "提供海内外资讯，有不少科技冷知识", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}], "type": "冷知识"}, {"ico": "ico", "children": [{"id": 16, "name": "故宫博物馆", "logo": "https://img.dpm.org.cn/Uploads/image/2024/04/30/1714450493oMLPHAkOn1.png", "websiteAddress": "dpm.org.cn/Home.html", "classification": "历史", "classificationId": 3, "icpNumber": "京ICP备05067311号-1", "descs": "故宫文物3D展示平台，支持360度无死角观察器物细节。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 17, "name": "中国国家博物馆", "logo": "https://www.chnmuseum.cn/images/header-logo.png", "websiteAddress": "chnmuseum.cn", "classification": "历史", "classificationId": 3, "icpNumber": "京ICP备05008885号", "descs": "在线展示文物、展览及学术资源，涵盖古代至近现代文化遗产。", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 18, "name": "识典古籍", "logo": "https://lf-welfare.amemv.com/obj/douyin-welfare-image/guji/shidian/static/image/home-title.4939068c.png", "websiteAddress": "shidianguji.com", "classification": "历史", "classificationId": 3, "icpNumber": "", "descs": "北大与字节合作平台，提供6882部古籍原文译文对照及内容检索", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 19, "name": "数字敦煌网", "logo": "https://cdn.e-dunhuang.com/index/image/logo.png", "websiteAddress": "e-dunhuang.com", "classification": "历史", "classificationId": 3, "icpNumber": "陇ICP备11000088号", "descs": "30个洞窟4430平方米壁画数字化展示，支持全景漫游与沉浸体验", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 20, "name": "历史上的今天", "logo": "https://www.todayonhistory.com/static/images/logo-s.png", "websiteAddress": "todayonhistory.com", "classification": "历史", "classificationId": 3, "icpNumber": "渝ICP备2022004231号-4", "descs": "每日回顾重大历史事件，增长知识，开阔眼界", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}, {"id": 21, "name": "南京博物馆", "logo": "https://www.njmuseum.com/static/img/logo_init.24a5854d.png", "websiteAddress": "njmuseum.com", "classification": "历史", "classificationId": 3, "icpNumber": "苏ICP备05009726-1号", "descs": "展示江苏地区历史文化的重要博物馆", "createtime": "2025-05-07 17:54:45", "creator": "huqinchangpei", "reviewer": null, "reviewerDescs": null, "isSysn": 0, "updatetime": null, "reviewertime": null, "type": 0, "sorting": null, "color": null, "w": "1", "h": "1", "x": "0", "y": "0", "webType": "app", "filetype": 1, "menuhometype": "public"}], "type": "历史"}], "status": 200}