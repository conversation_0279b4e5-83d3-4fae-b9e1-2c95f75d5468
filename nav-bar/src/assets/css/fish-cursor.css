/* 鱼形光标全局样式 */

/* 将SVG转为data URL格式 */
:root {
  --fish-cursor: url('/cursors/11zon_fish-cursor.cur') 0 5, auto;
  --fish-cursor-pointer: url('/cursors/11zon_fish-cursor.cur') 0 5, pointer;
}

/* 默认光标 */
* {
  cursor: var(--fish-cursor) !important;
}

/* 可交互元素光标 */
a, button, [type="button"], [type="submit"], [type="reset"], [type="file"], 
[role="button"], .clickable, .button, .ant-btn, 
.anticon, details > summary, select, option, 
[disabled="false"], .link, 
input[type="checkbox"], input[type="radio"], 
.ant-checkbox, .ant-radio, .ant-switch, 
.ant-select-selector, .ant-menu-item, 
.ant-tabs-tab, .ant-dropdown-menu-item {
  cursor: var(--fish-cursor-pointer) !important;
}

/* 禁用元素 */
[disabled], .disabled, .ant-btn[disabled], 
button:disabled, a.disabled, .ant-btn-disabled {
  cursor: not-allowed !important;
}

/* 文本和文本输入元素 */
input[type="text"], input[type="password"], 
input[type="email"], input[type="number"], 
input[type="search"], input[type="tel"], 
input[type="url"], textarea, .ant-input, 
[contenteditable="true"] {
  cursor: text !important;
}