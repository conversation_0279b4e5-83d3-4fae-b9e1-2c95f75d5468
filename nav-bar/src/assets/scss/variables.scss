// 主题变量
:root {
  // 默认亮色主题
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --accent-color: #0d6efd;
  --accent-color-rgb: 13, 110, 253;
  --border-color: #dee2e6;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --card-bg: rgba(255, 255, 255, 0.8);
  
  // 动画时间
  --transition-speed: 0.3s;
}

// 暗色主题
[data-theme="dark"] {
  --bg-primary: #1a2236;
  --bg-secondary: #252e43;
  --text-primary: #ffffff;
  --text-secondary: #c7c7c7;
  --accent-color: #00c3ff;
  --accent-color-rgb: 0, 195, 255;
  --border-color: #2d3a56;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --card-bg: rgba(37, 46, 67, 0.8);
  
  // MIUI特定颜色
  --miui-gradient-start: #1a2236;
  --miui-gradient-mid: #2d3a56;
  --miui-gradient-end: #384866;
  --miui-card-bg: rgba(255, 255, 255, 0.15);
  --miui-icon-size: 54px;
  --miui-radius: 16px;
}

// 蓝色主题
[data-theme="blue"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e3a8a;
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --accent-color: #38bdf8;
  --accent-color-rgb: 56, 189, 248;
  --border-color: #2563eb;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --card-bg: rgba(30, 58, 138, 0.8);
  
  // MIUI特定颜色
  --miui-gradient-start: #0f172a;
  --miui-gradient-mid: #1e3a8a;
  --miui-gradient-end: #2563eb;
  --miui-card-bg: rgba(255, 255, 255, 0.15);
  --miui-icon-size: 54px;
  --miui-radius: 16px;
} 