@use './variables.scss' as *;
@use "sass:map";

/* 主题颜色 */
$themes: (
  light: (
    background: #ffffff,
    text-color: #333333,
    accent-color: #3b82f6,
    card-bg: #ffffff,
    border-color: #e5e7eb,
    hover-bg: #f3f4f6,
    gradient-start: #f1f5f9,
    gradient-mid: #e2e8f0,
    gradient-end: #cbd5e1,
    shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    icon-color: #64748b
  ),
  dark: (
    background: #111827,
    text-color: #f3f4f6,
    accent-color: #60a5fa,
    card-bg: #1f2937,
    border-color: #374151,
    hover-bg: #374151,
    gradient-start: #0f172a,
    gradient-mid: #1e293b,
    gradient-end: #334155,
    shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
    icon-color: #94a3b8
  ),
  blue: (
    background: #eef2ff,
    text-color: #312e81,
    accent-color: #4f46e5,
    card-bg: #e0e7ff,
    border-color: #a5b4fc,
    hover-bg: #c7d2fe,
    gradient-start: #c7d2fe,
    gradient-mid: #a5b4fc,
    gradient-end: #818cf8,
    shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2),
    icon-color: #4f46e5
  )
);

/* 断点 */
$breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px
);

/* 响应式混合器 */
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "没有找到该断点：#{$breakpoint}";
  }
}

/* 字体尺寸 */
$font-sizes: (
  xs: 0.75rem,
  sm: 0.875rem,
  base: 1rem,
  lg: 1.125rem,
  xl: 1.25rem,
  2xl: 1.5rem,
  3xl: 1.875rem
);

/* 间距 */
$spacing: (
  0: 0,
  1: 0.25rem,
  2: 0.5rem,
  3: 0.75rem,
  4: 1rem,
  5: 1.25rem,
  6: 1.5rem,
  8: 2rem,
  10: 2.5rem,
  12: 3rem,
  16: 4rem,
  20: 5rem
);

/* 阴影 */
$shadows: (
  sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05),
  base: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
  md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
  lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
);

/* 边框圆角 */
$border-radius: (
  none: 0,
  sm: 0.125rem,
  base: 0.25rem,
  md: 0.375rem,
  lg: 0.5rem,
  xl: 0.75rem,
  full: 9999px
);

/* 过渡 */
$transitions: (
  fast: 0.2s ease,
  normal: 0.3s ease,
  slow: 0.5s ease
);

/* 全局动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

// 全局样式
body {
  line-height: 1.6;
  font-family: Inter, Arial, sans-serif;
  
  &.dark-theme {
    --bg-color: #121212;
    --text-color: #f5f5f5;
    --border-color: #2d2d2d;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  &.light-theme {
    --bg-color: #f5f5f5;
    --text-color: #121212;
    --border-color: #e0e0e0;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// 毛玻璃效果
@mixin glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

// 基础阴影
@mixin base-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// 容器类
.container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
  
  @include respond-to('sm') {
    max-width: 540px;
  }
  
  @include respond-to('md') {
    max-width: 720px;
  }
  
  @include respond-to('lg') {
    max-width: 960px;
  }
  
  @include respond-to('xl') {
    max-width: 1140px;
  }
}

// 卡片淡入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease forwards;
}

// 滚动触发动画的类
.scroll-animate {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.in-view {
    opacity: 1;
    transform: translateY(0);
  }
} 