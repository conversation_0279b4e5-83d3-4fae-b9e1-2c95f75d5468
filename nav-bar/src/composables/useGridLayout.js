import { ref, computed, nextTick } from 'vue'
import { GridUtils, defaultCalculator } from '@/utils/gridLayoutCalculator'
import { folderGridCalculator } from '@/utils/folderGridCalculator'

/**
 * 网格布局和尺寸计算功能
 * @returns {Object} - 返回网格布局相关的状态和方法
 */
export function useGridLayout() {
  // 网格列数响应式变量（支持异步加载）
  const gridColumns = ref(12) // 默认值

  // 智能网格信息展示
  const gridInfo = ref(null)

  // 网格间距
  const gridGap = computed(() => 30)

  // 异步初始化网格列数
  async function initGridColumns(isMobile, isTablet, isLandscape) {
    try {
      const params = await calculateOptimalGridParams(isMobile, isTablet, isLandscape)
      gridColumns.value = params.columns
    } catch (error) {
      gridColumns.value = 12
    }
  }

  // 计算最佳网格参数 - 使用新的智能计算器（异步版本）
  async function calculateOptimalGridParams(isMobile, isTablet, isLandscape) {
    // 平板设备专用16列计算逻辑
    if (isTablet.value) {
      return calculateTabletGridParams(isLandscape)
    }

    // 使用新的网格计算器，传入手机模式状态
    const result = await GridUtils.quickCalculate({
      isMobile: isMobile.value
    })

    // 转换为原有格式以保持兼容性
    const optimalParams = {
      columns: result.N,      // 列数
      iconSize: result.X1,    // 图标尺寸
      gap: result.X2,         // 间距尺寸
      gridWidth: result.W3,   // 网格总宽度
      windowRatio: result.P,  // 窗口占比
      gridRatio: result.Q     // 网格占比
    }

    // 不再进行缩放，固定显示1.00
    const scaleRatio = '1.00'
    const dpr = result.deviceInfo?.devicePixelRatio || window.devicePixelRatio || 1

    // 更新网格信息展示
    const gridInfoData = {
      设备类型: isMobile.value ? '手机' : isTablet.value ? '平板' : '桌面',
      桌面分辨率: `${result.W1}px`,
      浏览器宽度: `${result.W2}px`,
      窗口占比: `${(result.P * 100).toFixed(1)}%`,
      分辨率类型: result.resolutionName,
      列数: result.N,
      图标尺寸: `${result.X1}px`,
      缩放倍数: `${scaleRatio}x (DPR: ${dpr})`,
      真实尺寸: result.X1 + 'px',
      间距: `${result.X2}px`,
      网格宽度: `${result.W3}px`,
      网格占比: `${(result.Q * 100).toFixed(1)}%`,
      文字大小: `${result.fontSize}px`,
      行高: result.lineHeight,
      字体粗细: result.fontWeight,
      文字样式描述: result.fontStyleDescription,
      手机模式检测: `窗口宽度${result.W2}px ${result.W2 <= 480 ? '≤' : '>'} 480px`
    }
    // 更新响应式数据
    gridInfo.value = gridInfoData

    return optimalParams
  }

  // 平板设备专用网格参数计算（支持横竖屏）
  function calculateTabletGridParams(isLandscape) {
    // 检测屏幕方向并设置对应的列数
    const targetColumns = isLandscape.value ? 16 : 10 // 横屏16列，竖屏10列
    const gapRatio = 0.5 // 间隙比例，与默认配置保持一致

    // 获取可用屏幕宽度（逻辑像素）
    const screenWidth = window.innerWidth

    // 边界条件检查：确保屏幕宽度有效
    if (!screenWidth || screenWidth < 480) {
      console.warn('⚠️ 平板模式检测到异常屏幕宽度，回退到默认参数')
      return {
        columns: 6,
        iconSize: 40,
        gap: 20,
        gridWidth: 400,
        windowRatio: 0.8,
        gridRatio: 0.8
      }
    }

    const containerPadding = 40 // 预留左右边距
    const availableWidth = screenWidth - containerPadding

    // 反向计算图标尺寸：(可用宽度) / (列数 + (列数-1) * 间隙比例)
    const calculatedIconSize = Math.floor(availableWidth / (targetColumns + (targetColumns - 1) * gapRatio))

    // 设置图标尺寸边界限制
    const minIconSize = 20 // 最小图标尺寸
    const maxIconSize = 80 // 最大图标尺寸，避免过大

    // 应用边界限制
    let iconSize = Math.max(minIconSize, Math.min(maxIconSize, calculatedIconSize))

    // 额外检查：如果计算出的图标尺寸太小，适当调整列数
    if (calculatedIconSize < minIconSize) {
      console.warn(`⚠️ 平板16列模式图标过小(${calculatedIconSize}px)，保持最小尺寸${minIconSize}px`)
      iconSize = minIconSize
    }

    // 计算实际间隙尺寸
    const gap = Math.max(1, Math.floor(iconSize * gapRatio)) // 确保间隙至少为1px

    // 计算实际网格总宽度
    const gridWidth = iconSize * targetColumns + gap * (targetColumns - 1)

    // 计算网格占比，添加边界检查
    const gridRatio = availableWidth > 0 ? gridWidth / availableWidth : 1
    return {
      columns: targetColumns,
      iconSize: iconSize,
      gap: gap,
      gridWidth: gridWidth,
      windowRatio: availableWidth / screenWidth,
      gridRatio: gridRatio
    }
  }

  // 获取当前列数（用于拖拽等逻辑）
  function getColumnsCount() {
    return gridColumns.value
  }

  // 根据文件夹项目数量计算能显示的项目数量（使用新的计算器）
  function calculateFolderMaxItems(folder) {
    if (!folder || !folder.children) return 3 // 默认显示3个（1行）

    const itemCount = folder.children.length
    const folderSize = folder.size || { w: 2, h: 2 }

    // 使用新的文件夹网格计算器
    const containerWidth = 120
    const containerHeight = 120

    const layout = folderGridCalculator.calculateOptimalLayout(
      containerWidth,
      containerHeight,
      itemCount,
      folderSize  // 传递文件夹尺寸
    )

    return layout.maxItems
  }

  // 获取文件夹显示的子项目列表
  function getFolderDisplayItems(folder) {
    if (!folder || !folder.children) return []

    const maxItems = calculateFolderMaxItems(folder)
    return folder.children.slice(0, maxItems)
  }

  // 获取文件夹网格的动态样式（使用新的文件夹网格计算器）
  function getFolderGridStyle(folder) {
    const folderSize = folder?.size || { w: 2, h: 2 }

    if (!folder || !folder.children || folder.children.length === 0) {
      // 根据文件夹尺寸设置默认样式
      let widthPercent, heightPercent
      if (folderSize.w === 1 && folderSize.h === 1) {
        widthPercent = '70%'
        heightPercent = '70%'
      } else if (folderSize.h === 1) {
        widthPercent = '80%'
        heightPercent = '80%'
      } else {
        widthPercent = '85%'
        heightPercent = '85%'
      }

      // 3x1布局特殊处理：使用4列
      const columns = (folderSize.w === 3 && folderSize.h === 1) ? 4 : 3

      return {
        'display': 'grid',
        'grid-template-columns': `repeat(${columns}, 1fr)`,
        'grid-template-rows': 'repeat(1, 1fr)',
        'gap': '6px',
        'width': widthPercent,
        'height': heightPercent,
        'padding': '8px',
        'place-items': 'center',
        'place-content': 'start'
      }
    }

    const itemCount = folder.children.length

    // 使用新的文件夹网格计算器
    // 假设文件夹容器大小为 120x120px（可以根据实际情况调整）
    const containerWidth = 120
    const containerHeight = 120

    const layout = folderGridCalculator.calculateOptimalLayout(
      containerWidth,
      containerHeight,
      itemCount,
      folderSize  // 传递文件夹尺寸
    )

    return {
      'display': 'grid',
      'grid-template-columns': `repeat(${layout.columns}, 1fr)`,
      'grid-template-rows': `repeat(${layout.rows}, 1fr)`,
      'gap': `${layout.gap}px`,
      'width': layout.gridStyle.width,
      'height': layout.gridStyle.height,
      'padding': '8px',
      'place-items': 'center',
      'place-content': 'start',
      'box-sizing': 'border-box'
    }
  }

  // 获取可用的网格模式
  function getAvailableGridModes() {
    return defaultCalculator.getAvailablePresets()
  }

  return {
    gridColumns,
    gridInfo,
    gridGap,
    initGridColumns,
    calculateOptimalGridParams,
    calculateTabletGridParams,
    getColumnsCount,
    calculateFolderMaxItems,
    getFolderDisplayItems,
    getFolderGridStyle,
    getAvailableGridModes
  }
}