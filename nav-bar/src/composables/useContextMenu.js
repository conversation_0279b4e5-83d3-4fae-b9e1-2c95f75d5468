import { ref, toRef } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { h } from 'vue'
import { useWallpaperStore } from '@/stores/wallpaper'

/**
 * 通用右键菜单逻辑
 * @param {Object} options - 配置选项
 * @param {Function} options.saveAppOrder - 保存应用顺序的函数
 * @param {Object} options.dockApps - Dock栏应用列表的ref对象
 * @returns {Object} - 返回右键菜单相关的状态和方法
 */
export function useContextMenu(options = {}) {
  // 解构配置选项
  const { saveAppOrder, dockApps = ref([]) } = options

  // 右键菜单相关变量
  const showMenu = ref(false)
  const menuPosition = ref({ top: '0px', left: '0px' })
  const activeApp = ref(null)

  // 编辑模式状态
  const isEditMode = ref(false)

  // 显示上下文菜单
  function showContextMenu(event, app) {
    // 如果是编辑模式，不显示右键菜单
    if (isEditMode.value) {
      return
    }
    // 记录当前操作的应用，如果没有传入app则为null（空白区域点击）
    activeApp.value = app || null
    // 如果有应用对象，记录是否是Dock栏中的项目
    if (app) {
      const isInDock = dockApps.value.some(dockApp => dockApp.id === app.id)
      activeApp.value.isInDock = isInDock
    }
    // 设置菜单位置
    const x = event.clientX
    const y = event.clientY
    // 菜单宽度
    const menuWidth = 200
    // 动态高度：根据菜单内容类型判断
    let menuHeight = 300
    if (!app) {
      menuHeight = 120
    } else if (activeApp.value && activeApp.value.isInDock) {
      menuHeight = 340 // Dock菜单项更多时可适当加大
    }
    // 计算下方剩余空间
    const spaceBelow = window.innerHeight - y
    let top = y
    // 如果下方空间不足，菜单往上弹出
    if (spaceBelow < menuHeight && y > menuHeight) {
      top = y - menuHeight
    } else if (spaceBelow < menuHeight) {
      // 如果上方空间也不够，贴底显示
      top = window.innerHeight - menuHeight - 10
      if (top < 0) top = 10 // 防止超出顶部
    }
    // 横向同理，防止超出右侧
    let left = x
    if (x + menuWidth > window.innerWidth - 10) {
      left = window.innerWidth - menuWidth - 10
      if (left < 0) left = 10
    }
    menuPosition.value = {
      left: `${left}px`,
      top: `${top}px`
    }
    // 显示菜单
    showMenu.value = true
  }

  // 隐藏右键菜单
  function hideContextMenu() {
    showMenu.value = false
  }

  // 切换编辑模式
  function toggleEditMode() {
    isEditMode.value = !isEditMode.value
    showMenu.value = false // 关闭菜单
  }

  // 确认删除项目
  function confirmDeleteItem(item, folderItems) {
    
    folderItems = toRef(folderItems)
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 ${item.name} 吗？`,
      okText: '确认',
      cancelText: '取消',
      centered: true, // 让对话框垂直居中显示
      onOk() {
        

        // 从folderItems中删除
        if (folderItems.value) {
          const itemIndex = folderItems.value.findIndex(i => i.id === item.id)
          

          if (itemIndex !== -1) {
            folderItems.value.splice(itemIndex, 1)
            
          } else {
            
          }
          // 保存到localStorage
          localStorage.setItem('officeLayoutData', JSON.stringify(folderItems.value))
          
        } else {
          
        }
        // 如果有保存函数，调用它
        if (typeof saveAppOrder === 'function') {
          saveAppOrder()
        }
      }
    })
  }

  // 创建新文件夹的函数
  function createFolder(folderItems) {
    // 创建一个变量来存储输入值
    let folderNameValue = '新建文件夹';

    // 使用Modal弹出一个对话框，让用户输入文件夹名称
    Modal.confirm({
      title: '创建文件夹',
      centered: true, // 让对话框垂直居中显示
      content: h('div', {}, [
        h('p', '请输入文件夹名称：'),
        h('input', {
          style: {
            width: '100%',
            marginTop: '8px',
            padding: '8px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          },
          value: folderNameValue,
          onInput: (e) => {
            folderNameValue = e.target.value;
          },
          onMounted: (el) => {
            // 自动选中输入框内容
            setTimeout(() => {
              el.select();
            }, 100);
          }
        })
      ]),
      onOk: () => {
        // 使用存储的值作为文件夹名称
        const folderName = folderNameValue || '新建文件夹';
        
        // 生成唯一ID
        const uniqueId = `folder-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        
        // 创建一个新的文件夹
        const newFolder = {
          id: uniqueId,
          name: folderName,
          type: 'folder',
          icon: '📁',
          size: {
            w: 2,
            h: 2
          },
          color: '#f5ba42',
          children: []
        }
        
        // 添加到文件夹列表
        if (folderItems && folderItems.value) {
          folderItems.value.push(newFolder)

          // 保存到localStorage
          localStorage.setItem('officeLayoutData', JSON.stringify(folderItems.value))
        }

        // 关闭右键菜单
        showMenu.value = false

        // 显示成功提示
        message.success(`${folderName} 创建成功！`)
      },
      okText: '创建',
      cancelText: '取消',
      width: 360
    });
  }

  // 设置应用尺寸
  function setAppSize(width, height, folderItems) {
    // 如果参数是一个对象（从ContextMenu组件传入），则解构出width和height
    if (typeof width === 'object' && width !== null) {
      height = width.height;
      width = width.width;
    }

    if (!activeApp.value) return

    // 如果尺寸没有变化，直接返回
    if (activeApp.value.size?.w === width && activeApp.value.size?.h === height) {
      showMenu.value = false
      return
    }

    // 更新应用的尺寸
    activeApp.value.size = { w: width, h: height }

    // 查找应用在当前分类列表中的位置并更新
    if (folderItems && folderItems.value) {
      const itemIndex = folderItems.value.findIndex(item => item.id === activeApp.value.id)
      if (itemIndex !== -1) {
        folderItems.value[itemIndex].size = `${width}x${height}`
      }

      // 保存到localStorage
      localStorage.setItem('officeLayoutData', JSON.stringify(folderItems.value))
    }

    // 关闭菜单
    showMenu.value = false

    // 如果有保存函数，调用它
    if (typeof saveAppOrder === 'function') {
      saveAppOrder()
    }
  }

  // 添加到Dock栏
  function addToDock(app) {
    let dockList = dockApps.value.filter(item => item.id !== app.id)
    dockList.push(JSON.parse(JSON.stringify({
      id: app.id,
      name: app.name,
      icon: app.icon,
      color: app.color,
      description: app.description,
      url: app.url,
      websiteAddress: app.websiteAddress,
      iscanopen: app.iscanopen,
      type: app.type,
    })))
    // 保存所有应用到数组，DockBar组件会自动只显示最后12个
    dockApps.value = dockList
    localStorage.setItem('officeDockApps', JSON.stringify(dockList))
  }

  // 从Dock栏移除
  function removeFromDock(app) {
    let dockList = dockApps.value.filter(item => item.id !== app.id)
    dockApps.value = dockList
    localStorage.setItem('officeDockApps', JSON.stringify(dockList))
  }

  // 设置打开方式
  function setOpenMode(mode, folderItems) {
    if (!activeApp.value) return
    
    // 更新应用的打开方式
    activeApp.value.openMode = mode
    
    // 更新folderItems中对应的应用
    if (folderItems && folderItems.value) {
      const itemIndex = folderItems.value.findIndex(item => item.id === activeApp.value.id)
      if (itemIndex !== -1) {
        folderItems.value[itemIndex].openMode = mode
      }

      // 保存到localStorage
      localStorage.setItem('officeLayoutData', JSON.stringify(folderItems.value))
    }

    // 关闭菜单
    showMenu.value = false

    // 如果有保存函数，调用它
    if (typeof saveAppOrder === 'function') {
      saveAppOrder()
    }
  }

  // 移动应用到分类
  function moveAppToCategory(category) {
    showMenu.value = false
    // 这个函数需要在具体的视图中实现，因为Home和Office的分类系统可能不同
  }

  // 刷新壁纸
  function changeWallpaper() {
    // 获取wallpaper store实例
    const wallpaperStore = useWallpaperStore()

    // 强制使用随机API刷新壁纸（专门用于Office页面）
    wallpaperStore.forceRefreshRandomWallpaper()

    // 显示成功提示
    message.success('壁纸刷新中...')

    // 关闭菜单
    showMenu.value = false
  }

  // 删除应用
  function deleteApp(folderItems) {
    if (!activeApp.value) return
    
    confirmDeleteItem(activeApp.value, folderItems)
  }

  // 编辑图标
  function showEditIconModal() {
    showMenu.value = false
    // 这个函数需要在具体的视图中实现
  }

  // 切换纯净模式
  function togglePureMode(isPureModeActive) {
    showMenu.value = false
    // 这个函数需要在具体的视图中实现
  }

  return {
    showMenu,
    menuPosition,
    activeApp,
    isEditMode,
    showContextMenu,
    hideContextMenu,
    toggleEditMode,
    confirmDeleteItem,
    createFolder,
    setAppSize,
    addToDock,
    removeFromDock,
    setOpenMode,
    moveAppToCategory,
    changeWallpaper,
    deleteApp,
    showEditIconModal,
    togglePureMode
  }
} 