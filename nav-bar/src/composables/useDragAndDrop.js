import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { saveFolderApp } from '@/api/folder'
import { useNavigationStore } from '@/stores/navigation'

/**
 * 拖拽和放置功能
 * @returns {Object} - 返回拖拽相关的状态和方法
 */
export function useDragAndDrop() {
  // 当前被拖拽的应用
  const draggedApp = ref(null)
  // 是否从文件夹中拖拽
  const isDraggingFromFolder = ref(false)

  // 处理拖拽开始
  function handleDragStart(event, app, fromFolder = false, activeFolder = null) {
    // 设置被拖拽的应用
    draggedApp.value = app
    isDraggingFromFolder.value = fromFolder

    // 设置数据传输对象
    event.dataTransfer.setData('application/json', JSON.stringify({
      id: app.id,
      fromFolder: fromFolder,
      folderID: fromFolder && activeFolder?.value ? activeFolder.value.id : null
    }))

    // 设置拖拽图片和效果
    event.dataTransfer.effectAllowed = 'move'
  }

  // 使得应用项可以拖拽
  function makeAppItemsDraggable(navigationStore, shouldDisableDrag) {
    // 手机和平板设备不启用拖拽功能
    if (shouldDisableDrag.value) return;

    // 为所有app-item添加draggable属性
    const appItems = document.querySelectorAll('.app-item:not([data-is-folder="true"])')
    appItems.forEach(item => {
      const appId = item.getAttribute('data-id')
      const app = navigationStore.getAppById(appId)

      if (app && app.type !== 'folder' && app.type !== 'collection') {
        item.setAttribute('draggable', 'true')

        // 确保事件监听器不重复
        item.removeEventListener('dragstart', item._dragStartHandler)

        // 创建新的事件处理函数
        item._dragStartHandler = (event) => {
          handleDragStart(event, app, false)
        }

        // 添加拖拽开始事件
        item.addEventListener('dragstart', item._dragStartHandler)
      }
    })

    // 为所有文件夹类型的app-item添加拖拽放置区域（只有文件夹，合集不可接收）
    const folderItems = document.querySelectorAll('.app-item[data-is-folder="true"]')

    folderItems.forEach(item => {
      const folderId = item.getAttribute('data-id')
      const folderApp = navigationStore.getAppById(folderId)
      const isFolder = folderApp?.type === 'folder'

      // 只为文件夹类型添加拖拽功能，合集不接收拖放
      if (isFolder) {
        // 添加标记
        item.setAttribute('data-folder-drop-zone', 'true')

        // 移除之前的事件监听器
        if (item._dragoverHandler) item.removeEventListener('dragover', item._dragoverHandler)
        if (item._dragleaveHandler) item.removeEventListener('dragleave', item._dragleaveHandler)
        if (item._dropHandler) item.removeEventListener('drop', item._dropHandler)

        // 创建新的事件处理函数
        item._dragoverHandler = (event) => {
          event.preventDefault()
          event.stopPropagation()
          item.classList.add('folder-drag-over')
        }

        item._dragleaveHandler = () => {
          item.classList.remove('folder-drag-over')
        }

        item._dropHandler = (event) => {
          event.preventDefault()
          event.stopPropagation()
          item.classList.remove('folder-drag-over')

          try {
            // 获取拖拽数据
            const data = JSON.parse(event.dataTransfer.getData('application/json'))
            handleDropOnFolder(data.id, folderId, data.fromFolder, data.folderID)
          } catch (error) {
            console.error('处理拖拽数据失败:', error)
          }
        }

        // 添加拖拽事件
        item.addEventListener('dragover', item._dragoverHandler)
        item.addEventListener('dragleave', item._dragleaveHandler)
        item.addEventListener('drop', item._dropHandler)
      }
    })
  }

  // 处理拖放到文件夹
  async function handleDropOnFolder(appId, folderId, fromFolder, sourceFolderId) {
    const navigationStore = useNavigationStore()

    // 如果没有有效应用ID，返回
    if (!appId) return

    // 检查拖拽的应用和目标文件夹
    const draggedApp = navigationStore.getAppById(appId)
    const targetFolder = navigationStore.getAppById(folderId)

    // 检查目标文件夹内是否已存在相同ID的应用
    if (draggedApp && targetFolder && targetFolder.children) {
      const existingApp = targetFolder.children.find(child => child && child.id === draggedApp.id)
      if (existingApp) {
        message.error(`文件夹内已存在相同应用`)
        return
      }
    }

    const token = localStorage.getItem('token')
    if (token) {
      try {
        const res = await saveFolderApp(folderId, appId)
        if (res.status == 200) {
          console.log('添加成功')
        }
      } catch (error) {
        console.error('保存文件夹应用失败:', error)
      }
    }

    // 使用Navigation Store的getAppById函数获取应用和目标文件夹
    const app = navigationStore.getAppById(appId)

    // 通过ID直接从全局应用列表和当前分类中获取目标文件夹
    // 而不仅仅使用getAppById函数，确保获取到正确的文件夹对象
    let folder = null

    // 先从当前分类中查找
    const currentCategoryApps = navigationStore.categoryApps[navigationStore.currentCategory] || []

    const folderInCategory = currentCategoryApps.find(a => a && a.id && a.id === folderId)
    if (folderInCategory && folderInCategory.type === 'folder') {
      folder = folderInCategory
    } else {
      // 如果当前分类没有，使用Navigation Store查找
      folder = navigationStore.getAppById(folderId)
    }

    // 如果应用不存在或文件夹不存在，返回
    if (!app || !folder || folder.type !== 'folder') {
      console.error('🚨 拖拽错误 - 应用或文件夹不存在:', {
        app,
        folder,
        appId,
        folderId,
        currentDataSource: navigationStore.currentDataSource,
        allAppsPreview: navigationStore.allApps.slice(0, 5).map(a => ({ id: a.id, name: a.name, type: a.type })),
        categoryAppsPreview: navigationStore.categoryApps[navigationStore.currentCategory]?.slice(0, 5).map(a => ({ id: a.id, name: a.name, type: a.type })) || []
      })
      return
    }

    // 文件夹只能添加应用类型，其他类型不允许
    if (app.type !== 'app') {
      message.error(`文件夹只能添加应用，不能添加${app.type === 'folder' ? '文件夹' : app.type === 'collection' ? '应用合集' : app.type === 'card' ? '组件' : '其他类型'}`)
      return
    }

    // 创建一个应用的副本，避免引用问题
    const appCopy = JSON.parse(JSON.stringify(app))

    // 如果应用是从文件夹中拖出的
    if (fromFolder && sourceFolderId) {
      const sourceFolder = navigationStore.getAppById(sourceFolderId)
      if (sourceFolder && sourceFolder.children) {
        // 从源文件夹中移除
        const index = sourceFolder.children.findIndex(a => a && a.id && a.id === appId)
        if (index !== -1) {
          sourceFolder.children.splice(index, 1)
        }
      }
    } else {
      // 从主网格中移除
      const currentCategoryAppsForRemoval = navigationStore.categoryApps[navigationStore.currentCategory] || []
      const categoryIndex = currentCategoryAppsForRemoval.findIndex(a => a && a.id && a.id === appId)
      if (categoryIndex !== -1) {
        navigationStore.categoryApps[navigationStore.currentCategory].splice(categoryIndex, 1)
      }
    }

    // 确保文件夹有children数组
    if (!folder.children) {
      folder.children = []
    }

    // 添加到目标文件夹
    folder.children.push(appCopy)

    // 保存更新
    navigationStore.saveAllCategoryData()

    // 如果文件夹正在显示中，更新文件夹内容
    // 注意：这个逻辑需要在组件中处理，因为我们无法访问组件的状态

    // 显示成功提示
    message.success(`已将 ${app.name} 添加到 ${folder.name} 文件夹!`)
  }

  return {
    draggedApp,
    isDraggingFromFolder,
    handleDragStart,
    makeAppItemsDraggable,
    handleDropOnFolder
  }
}