import { ref } from 'vue'
import { useNavigationStore } from '@/stores/navigation'

/**
 * 滑动切换分类功能
 * @returns {Object} - 返回滑动切换相关的状态和方法
 */
export function useSwipeNavigation() {
  // 滚轮冷却，防止多次触发
  const wheelCooldown = ref(false)
  // 分类切换状态，防止重复触发
  const isChangingCategory = ref(false)
  // 滑动提示状态
  const showSlideHint = ref(false)
  let slideHintTimer = null

  // 添加更严格的滚动速度限制
  const lastScrollTime = ref(0)
  const SCROLL_COOLDOWN_TIME = 800 // 滚动冷却时间，毫秒

  /**
   * 切换到下一个分类（循环）- 简化版带动画
   */
  function switchToNextCategory(currentMenuType, menuCategories, emitter) {
    // 触发侧边栏鼠标进入事件
    emitter.emit('scroll-category-switch')
    // 如果已经在切换中，不执行
    if (isChangingCategory.value || wheelCooldown.value) return;

    const navigationStore = useNavigationStore();
    
    // 确保使用当前数据源的最新分类数据
    const currentDataSourceCategories = navigationStore?.allNavItems || [];

    // 检查数据源一致性和数据是否已初始化
    if (!currentDataSourceCategories || currentDataSourceCategories.length === 0) {
      console.warn('[切换分类] 当前数据源分类数据尚未初始化，忽略切换请求');
      console.warn('[切换分类] 当前状态:', {
        currentDataSource: navigationStore?.currentDataSource,
        navigationStoreAllNavItems: currentDataSourceCategories?.length || 0,
        currentMenuType: currentMenuType.value,
        navigationStoreCurrentCategory: navigationStore?.currentCategory
      });
      return;
    }

    // 更新本地分类数据为当前数据源的最新数据
    menuCategories.value = currentDataSourceCategories.map(item => ({
      type: item.type,
      icon: item.ico
    }));

    // 确保当前分类与 navigationStore 同步
    if (!currentMenuType.value && navigationStore.currentCategory) {
      console.warn('[切换分类] 同步 currentMenuType 从 navigationStore');
      currentMenuType.value = navigationStore.currentCategory;
    }

    // 如果当前分类不在当前数据源的分类列表中，使用第一个分类
    const currentCategoryExists = menuCategories.value.some(c => c.type === currentMenuType.value);
    if (!currentCategoryExists && menuCategories.value.length > 0) {
      console.warn('[切换分类] 当前分类不存在于当前数据源，使用第一个分类作为默认值');
      currentMenuType.value = menuCategories.value[0].type;
    }

    // 设置冷却状态
    wheelCooldown.value = true;
    isChangingCategory.value = true;

    const idx = menuCategories.value.findIndex(c => c.type === currentMenuType.value)

    if (idx >= 0) {
      const nextIdx = (idx + 1) % menuCategories.value.length // 循环切换
      const nextType = menuCategories.value[nextIdx].type

      // 获取app-grid元素并添加向上滑动动画
      const appGrid = document.querySelector('.app-grid');
      if (appGrid) {
        // 添加动画类
        appGrid.classList.add('slide-up-animation');

        // 动画结束后执行切换
        setTimeout(() => {
          // 移除动画类
          appGrid.classList.remove('slide-up-animation');

          // 切换分类
          window.dispatchEvent(new CustomEvent('menuTypeChanged', { detail: { type: nextType } }))

          scrollToTop()

          // 延长重置时间，确保切换完全完成
          setTimeout(() => {
            wheelCooldown.value = false;
            isChangingCategory.value = false;
          }, 300); // 增加延迟，确保切换完全完成
        }, 400); // 等待动画完成
      } else {
        // 如果找不到app-grid元素，直接切换
        window.dispatchEvent(new CustomEvent('menuTypeChanged', { detail: { type: nextType } }))

        scrollToTop()

        setTimeout(() => {
          wheelCooldown.value = false;
          isChangingCategory.value = false;
        }, 500);
      }
    } else {
      console.warn(`[切换分类] 无法找到当前分类 ${currentMenuType.value} 的索引，当前分类列表:`, menuCategories.value.map(c => c.type));
      console.warn(`[切换分类] 当前分类类型: ${currentMenuType.value}`);

      // 如果找不到当前分类，尝试使用第一个分类
      if (menuCategories.value.length > 0) {
        const firstType = menuCategories.value[0].type;
        console.warn(`[切换分类] 使用第一个分类作为备选: ${firstType}`);
        currentMenuType.value = firstType;
        // 重新尝试切换
        setTimeout(() => {
          wheelCooldown.value = false;
          isChangingCategory.value = false;
          switchToNextCategory(currentMenuType, menuCategories, emitter);
        }, 100);
      } else {
        wheelCooldown.value = false;
        isChangingCategory.value = false;
      }
    }
  }

  /**
   * 切换到上一个分类（循环）- 简化版带动画
   */
  function switchToPreviousCategory(currentMenuType, menuCategories, emitter) {
    // 触发侧边栏鼠标进入事件
    emitter.emit('scroll-category-switch')
    // 如果已经在切换中，不执行
    if (isChangingCategory.value || wheelCooldown.value) return;

    const navigationStore = useNavigationStore();
    
    // 确保使用当前数据源的最新分类数据
    const currentDataSourceCategories = navigationStore?.allNavItems || [];

    // 检查数据源一致性和数据是否已初始化
    if (!currentDataSourceCategories || currentDataSourceCategories.length === 0) {
      console.warn('[切换分类] 当前数据源分类数据尚未初始化，忽略切换请求');
      console.warn('[切换分类] 当前状态:', {
        currentDataSource: navigationStore?.currentDataSource,
        navigationStoreAllNavItems: currentDataSourceCategories?.length || 0,
        currentMenuType: currentMenuType.value,
        navigationStoreCurrentCategory: navigationStore?.currentCategory
      });
      return;
    }

    // 更新本地分类数据为当前数据源的最新数据
    menuCategories.value = currentDataSourceCategories.map(item => ({
      type: item.type,
      icon: item.ico
    }));

    // 确保当前分类与 navigationStore 同步
    if (!currentMenuType.value && navigationStore.currentCategory) {
      console.warn('[切换分类] 同步 currentMenuType 从 navigationStore');
      currentMenuType.value = navigationStore.currentCategory;
    }

    // 如果当前分类不在当前数据源的分类列表中，使用最后一个分类
    const currentCategoryExists = menuCategories.value.some(c => c.type === currentMenuType.value);
    if (!currentCategoryExists && menuCategories.value.length > 0) {
      console.warn('[切换分类] 当前分类不存在于当前数据源，使用最后一个分类作为默认值');
      currentMenuType.value = menuCategories.value[menuCategories.value.length - 1].type;
    }

    // 设置冷却状态
    wheelCooldown.value = true;
    isChangingCategory.value = true;

    const idx = menuCategories.value.findIndex(c => c.type === currentMenuType.value)

    if (idx >= 0) {
      const prevIdx = (idx - 1 + menuCategories.value.length) % menuCategories.value.length // 循环切换
      const prevType = menuCategories.value[prevIdx].type

      // 获取app-grid元素并添加向下滑动动画
      const appGrid = document.querySelector('.app-grid');
      if (appGrid) {
        // 添加动画类
        appGrid.classList.add('slide-down-animation');

        // 动画结束后执行切换
        setTimeout(() => {
          // 移除动画类
          appGrid.classList.remove('slide-down-animation');

          // 切换分类
          window.dispatchEvent(new CustomEvent('menuTypeChanged', { detail: { type: prevType } }))

          scrollToTop()

          // 延长重置时间，确保切换完全完成
          setTimeout(() => {
            wheelCooldown.value = false;
            isChangingCategory.value = false;
          }, 300); // 增加延迟，确保切换完全完成
        }, 400); // 等待动画完成
      } else {
        // 如果找不到app-grid元素，直接切换
        window.dispatchEvent(new CustomEvent('menuTypeChanged', { detail: { type: prevType } }))

        scrollToTop()

        setTimeout(() => {
          wheelCooldown.value = false;
          isChangingCategory.value = false;
        }, 500);
      }
    } else {
      console.warn(`[切换分类] 无法找到当前分类 ${currentMenuType.value} 的索引，当前分类列表:`, menuCategories.value.map(c => c.type));
      console.warn(`[切换分类] 当前分类类型: ${currentMenuType.value}`);

      // 如果找不到当前分类，尝试使用最后一个分类
      if (menuCategories.value.length > 0) {
        const lastType = menuCategories.value[menuCategories.value.length - 1].type;
        console.warn(`[切换分类] 使用最后一个分类作为备选: ${lastType}`);
        currentMenuType.value = lastType;
        // 重新尝试切换
        setTimeout(() => {
          wheelCooldown.value = false;
          isChangingCategory.value = false;
          switchToPreviousCategory(currentMenuType, menuCategories, emitter);
        }, 100);
      } else {
        wheelCooldown.value = false;
        isChangingCategory.value = false;
      }
    }
  }

  /**
   * 显示滑动提示消息
   */
  function showSlideHintMessage(message) {
    showSlideHint.value = message
    // 清除之前的定时器
    if (slideHintTimer) {
      clearTimeout(slideHintTimer)
    }
    // 设置新定时器
    slideHintTimer = setTimeout(() => {
      showSlideHint.value = false
    }, 1500) // 略微减少显示时间
  }

  /**
   * 滚动到顶部
   */
  function scrollToTop() {
    const appGrid = document.querySelector('.app-grid')
    if (appGrid) {
      // 使用直接设置而非平滑滚动，提高速度
      appGrid.scrollTop = 0
    }
  }

  /**
   * 清理函数
   */
  function cleanup() {
    if (slideHintTimer) {
      clearTimeout(slideHintTimer)
      slideHintTimer = null
    }
  }

  /**
   * 简化的触摸移动处理
   */
  function handleTouchMove(e, touchStartY, touchStartX) {
    if (touchStartY === 0) {
      touchStartY = e.touches[0].clientY;
      touchStartX = e.touches[0].clientX;
    }
    return { touchStartY, touchStartX };
  }

  /**
   * 简化的触摸结束处理
   */
  function handleTouchEnd(e, touchStartY, touchStartX, wheelCooldown, isChangingCategory, 
                         switchToNextCategory, switchToPreviousCategory, currentMenuType, menuCategories) {
    if (touchStartY === 0) return;

    // 获取终点位置
    const touchEndY = e.changedTouches[0].clientY;
    const touchEndX = e.changedTouches[0].clientX;

    // 检查是否是垂直滑动
    if (Math.abs(touchEndY - touchStartY) > Math.abs(touchEndX - touchStartX)) {
      // 检查是否在需要排除的组件内触摸
      const shouldExclude = e.target.closest(
        // 弹窗和模态框
        '.app-modal-overlay, .app-modal, .hotnet-modal, .modal-list, ' +
        // 抽屉和侧边栏
        '.drawer-container, .drawer-content, .mobile-drawer, ' +
        // 有滚动条的组件
        '.hotnet-container, .video-card, [class*="card-component"], ' +
        '.danmaku-container, .tasks-container, .all-platforms-sidebar, ' +
        // Ant Design 组件
        '.ant-modal, .ant-drawer, .ant-select-dropdown, .ant-tooltip, ' +
        '.ant-popover, .ant-dropdown, .ant-menu, ' +
        // 其他可滚动容器
        '[data-scrollable], .scrollable, .overflow-auto, .overflow-y-auto'
      );

      if (shouldExclude) {
        // 在排除的组件内，不触发分类切换
        return { touchStartY: 0, touchStartX: 0 };
      }

      // 使用closest方法检测是否在应用区域内，与wheel事件保持一致
      const isInsideAppsSection = e.target.closest('.apps-section, .app-grid, .app-item') !== null;

      if (!isInsideAppsSection && !wheelCooldown.value && !isChangingCategory.value) {
        const TOUCH_THRESHOLD = 50; // 简化的触摸阈值

        if (Math.abs(touchEndY - touchStartY) > TOUCH_THRESHOLD) {
          if (touchEndY < touchStartY) {
            // 向上滑动，切换到下一分类
            switchToNextCategory(currentMenuType, menuCategories);
          } else if (touchEndY > touchStartY) {
            // 向下滑动，切换到上一分类
            switchToPreviousCategory(currentMenuType, menuCategories);
          }
        }
      }
    }

    // 重置触摸状态
    return { touchStartY: 0, touchStartX: 0 };
  }

  return {
    wheelCooldown,
    isChangingCategory,
    showSlideHint,
    lastScrollTime,
    SCROLL_COOLDOWN_TIME,
    switchToNextCategory,
    switchToPreviousCategory,
    showSlideHintMessage,
    scrollToTop,
    handleTouchMove,
    handleTouchEnd,
    cleanup
  }
}