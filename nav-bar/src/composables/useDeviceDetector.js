import { ref, computed, onMounted, onUnmounted } from 'vue'

// 设备类型检测相关状态
const isMobile = ref(false)
const isTablet = ref(false)
const isTouchDevice = ref(false)
const isLandscape = ref(false)

// 检测屏幕方向的函数
function detectOrientation() {
  // 方法1：使用媒体查询检测（最可靠）
  if (window.matchMedia) {
    const landscapeQuery = window.matchMedia('(orientation: landscape)')
    isLandscape.value = landscapeQuery.matches
    return
  }

  // 方法2：使用屏幕尺寸比例检测（备用方案）
  const width = window.innerWidth
  const height = window.innerHeight
  isLandscape.value = width > height
}

// 检测设备类型的函数（简洁的批量判断）
function detectDeviceType() {
  const userAgent = navigator.userAgent.toLowerCase()

  // 检测是否为触摸设备
  isTouchDevice.value = 'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    navigator.msMaxTouchPoints > 0

  // 设备检测规则配置
  const deviceRules = {
    mobile: [
      'iphone', 'ipod', 'android.*mobile', 'blackberry', 'bb10',
      'windows.*phone', 'wpdesktop', 'opera.*mini', 'opera.*mobi',
      'mobile', 'phone', 'webos', 'palm'
    ],
    tablet: [
      'ipad', 'android(?!.*mobile)', 'kindle', 'silk',
      'playbook', 'tablet', 'tab'
    ]
  }

  // 批量检测设备类型
  let deviceType = 'desktop'

  // 优先检测iPad（最高优先级）- 包括正常iPad和伪装成Mac的iPad
  if (deviceRules.tablet.some(pattern => new RegExp(pattern, 'i').test(userAgent))) {
    deviceType = 'tablet'
  }
  // 特殊检测：iPad在iPadOS 13+中会伪装成Mac，需要特殊处理
  else if (isTouchDevice.value &&
    userAgent.includes('macintosh') &&
    userAgent.includes('safari') &&
    navigator.maxTouchPoints > 0) {
    // 这很可能是iPad伪装成Mac的情况
    deviceType = 'tablet'
  }
  // 然后检测手机（iPad检测完成后再检测手机）
  else if (deviceRules.mobile.some(pattern => new RegExp(pattern, 'i').test(userAgent))) {
    deviceType = 'mobile'
  }
  // 其他触摸设备但没有明确标识
  else if (isTouchDevice.value && /touch/i.test(userAgent)) {
    deviceType = 'tablet'
  }

  // 设置设备状态
  isMobile.value = deviceType === 'mobile'
  isTablet.value = deviceType === 'tablet'

  // 同时检测屏幕方向
  detectOrientation()
}

// 兼容性函数：保持原有的checkIsMobile函数
function checkIsMobile() {
  detectDeviceType()
}

// 判断是否应该禁用拖拽（手机或平板）
const shouldDisableDrag = computed(() => isMobile.value || isTablet.value)

// 处理防抖的窗口大小变化事件
function debouncedHandleResize() {
  // 检测设备类型
  detectDeviceType();
}

// 组件挂载时的初始化
function initDeviceDetector() {
  // 初始检测
  detectDeviceType()
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', debouncedHandleResize)
  
  // 监听屏幕方向变化（兼容不同浏览器）
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.addEventListener('change', detectDeviceType)
  } else {
    // 备用方案：监听orientationchange事件
    window.addEventListener('orientationchange', () => {
      // 延迟执行，确保屏幕尺寸已更新
      setTimeout(detectDeviceType, 100)
    })
  }
}

// 组件卸载时的清理
function cleanupDeviceDetector() {
  window.removeEventListener('resize', debouncedHandleResize)
  
  // 清理屏幕方向变化监听器
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.removeEventListener('change', detectDeviceType)
  } else {
    window.removeEventListener('orientationchange', detectDeviceType)
  }
}

// 导出所有属性和方法
export function useDeviceDetector() {
  return {
    // 响应式属性
    isMobile,
    isTablet,
    isTouchDevice,
    isLandscape,
    
    // 计算属性
    shouldDisableDrag,
    
    // 方法
    detectOrientation,
    detectDeviceType,
    checkIsMobile,
    debouncedHandleResize,
    initDeviceDetector,
    cleanupDeviceDetector
  }
}